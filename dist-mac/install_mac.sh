#!/bin/bash
# Ubuntu Remote Control Mac M2 安装脚本

echo "安装 Ubuntu Remote Control (Mac M2 版本)..."

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "arm64" ]; then
    echo "警告: 当前架构是 $ARCH，此程序为 arm64 (Mac M2) 构建"
fi

# 创建用户bin目录
mkdir -p ~/.local/bin

# 复制二进制文件
if [ -f "controller" ]; then
    cp controller ~/.local/bin/
    chmod +x ~/.local/bin/controller
    echo "✓ Controller已安装到 ~/.local/bin/controller"
fi

if [ -f "agent" ]; then
    cp agent ~/.local/bin/
    chmod +x ~/.local/bin/agent
    echo "✓ Agent已安装到 ~/.local/bin/agent"
fi

# 检查PATH
if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
    echo ""
    echo "注意: ~/.local/bin 不在您的PATH中"
    echo "请将以下行添加到 ~/.zshrc 或 ~/.bash_profile:"
    echo "  export PATH=\"\$HOME/.local/bin:\$PATH\""
    echo "然后运行: source ~/.zshrc"
fi

echo ""
echo "安装完成！"
echo "使用方法:"
echo "  控制端: ~/.local/bin/controller --help"
echo "  受控端: ~/.local/bin/agent --help"
echo ""
echo "或者如果PATH已配置:"
echo "  控制端: controller --help"
echo "  受控端: agent --help"
