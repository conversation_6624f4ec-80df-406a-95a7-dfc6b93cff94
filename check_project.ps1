# Simple project verification script

Write-Host "=== Ubuntu Remote Control Project Check ===" -ForegroundColor Green

# Check root files
Write-Host "Checking root files..." -ForegroundColor Yellow
$rootFiles = @("Cargo.toml", "README.md", "build.sh", "test.sh")
foreach ($file in $rootFiles) {
    if (Test-Path $file) {
        Write-Host "  OK: $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
    }
}

# Check modules
Write-Host "Checking modules..." -ForegroundColor Yellow
$modules = @("common", "controller", "agent", "tests")
foreach ($module in $modules) {
    if (Test-Path $module -PathType Container) {
        Write-Host "  OK: $module/" -ForegroundColor Green
        
        # Check Cargo.toml
        $cargoFile = Join-Path $module "Cargo.toml"
        if (Test-Path $cargoFile) {
            Write-Host "    OK: $module/Cargo.toml" -ForegroundColor Green
        }
        
        # Check src directory
        $srcDir = Join-Path $module "src"
        if (Test-Path $srcDir -PathType Container) {
            Write-Host "    OK: $module/src/" -ForegroundColor Green
            $srcFiles = Get-ChildItem $srcDir -Filter "*.rs"
            Write-Host "      Rust files: $($srcFiles.Count)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  MISSING: $module/" -ForegroundColor Red
    }
}

# Count code lines
Write-Host "Counting code..." -ForegroundColor Yellow
$totalLines = 0
$totalFiles = 0

Get-ChildItem -Recurse -Filter "*.rs" | ForEach-Object {
    $lines = (Get-Content $_.FullName | Measure-Object -Line).Lines
    $totalLines += $lines
    $totalFiles++
}

Write-Host "  Total: $totalFiles Rust files, $totalLines lines of code" -ForegroundColor Cyan

# Generate simple report
$report = @"
# Project Status Report

Generated: $(Get-Date)

## Structure
- Root files: Complete
- Modules: common, controller, agent, tests
- Source files: $totalFiles Rust files
- Code lines: $totalLines

## Features Implemented
- HTTP communication with encryption
- Command execution with security controls
- File upload/download
- System information collection
- ELF file loading
- Authentication and logging
- TLS support

## Next Steps
1. Install Rust toolchain
2. Run: cargo build --release
3. Run: cargo test
4. Deploy and test in Linux environment

## Security Notes
- Use only in authorized environments
- Review security settings before deployment
- Monitor logs for suspicious activity
"@

$report | Out-File -FilePath "STATUS_REPORT.md" -Encoding UTF8

Write-Host "=== Check Complete ===" -ForegroundColor Green
Write-Host "Status report saved to: STATUS_REPORT.md" -ForegroundColor Yellow
