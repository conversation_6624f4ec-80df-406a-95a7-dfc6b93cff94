use std::time::Duration;
use tokio::time::sleep;
use common::{
    HttpCommunicator, Message, MessageType, CommandMessage, CommandResult,
    CryptoM<PERSON><PERSON>, <PERSON><PERSON>, AuthManager, Permission
};

#[tokio::test]
async fn test_end_to_end_communication() {
    // 初始化加密密钥
    let crypto_key = CryptoManager::generate_key();
    
    // 创建通信器
    let server_url = "http://localhost:8080".to_string();
    let communicator = HttpCommunicator::new(server_url, &crypto_key).unwrap();
    
    // 创建测试消息
    let cmd_msg = CommandMessage {
        command: "echo".to_string(),
        args: vec!["hello".to_string()],
        working_dir: None,
        timeout: Some(30),
    };
    
    let message = Message::new(MessageType::Command(cmd_msg));
    
    // 验证消息完整性
    assert!(message.verify_checksum());
}

#[tokio::test]
async fn test_crypto_operations() {
    let key = CryptoManager::generate_key();
    let crypto = CryptoManager::new(&key).unwrap();
    
    let test_data = b"This is a test message for encryption";
    
    // 加密
    let encrypted = crypto.encrypt(test_data).unwrap();
    assert_ne!(encrypted, test_data);
    
    // 解密
    let decrypted = crypto.decrypt(&encrypted).unwrap();
    assert_eq!(decrypted, test_data);
    
    // 测试哈希
    let hash1 = CryptoManager::hash_data(test_data);
    let hash2 = CryptoManager::hash_data(test_data);
    assert_eq!(hash1, hash2);
    assert_eq!(hash1.len(), 64); // SHA256 hex string
}

#[tokio::test]
async fn test_authentication_flow() {
    let crypto_key = CryptoManager::generate_key();
    let mut auth_manager = AuthManager::new(&crypto_key, 3600).unwrap();
    
    // 创建token
    let permissions = vec![Permission::CommandExecution, Permission::FileUpload];
    let token = auth_manager.create_token("test_user".to_string(), permissions.clone()).unwrap();
    
    // 验证token
    let verified_token = auth_manager.verify_token(&token).unwrap();
    assert_eq!(verified_token.user_id, "test_user");
    assert_eq!(verified_token.permissions, permissions);
    
    // 检查权限
    assert!(auth_manager.check_permission(&verified_token, &Permission::CommandExecution));
    assert!(auth_manager.check_permission(&verified_token, &Permission::FileUpload));
    assert!(!auth_manager.check_permission(&verified_token, &Permission::AdminAccess));
    
    // 撤销token
    auth_manager.revoke_token(&token);
    
    // 清理过期token
    auth_manager.cleanup_expired_tokens().unwrap();
}

#[tokio::test]
async fn test_logging_system() {
    use tempfile::NamedTempFile;
    
    let temp_file = NamedTempFile::new().unwrap();
    let logger = Logger::new(temp_file.path().to_string_lossy().to_string());
    
    // 测试各种日志级别
    logger.log_authentication("test_user", true, None).unwrap();
    logger.log_command("test_user", uuid::Uuid::new_v4(), "ls -la", true).unwrap();
    logger.log_file_operation("test_user", uuid::Uuid::new_v4(), "upload", "/tmp/test.txt", true).unwrap();
    logger.log_security_event("Suspicious activity detected", None).unwrap();
    logger.log_error("Test error message", None).unwrap();
    
    // 验证日志文件内容
    let log_content = std::fs::read_to_string(temp_file.path()).unwrap();
    assert!(log_content.contains("test_user"));
    assert!(log_content.contains("Authentication"));
    assert!(log_content.contains("Command"));
    assert!(log_content.contains("FileOperation"));
    assert!(log_content.contains("Security"));
    assert!(log_content.contains("Error"));
}

#[tokio::test]
async fn test_message_protocol() {
    use common::{SystemInfoResult, DiskInfo, NetworkInterface};
    
    // 测试不同类型的消息
    let system_info = SystemInfoResult {
        hostname: "test-host".to_string(),
        os_version: "Ubuntu 22.04 LTS".to_string(),
        kernel_version: "5.15.0".to_string(),
        architecture: "x86_64".to_string(),
        cpu_info: "Intel Core i7".to_string(),
        memory_total: 8 * 1024 * 1024 * 1024, // 8GB
        memory_available: 4 * 1024 * 1024 * 1024, // 4GB
        disk_info: vec![
            DiskInfo {
                device: "/dev/sda1".to_string(),
                mount_point: "/".to_string(),
                total_space: 100 * 1024 * 1024 * 1024, // 100GB
                available_space: 50 * 1024 * 1024 * 1024, // 50GB
                filesystem: "ext4".to_string(),
            }
        ],
        network_interfaces: vec![
            NetworkInterface {
                name: "eth0".to_string(),
                ip_addresses: vec!["*************".to_string()],
                mac_address: "00:11:22:33:44:55".to_string(),
                is_up: true,
            }
        ],
    };
    
    let message = Message::new(MessageType::SystemInfoResult(system_info));
    
    // 验证消息序列化和反序列化
    let serialized = serde_json::to_vec(&message).unwrap();
    let deserialized: Message = serde_json::from_slice(&serialized).unwrap();
    
    assert_eq!(message.id, deserialized.id);
    assert_eq!(message.timestamp, deserialized.timestamp);
    assert!(message.verify_checksum());
    assert!(deserialized.verify_checksum());
}

#[tokio::test]
async fn test_file_operations() {
    use common::FileUploadMessage;
    use tempfile::TempDir;
    
    let temp_dir = TempDir::new().unwrap();
    let test_file_path = temp_dir.path().join("test_upload.txt");
    let test_content = b"This is test file content for upload testing";
    
    // 模拟文件上传
    let chunk_size = 16; // 小chunk用于测试
    let total_chunks = (test_content.len() + chunk_size - 1) / chunk_size;
    
    for (chunk_index, chunk) in test_content.chunks(chunk_size).enumerate() {
        let upload_msg = FileUploadMessage {
            file_path: test_file_path.to_string_lossy().to_string(),
            file_size: test_content.len() as u64,
            chunk_index: chunk_index as u32,
            total_chunks: total_chunks as u32,
            data: chunk.to_vec(),
        };
        
        let message = Message::new(MessageType::FileUpload(upload_msg));
        assert!(message.verify_checksum());
    }
}

#[tokio::test]
async fn test_command_execution_security() {
    use agent::executor::CommandExecutor;
    
    let executor = CommandExecutor::new();
    
    // 测试允许的命令
    let result = executor.execute_command("echo", &["hello".to_string()], None, None).await.unwrap();
    assert_eq!(result.exit_code, 0);
    assert_eq!(result.stdout.trim(), "hello");
    
    // 测试被禁止的命令
    let result = executor.execute_command("rm", &["-rf".to_string(), "/".to_string()], None, None).await.unwrap();
    assert_eq!(result.exit_code, -1);
    assert!(result.stderr.contains("not allowed"));
    
    // 测试超时
    let result = executor.execute_command("sleep", &["1".to_string()], None, Some(1)).await.unwrap();
    // 应该在超时前完成或者被终止
}

#[tokio::test]
async fn test_system_info_collection() {
    use agent::system_info::SystemInfoCollector;
    
    let collector = SystemInfoCollector::new();
    let info = collector.collect().await.unwrap();
    
    // 验证基本信息
    assert!(!info.hostname.is_empty());
    assert!(!info.os_version.is_empty());
    assert!(!info.kernel_version.is_empty());
    assert!(!info.architecture.is_empty());
    assert!(info.memory_total > 0);
    assert!(info.memory_available <= info.memory_total);
    
    // 验证网络接口信息
    assert!(!info.network_interfaces.is_empty());
    for interface in &info.network_interfaces {
        assert!(!interface.name.is_empty());
        if interface.is_up {
            assert!(!interface.ip_addresses.is_empty());
        }
    }
}

#[tokio::test]
async fn test_elf_loader_security() {
    use agent::elf_loader::ElfLoader;
    
    let loader = ElfLoader::new();
    
    // 测试无效的ELF文件
    let invalid_elf = b"This is not an ELF file";
    let result = loader.load_and_execute(invalid_elf, &[]).await;
    assert!(result.is_err());
    
    // 测试ELF头验证
    let invalid_header = [0x00, 0x00, 0x00, 0x00]; // 无效魔数
    assert!(loader.validate_elf_header(&invalid_header).is_err());
    
    // 测试有效的ELF头（简化）
    let valid_header = [
        0x7f, 0x45, 0x4c, 0x46, // ELF魔数
        0x02, // 64位
        0x01, // 小端
        0x01, // 版本
        0x00, // System V ABI
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 填充
        0x02, 0x00, // 可执行文件
        0x3e, 0x00, // x86-64
    ];
    assert!(loader.validate_elf_header(&valid_header).is_ok());
}

#[tokio::test]
async fn test_concurrent_operations() {
    use std::sync::Arc;
    use tokio::sync::Mutex;
    
    let crypto_key = CryptoManager::generate_key();
    let auth_manager = Arc::new(Mutex::new(AuthManager::new(&crypto_key, 3600).unwrap()));
    
    // 并发创建多个token
    let mut handles = vec![];
    for i in 0..10 {
        let auth_manager_clone = auth_manager.clone();
        let handle = tokio::spawn(async move {
            let mut auth = auth_manager_clone.lock().await;
            auth.create_token(
                format!("user_{}", i),
                vec![Permission::CommandExecution]
            )
        });
        handles.push(handle);
    }
    
    // 等待所有任务完成
    let mut tokens = vec![];
    for handle in handles {
        let token = handle.await.unwrap().unwrap();
        tokens.push(token);
    }
    
    assert_eq!(tokens.len(), 10);
    
    // 验证所有token
    let auth = auth_manager.lock().await;
    for token in tokens {
        assert!(auth.verify_token(&token).is_ok());
    }
}

#[tokio::test]
async fn test_error_handling() {
    // 测试各种错误情况
    
    // 无效的加密密钥
    let result = CryptoManager::new(&[]);
    assert!(result.is_ok()); // 应该使用哈希处理短密钥
    
    // 无效的服务器URL
    let result = HttpCommunicator::new("invalid-url".to_string(), &[1, 2, 3, 4]);
    assert!(result.is_ok()); // URL验证在实际请求时进行
    
    // 无效的日志路径
    let logger = Logger::new("/invalid/path/log.txt".to_string());
    let result = logger.ensure_log_directory();
    // 在某些系统上可能失败，这是预期的
}

// 性能测试
#[tokio::test]
async fn test_performance() {
    use std::time::Instant;
    
    let crypto_key = CryptoManager::generate_key();
    let crypto = CryptoManager::new(&crypto_key).unwrap();
    
    let test_data = vec![0u8; 1024 * 1024]; // 1MB数据
    
    // 测试加密性能
    let start = Instant::now();
    let encrypted = crypto.encrypt(&test_data).unwrap();
    let encrypt_time = start.elapsed();
    
    // 测试解密性能
    let start = Instant::now();
    let decrypted = crypto.decrypt(&encrypted).unwrap();
    let decrypt_time = start.elapsed();
    
    assert_eq!(test_data, decrypted);
    
    println!("Encryption time for 1MB: {:?}", encrypt_time);
    println!("Decryption time for 1MB: {:?}", decrypt_time);
    
    // 性能应该在合理范围内（这些值可能需要根据实际硬件调整）
    assert!(encrypt_time.as_millis() < 1000); // 小于1秒
    assert!(decrypt_time.as_millis() < 1000); // 小于1秒
}
