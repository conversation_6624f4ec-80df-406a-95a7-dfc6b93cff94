# Linux AMD64 交叉编译指南

本指南说明如何在 macOS 上为 Linux AMD64 平台构建您的 Rust 项目。

## 构建选项

我为您创建了两个构建脚本：

### 1. 标准 Linux 构建 (`build_linux.sh`)
- 目标平台：`x86_64-unknown-linux-gnu`
- 动态链接，依赖系统库
- 文件较小，但需要目标系统有相应的库

### 2. 静态链接构建 (`build_linux_static.sh`) **推荐**
- 目标平台：`x86_64-unknown-linux-musl`
- 静态链接，无外部依赖
- 文件较大，但可在任何 Linux 发行版运行

## 快速开始

### 步骤 1：准备环境
确保您已安装 Rust：
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 步骤 2：选择构建方式

#### 推荐：静态链接构建
```bash
./build_linux_static.sh
```

#### 或者：标准构建
```bash
./build_linux.sh
```

### 步骤 3：获取构建结果

构建完成后，您将得到：

**静态链接版本：**
- `target/x86_64-unknown-linux-musl/release/controller`
- `target/x86_64-unknown-linux-musl/release/agent`
- `dist-linux-static/` 目录（包含部署包）

**标准版本：**
- `target/x86_64-unknown-linux-gnu/release/controller`
- `target/x86_64-unknown-linux-gnu/release/agent`
- `dist-linux/` 目录（包含部署包）

## 部署到 Linux 服务器

### 方法 1：直接复制二进制文件
```bash
# 复制到 Linux 服务器
scp target/x86_64-unknown-linux-musl/release/controller user@linux-server:/tmp/
scp target/x86_64-unknown-linux-musl/release/agent user@linux-server:/tmp/

# 在 Linux 服务器上
chmod +x /tmp/controller /tmp/agent
sudo mv /tmp/controller /tmp/agent /usr/local/bin/
```

### 方法 2：使用部署包
```bash
# 创建分发包
cd dist-linux-static
./package.sh

# 复制到 Linux 服务器
scp ubuntu-remote-control-linux-amd64-static.tar.gz user@linux-server:/tmp/

# 在 Linux 服务器上解压并安装
cd /tmp
tar -xzf ubuntu-remote-control-linux-amd64-static.tar.gz
cd ubuntu-remote-control-linux-amd64-static

# 系统安装（需要 root 权限）
sudo ./install.sh

# 或用户安装（无需 root 权限）
./install_user.sh
```

## 验证构建结果

在 macOS 上验证生成的二进制文件：
```bash
# 检查文件类型
file target/x86_64-unknown-linux-musl/release/controller
file target/x86_64-unknown-linux-musl/release/agent

# 应该显示类似：
# controller: ELF 64-bit LSB executable, x86-64, version 1 (SYSV), statically linked, stripped
```

在 Linux 服务器上测试：
```bash
# 检查程序是否能运行
./controller --help
./agent --help
```

## 故障排除

### 问题 1：缺少目标平台
```bash
# 手动安装目标平台
rustup target add x86_64-unknown-linux-musl
```

### 问题 2：构建失败
```bash
# 清理并重试
cargo clean
./build_linux_static.sh
```

### 问题 3：在 Linux 上无法运行
- 检查架构：`uname -m` 应该显示 `x86_64`
- 检查权限：`chmod +x controller agent`
- 对于静态版本，通常不会有依赖问题

## 文件大小对比

- 静态链接版本：较大（~10-20MB），但无依赖
- 动态链接版本：较小（~5-10MB），但需要系统库

## 建议

1. **推荐使用静态链接版本** (`build_linux_static.sh`)，因为：
   - 部署简单，无需考虑依赖
   - 可在任何 Linux 发行版运行
   - 避免库版本冲突

2. **测试流程**：
   - 在本地构建
   - 在目标 Linux 环境测试
   - 确认功能正常后部署

3. **安全考虑**：
   - 在生产环境部署前进行充分测试
   - 考虑使用容器化部署
   - 定期更新依赖库

## 自动化构建

您可以将构建过程集成到 CI/CD 流水线中：
```bash
# 在 GitHub Actions 或其他 CI 中
./build_linux_static.sh
# 上传构建产物
```

这样就可以在 macOS 上为 Linux AMD64 平台构建您的 Rust 项目了！
