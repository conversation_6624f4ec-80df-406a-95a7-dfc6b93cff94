use anyhow::{Result, anyhow};
use rustls::{ClientConfig, ServerConfig, Certificate, PrivateKey};
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::fs::File;
use std::io::BufReader;
use std::sync::Arc;

pub struct TlsConfig {
    pub client_config: Option<Arc<ClientConfig>>,
    pub server_config: Option<Arc<ServerConfig>>,
}

impl TlsConfig {
    pub fn new() -> Self {
        Self {
            client_config: None,
            server_config: None,
        }
    }

    pub fn with_client_config(mut self, config: Arc<ClientConfig>) -> Self {
        self.client_config = Some(config);
        self
    }

    pub fn with_server_config(mut self, config: Arc<ServerConfig>) -> Self {
        self.server_config = Some(config);
        self
    }

    pub fn create_client_config() -> Result<Arc<ClientConfig>> {
        let mut root_store = rustls::RootCertStore::empty();
        
        // 添加系统根证书
        root_store.add_server_trust_anchors(
            webpki_roots::TLS_SERVER_ROOTS.0.iter().map(|ta| {
                rustls::OwnedTrustAnchor::from_subject_spki_name_constraints(
                    ta.subject,
                    ta.spki,
                    ta.name_constraints,
                )
            })
        );

        let config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(root_store)
            .with_no_client_auth();

        Ok(Arc::new(config))
    }

    pub fn create_client_config_with_cert(cert_path: &str, key_path: &str) -> Result<Arc<ClientConfig>> {
        let mut root_store = rustls::RootCertStore::empty();
        
        // 添加系统根证书
        root_store.add_server_trust_anchors(
            webpki_roots::TLS_SERVER_ROOTS.0.iter().map(|ta| {
                rustls::OwnedTrustAnchor::from_subject_spki_name_constraints(
                    ta.subject,
                    ta.spki,
                    ta.name_constraints,
                )
            })
        );

        // 加载客户端证书和私钥
        let cert_file = File::open(cert_path)?;
        let mut cert_reader = BufReader::new(cert_file);
        let cert_chain = certs(&mut cert_reader)?
            .into_iter()
            .map(Certificate)
            .collect();

        let key_file = File::open(key_path)?;
        let mut key_reader = BufReader::new(key_file);
        let mut keys = pkcs8_private_keys(&mut key_reader)?;
        
        if keys.is_empty() {
            return Err(anyhow!("No private keys found"));
        }
        
        let key = PrivateKey(keys.remove(0));

        let config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(root_store)
            .with_single_cert(cert_chain, key)?;

        Ok(Arc::new(config))
    }

    pub fn create_server_config(cert_path: &str, key_path: &str) -> Result<Arc<ServerConfig>> {
        // 加载服务器证书和私钥
        let cert_file = File::open(cert_path)?;
        let mut cert_reader = BufReader::new(cert_file);
        let cert_chain = certs(&mut cert_reader)?
            .into_iter()
            .map(Certificate)
            .collect();

        let key_file = File::open(key_path)?;
        let mut key_reader = BufReader::new(key_file);
        let mut keys = pkcs8_private_keys(&mut key_reader)?;
        
        if keys.is_empty() {
            return Err(anyhow!("No private keys found"));
        }
        
        let key = PrivateKey(keys.remove(0));

        let config = ServerConfig::builder()
            .with_safe_defaults()
            .with_no_client_auth()
            .with_single_cert(cert_chain, key)?;

        Ok(Arc::new(config))
    }

    pub fn create_self_signed_cert() -> Result<(Vec<u8>, Vec<u8>)> {
        // 生成自签名证书（用于测试）
        use rcgen::{Certificate as RcgenCert, CertificateParams, DistinguishedName};
        
        let mut params = CertificateParams::new(vec!["localhost".to_string()]);
        params.distinguished_name = DistinguishedName::new();
        params.distinguished_name.push(rcgen::DnType::CommonName, "Remote Control Server");
        params.distinguished_name.push(rcgen::DnType::OrganizationName, "Remote Control");
        
        let cert = RcgenCert::from_params(params)?;
        let cert_pem = cert.serialize_pem()?;
        let key_pem = cert.serialize_private_key_pem();
        
        Ok((cert_pem.into_bytes(), key_pem.into_bytes()))
    }

    pub fn verify_certificate_chain(cert_chain: &[Certificate]) -> Result<()> {
        if cert_chain.is_empty() {
            return Err(anyhow!("Empty certificate chain"));
        }

        // 这里可以添加更多的证书验证逻辑
        // 例如：检查证书有效期、吊销状态等
        
        Ok(())
    }

    pub fn get_certificate_info(cert: &Certificate) -> Result<CertificateInfo> {
        use x509_parser::prelude::*;
        
        let (_, parsed_cert) = X509Certificate::from_der(&cert.0)?;
        
        Ok(CertificateInfo {
            subject: parsed_cert.subject().to_string(),
            issuer: parsed_cert.issuer().to_string(),
            serial_number: format!("{:x}", parsed_cert.serial),
            not_before: parsed_cert.validity().not_before.timestamp(),
            not_after: parsed_cert.validity().not_after.timestamp(),
            fingerprint: Self::calculate_fingerprint(&cert.0),
        })
    }

    fn calculate_fingerprint(cert_der: &[u8]) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(cert_der);
        format!("{:x}", hasher.finalize())
    }
}

#[derive(Debug, Clone)]
pub struct CertificateInfo {
    pub subject: String,
    pub issuer: String,
    pub serial_number: String,
    pub not_before: i64,
    pub not_after: i64,
    pub fingerprint: String,
}

pub struct TlsManager {
    config: TlsConfig,
}

impl TlsManager {
    pub fn new(config: TlsConfig) -> Self {
        Self { config }
    }

    pub fn get_client_config(&self) -> Option<&Arc<ClientConfig>> {
        self.config.client_config.as_ref()
    }

    pub fn get_server_config(&self) -> Option<&Arc<ServerConfig>> {
        self.config.server_config.as_ref()
    }

    pub async fn create_secure_client(&self, server_name: &str) -> Result<reqwest::Client> {
        if let Some(client_config) = &self.config.client_config {
            let client = reqwest::Client::builder()
                .use_preconfigured_tls(client_config.clone())
                .build()?;
            Ok(client)
        } else {
            // 使用默认TLS配置
            let client = reqwest::Client::builder()
                .https_only(true)
                .build()?;
            Ok(client)
        }
    }

    pub fn validate_server_certificate(&self, cert_chain: &[Certificate], server_name: &str) -> Result<()> {
        // 验证证书链
        TlsConfig::verify_certificate_chain(cert_chain)?;
        
        // 验证服务器名称
        if let Ok(cert_info) = TlsConfig::get_certificate_info(&cert_chain[0]) {
            if !cert_info.subject.contains(server_name) {
                return Err(anyhow!("Server name mismatch"));
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_self_signed_cert_generation() {
        let result = TlsConfig::create_self_signed_cert();
        assert!(result.is_ok());
        
        let (cert_pem, key_pem) = result.unwrap();
        assert!(!cert_pem.is_empty());
        assert!(!key_pem.is_empty());
        
        // 验证PEM格式
        assert!(String::from_utf8_lossy(&cert_pem).contains("-----BEGIN CERTIFICATE-----"));
        assert!(String::from_utf8_lossy(&key_pem).contains("-----BEGIN PRIVATE KEY-----"));
    }

    #[test]
    fn test_client_config_creation() {
        let result = TlsConfig::create_client_config();
        assert!(result.is_ok());
    }
}
