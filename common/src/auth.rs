use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::crypto::CryptoManager;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthToken {
    pub user_id: String,
    pub session_id: Uuid,
    pub issued_at: u64,
    pub expires_at: u64,
    pub permissions: Vec<Permission>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum Permission {
    CommandExecution,
    FileUpload,
    FileDownload,
    ProcessManagement,
    SystemInfo,
    AdminAccess,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct AuthManager {
    crypto: CryptoManager,
    active_tokens: HashMap<String, AuthToken>,
    token_lifetime: u64, // 秒
}

impl AuthManager {
    pub fn new(crypto_key: &[u8], token_lifetime: u64) -> Result<Self> {
        Ok(Self {
            crypto: CryptoManager::new(crypto_key)?,
            active_tokens: HashMap::new(),
            token_lifetime,
        })
    }

    pub fn create_token(&mut self, user_id: String, permissions: Vec<Permission>) -> Result<String> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();

        let token = AuthToken {
            user_id: user_id.clone(),
            session_id: Uuid::new_v4(),
            issued_at: now,
            expires_at: now + self.token_lifetime,
            permissions,
        };

        // 序列化并加密token
        let serialized = serde_json::to_vec(&token)?;
        let encrypted = self.crypto.encrypt(&serialized)?;
        let token_string = base64::encode(&encrypted);

        // 存储活跃token
        self.active_tokens.insert(token_string.clone(), token);

        Ok(token_string)
    }

    pub fn verify_token(&self, token_string: &str) -> Result<AuthToken> {
        // 从活跃token中查找
        if let Some(token) = self.active_tokens.get(token_string) {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)?
                .as_secs();

            if token.expires_at > now {
                return Ok(token.clone());
            }
        }

        // 尝试解密和验证token
        let encrypted = base64::decode(token_string)
            .map_err(|_| anyhow!("Invalid token format"))?;
        
        let decrypted = self.crypto.decrypt(&encrypted)?;
        let token: AuthToken = serde_json::from_slice(&decrypted)?;

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();

        if token.expires_at <= now {
            return Err(anyhow!("Token expired"));
        }

        Ok(token)
    }

    pub fn revoke_token(&mut self, token_string: &str) {
        self.active_tokens.remove(token_string);
    }

    pub fn cleanup_expired_tokens(&mut self) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();

        self.active_tokens.retain(|_, token| token.expires_at > now);
        Ok(())
    }

    pub fn check_permission(&self, token: &AuthToken, required_permission: &Permission) -> bool {
        token.permissions.contains(required_permission) || 
        token.permissions.contains(&Permission::AdminAccess)
    }

    pub fn get_active_sessions(&self) -> Vec<(String, AuthToken)> {
        self.active_tokens.iter()
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect()
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
    pub client_info: ClientInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ClientInfo {
    pub hostname: String,
    pub os_version: String,
    pub client_version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub success: bool,
    pub token: Option<String>,
    pub error_message: Option<String>,
    pub permissions: Vec<Permission>,
}

pub fn authenticate_user(username: &str, password: &str) -> Result<Vec<Permission>> {
    // 简单的硬编码认证（生产环境应使用数据库和哈希密码）
    match (username, password) {
        ("admin", "admin123") => Ok(vec![Permission::AdminAccess]),
        ("operator", "op123") => Ok(vec![
            Permission::CommandExecution,
            Permission::FileUpload,
            Permission::FileDownload,
            Permission::ProcessManagement,
            Permission::SystemInfo,
        ]),
        ("readonly", "ro123") => Ok(vec![
            Permission::SystemInfo,
        ]),
        _ => Err(anyhow!("Invalid credentials")),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_creation_and_verification() {
        let key = CryptoManager::generate_key();
        let mut auth_manager = AuthManager::new(&key, 3600).unwrap();
        
        let permissions = vec![Permission::CommandExecution, Permission::FileUpload];
        let token = auth_manager.create_token("test_user".to_string(), permissions.clone()).unwrap();
        
        let verified_token = auth_manager.verify_token(&token).unwrap();
        assert_eq!(verified_token.user_id, "test_user");
        assert_eq!(verified_token.permissions, permissions);
    }

    #[test]
    fn test_permission_check() {
        let key = CryptoManager::generate_key();
        let auth_manager = AuthManager::new(&key, 3600).unwrap();
        
        let token = AuthToken {
            user_id: "test".to_string(),
            session_id: Uuid::new_v4(),
            issued_at: 0,
            expires_at: u64::MAX,
            permissions: vec![Permission::CommandExecution],
        };
        
        assert!(auth_manager.check_permission(&token, &Permission::CommandExecution));
        assert!(!auth_manager.check_permission(&token, &Permission::FileUpload));
    }
}
