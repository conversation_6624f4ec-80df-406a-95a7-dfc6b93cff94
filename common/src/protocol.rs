use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sha2::{Sha256, Digest};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub timestamp: DateTime<Utc>,
    pub message_type: MessageType,
    pub payload: Vec<u8>,
    pub checksum: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum MessageType {
    // 控制端发送的消息
    Command(CommandMessage),
    FileUpload(FileUploadMessage),
    FileDownload(FileDownloadMessage),
    ProcessManagement(ProcessMessage),
    SystemInfo,
    
    // 受控端发送的消息
    CommandResult(CommandResult),
    FileData(FileData),
    ProcessInfo(ProcessInfo),
    SystemInfoResult(SystemInfoResult),
    Heartbeat,
    
    // 通用消息
    Error(ErrorMessage),
    Ack,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CommandMessage {
    pub command: String,
    pub args: Vec<String>,
    pub working_dir: Option<String>,
    pub timeout: Option<u64>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CommandResult {
    pub exit_code: i32,
    pub stdout: String,
    pub stderr: String,
    pub execution_time: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileUploadMessage {
    pub file_path: String,
    pub file_size: u64,
    pub chunk_index: u32,
    pub total_chunks: u32,
    pub data: Vec<u8>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileDownloadMessage {
    pub file_path: String,
    pub chunk_size: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileData {
    pub file_path: String,
    pub chunk_index: u32,
    pub total_chunks: u32,
    pub data: Vec<u8>,
    pub is_complete: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessMessage {
    pub action: ProcessAction,
    pub process_id: Option<u32>,
    pub process_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessAction {
    List,
    Kill,
    Start(String, Vec<String>),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessInfo {
    pub processes: Vec<ProcessDetails>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessDetails {
    pub pid: u32,
    pub name: String,
    pub cpu_usage: f32,
    pub memory_usage: u64,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfoResult {
    pub hostname: String,
    pub os_version: String,
    pub kernel_version: String,
    pub architecture: String,
    pub cpu_info: String,
    pub memory_total: u64,
    pub memory_available: u64,
    pub disk_info: Vec<DiskInfo>,
    pub network_interfaces: Vec<NetworkInterface>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskInfo {
    pub device: String,
    pub mount_point: String,
    pub total_space: u64,
    pub available_space: u64,
    pub filesystem: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterface {
    pub name: String,
    pub ip_addresses: Vec<String>,
    pub mac_address: String,
    pub is_up: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorMessage {
    pub error_code: u32,
    pub error_message: String,
    pub details: Option<String>,
}

impl Message {
    pub fn new(message_type: MessageType) -> Self {
        let payload = serde_json::to_vec(&message_type).unwrap_or_default();
        let checksum = format!("{:x}", Sha256::digest(&payload));
        
        Self {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            message_type,
            payload,
            checksum,
        }
    }
    
    pub fn verify_checksum(&self) -> bool {
        let expected = format!("{:x}", Sha256::digest(&self.payload));
        self.checksum == expected
    }
}
