use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{A<PERSON>, NewAead}};
use anyhow::{Result, anyhow};
use rand::{RngCore, thread_rng};
use sha2::{Sha256, Digest};

pub struct CryptoManager {
    cipher: Aes256Gcm,
}

impl CryptoManager {
    pub fn new(key: &[u8]) -> Result<Self> {
        // 使用SHA256哈希来确保密钥长度为32字节
        let mut hasher = Sha256::new();
        hasher.update(key);
        let key_hash = hasher.finalize();
        
        let key = Key::from_slice(&key_hash);
        let cipher = Aes256Gcm::new(key);
        
        Ok(Self { cipher })
    }

    pub fn encrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        // 生成随机nonce
        let mut nonce_bytes = [0u8; 12];
        thread_rng().fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        // 加密数据
        let ciphertext = self.cipher
            .encrypt(nonce, data)
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // 将nonce和密文组合
        let mut result = Vec::with_capacity(12 + ciphertext.len());
        result.extend_from_slice(&nonce_bytes);
        result.extend_from_slice(&ciphertext);
        
        Ok(result)
    }

    pub fn decrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        if data.len() < 12 {
            return Err(anyhow!("Invalid encrypted data length"));
        }

        // 分离nonce和密文
        let (nonce_bytes, ciphertext) = data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        // 解密数据
        let plaintext = self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        Ok(plaintext)
    }

    pub fn generate_key() -> Vec<u8> {
        let mut key = vec![0u8; 32];
        thread_rng().fill_bytes(&mut key);
        key
    }

    pub fn hash_data(data: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(data);
        format!("{:x}", hasher.finalize())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encrypt_decrypt() {
        let key = CryptoManager::generate_key();
        let crypto = CryptoManager::new(&key).unwrap();
        
        let data = b"Hello, World!";
        let encrypted = crypto.encrypt(data).unwrap();
        let decrypted = crypto.decrypt(&encrypted).unwrap();
        
        assert_eq!(data, decrypted.as_slice());
    }

    #[test]
    fn test_hash_data() {
        let data = b"test data";
        let hash1 = CryptoManager::hash_data(data);
        let hash2 = CryptoManager::hash_data(data);
        
        assert_eq!(hash1, hash2);
        assert_eq!(hash1.len(), 64); // SHA256 produces 64 character hex string
    }
}
