use std::fs::OpenOptions;
use std::io::Write;
use std::path::Path;
use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: Uuid,
    pub timestamp: DateTime<Utc>,
    pub level: LogLevel,
    pub category: LogCategory,
    pub user_id: Option<String>,
    pub session_id: Option<Uuid>,
    pub message: String,
    pub details: Option<serde_json::Value>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogCategory {
    Authentication,
    Command,
    FileOperation,
    ProcessManagement,
    SystemInfo,
    Communication,
    Security,
    Error,
}

pub struct Logger {
    log_file_path: String,
}

impl Logger {
    pub fn new(log_file_path: String) -> Self {
        Self { log_file_path }
    }

    pub fn log(&self, entry: LogEntry) -> Result<()> {
        // 写入到文件
        self.write_to_file(&entry)?;
        
        // 同时输出到控制台（根据日志级别）
        match entry.level {
            LogLevel::Debug => log::debug!("{}", self.format_entry(&entry)),
            LogLevel::Info => log::info!("{}", self.format_entry(&entry)),
            LogLevel::Warning => log::warn!("{}", self.format_entry(&entry)),
            LogLevel::Error => log::error!("{}", self.format_entry(&entry)),
            LogLevel::Critical => log::error!("CRITICAL: {}", self.format_entry(&entry)),
        }

        Ok(())
    }

    pub fn log_authentication(&self, user_id: &str, success: bool, details: Option<serde_json::Value>) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: if success { LogLevel::Info } else { LogLevel::Warning },
            category: LogCategory::Authentication,
            user_id: Some(user_id.to_string()),
            session_id: None,
            message: format!("Authentication {} for user: {}", 
                if success { "successful" } else { "failed" }, user_id),
            details,
        };
        self.log(entry)
    }

    pub fn log_command(&self, user_id: &str, session_id: Uuid, command: &str, success: bool) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: if success { LogLevel::Info } else { LogLevel::Error },
            category: LogCategory::Command,
            user_id: Some(user_id.to_string()),
            session_id: Some(session_id),
            message: format!("Command execution {}: {}", 
                if success { "successful" } else { "failed" }, command),
            details: None,
        };
        self.log(entry)
    }

    pub fn log_file_operation(&self, user_id: &str, session_id: Uuid, operation: &str, file_path: &str, success: bool) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: if success { LogLevel::Info } else { LogLevel::Error },
            category: LogCategory::FileOperation,
            user_id: Some(user_id.to_string()),
            session_id: Some(session_id),
            message: format!("File {} {}: {}", operation, 
                if success { "successful" } else { "failed" }, file_path),
            details: None,
        };
        self.log(entry)
    }

    pub fn log_security_event(&self, event: &str, details: Option<serde_json::Value>) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: LogLevel::Critical,
            category: LogCategory::Security,
            user_id: None,
            session_id: None,
            message: format!("Security event: {}", event),
            details,
        };
        self.log(entry)
    }

    pub fn log_error(&self, error: &str, details: Option<serde_json::Value>) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: LogLevel::Error,
            category: LogCategory::Error,
            user_id: None,
            session_id: None,
            message: error.to_string(),
            details,
        };
        self.log(entry)
    }

    fn write_to_file(&self, entry: &LogEntry) -> Result<()> {
        let json_line = serde_json::to_string(entry)?;
        
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.log_file_path)?;
            
        writeln!(file, "{}", json_line)?;
        file.flush()?;
        
        Ok(())
    }

    fn format_entry(&self, entry: &LogEntry) -> String {
        format!("[{}] [{:?}] [{:?}] {} - {}", 
            entry.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
            entry.level,
            entry.category,
            entry.user_id.as_deref().unwrap_or("system"),
            entry.message
        )
    }

    pub fn ensure_log_directory(&self) -> Result<()> {
        if let Some(parent) = Path::new(&self.log_file_path).parent() {
            std::fs::create_dir_all(parent)?;
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[test]
    fn test_logging() {
        let temp_file = NamedTempFile::new().unwrap();
        let logger = Logger::new(temp_file.path().to_string_lossy().to_string());
        
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: LogLevel::Info,
            category: LogCategory::Authentication,
            user_id: Some("test_user".to_string()),
            session_id: None,
            message: "Test log entry".to_string(),
            details: None,
        };
        
        logger.log(entry).unwrap();
        
        // 验证文件内容
        let content = std::fs::read_to_string(temp_file.path()).unwrap();
        assert!(content.contains("Test log entry"));
    }
}
