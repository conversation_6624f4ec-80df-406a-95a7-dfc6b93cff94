OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
cargo:rustc-link-lib=static=ring-core
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=TARGET_CC
TARGET_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CROSS_COMPILE
CROSS_COMPILE = None
RUSTC_LINKER = None
PATH = Some(/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Users/<USER>/.local/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/Users/<USER>/miniconda3/condabin:/opt/homebrew/opt/postgresql@15/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/Users/<USER>/.orbstack/bin:/Users/<USER>/sdk/go1.20/bin)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(crt-static,fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=TARGET_CFLAGS
TARGET_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static)
cargo:rustc-link-lib=static=ring-test
cargo:rustc-link-search=native=/Users/<USER>/Downloads/生成代码/target/x86_64-unknown-linux-musl/debug/build/ring-91a1aba94a9bf4f5/out
