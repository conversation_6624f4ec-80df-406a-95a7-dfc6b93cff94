{"rustc": 14389903092037495548, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12833142538252791333, "build_script_build", false, 16581274806954121713]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-musl/debug/build/libc-0c0fae63b6d81473/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_FREEBSD_VERSION", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_MUSL_V1_2_3", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_LINUX_TIME_BITS64", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_FILE_OFFSET_BITS", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_TIME_BITS", "val": null}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 0, "config": 0, "compile_kind": 0}