{"rustc": 15497389221046826682, "features": "[\"client\", \"default\", \"h2\", \"http1\", \"http2\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 15657897354478470176, "path": 3548205941525672353, "deps": [[784494742817713399, "tower_service", false, 13498446512614934318], [1569313478171189446, "want", false, 6914766910496807971], [1811549171721445101, "futures_channel", false, 17037763353070660570], [1906322745568073236, "pin_project_lite", false, 13848817472509504521], [4405182208873388884, "http", false, 9752162378891032398], [6163892036024256188, "httparse", false, 5624755823816601330], [6304235478050270880, "httpdate", false, 15024618828216831023], [7620660491849607393, "futures_core", false, 16361054614432244940], [7695812897323945497, "itoa", false, 11493748733483228252], [8606274917505247608, "tracing", false, 16214190024088483953], [8915503303801890683, "http_body", false, 4860913556413215518], [9538054652646069845, "tokio", false, 17845185179709145024], [10629569228670356391, "futures_util", false, 12436447373513645546], [12614995553916589825, "socket2", false, 12448718539487965511], [13809605890706463735, "h2", false, 14849084729469314460], [16066129441945555748, "bytes", false, 875704742383242426]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/hyper-15cc3d37edfef637/dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 6253457446599841207}