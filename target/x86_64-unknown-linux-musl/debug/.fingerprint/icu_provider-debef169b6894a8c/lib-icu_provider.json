{"rustc": 15497389221046826682, "features": "[\"baked\", \"zerotrie\"]", "declared_features": "[\"alloc\", \"baked\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"export\", \"logging\", \"serde\", \"std\", \"sync\", \"zerotrie\"]", "target": 8134314816311233441, "profile": 15657897354478470176, "path": 18194047105515495525, "deps": [[577007972892873560, "icu_locale_core", false, 6377247008479968468], [1537006514548139957, "zerovec", false, 10630754421239109653], [1720717020211068583, "writeable", false, 12694090053916375671], [2094002304596326048, "zerotrie", false, 7241156482802175362], [*******************, "stable_deref_trait", false, 3054144737957785131], [5298260564258778412, "displaydoc", false, 10357564515802703469], [10706449961930108323, "yoke", false, 12034855611995008696], [17046516144589451410, "zerofrom", false, 5638129036984432884], [18328566729972757851, "tinystr", false, 17253108901453796446]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/icu_provider-debef169b6894a8c/dep-lib-icu_provider", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 6253457446599841207}