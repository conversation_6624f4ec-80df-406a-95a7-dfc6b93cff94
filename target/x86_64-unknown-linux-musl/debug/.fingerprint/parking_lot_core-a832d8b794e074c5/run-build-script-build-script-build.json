{"rustc": 14389903092037495548, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10502524114991711738, "build_script_build", false, 7255554637851993038]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-musl/debug/build/parking_lot_core-a832d8b794e074c5/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 0, "config": 0, "compile_kind": 0}