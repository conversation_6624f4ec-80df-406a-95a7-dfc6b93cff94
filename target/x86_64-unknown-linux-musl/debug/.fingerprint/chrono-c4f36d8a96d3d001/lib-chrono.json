{"rustc": 14389903092037495548, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 16690480377348987070, "path": 6165458447426752483, "deps": [[10448766010662481490, "num_traits", false, 10975305711378212469], [10633404241517405153, "serde", false, 14284728906081798239], [17958873330977204455, "iana_time_zone", false, 7522787890161055289]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/chrono-c4f36d8a96d3d001/dep-lib-chrono"}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 5257165632161081476}