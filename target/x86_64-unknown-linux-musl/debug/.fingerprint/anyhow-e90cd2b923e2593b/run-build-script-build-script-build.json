{"rustc": 14389903092037495548, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10291739091677281249, "build_script_build", false, 3513231582091445921]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-musl/debug/build/anyhow-e90cd2b923e2593b/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 0, "config": 0, "compile_kind": 0}