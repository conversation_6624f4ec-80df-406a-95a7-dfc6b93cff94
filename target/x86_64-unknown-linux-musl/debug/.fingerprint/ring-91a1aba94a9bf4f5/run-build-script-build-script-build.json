{"rustc": 14389903092037495548, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4439724684199411703, "build_script_build", false, 2257514887811422783]], "local": [{"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CROSS_COMPILE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 0, "config": 0, "compile_kind": 0}