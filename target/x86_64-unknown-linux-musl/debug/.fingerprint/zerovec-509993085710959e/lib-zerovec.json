{"rustc": 15497389221046826682, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 7111364811161841961, "deps": [[9620753569207166497, "zerovec_derive", false, 3740447881798644688], [10706449961930108323, "yoke", false, 12034855611995008696], [17046516144589451410, "zerofrom", false, 5638129036984432884]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/zerovec-509993085710959e/dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 6253457446599841207}