{"rustc": 15497389221046826682, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 15657897354478470176, "path": 13911930050628075899, "deps": [[99287295355353247, "data_encoding", false, 9336206958275468039], [3150220818285335163, "url", false, 17863916626696813119], [3712811570531045576, "byteorder", false, 7859281221304559409], [4359956005902820838, "utf8", false, 171736495505945700], [5986029879202738730, "log", false, 15134801711149163775], [6163892036024256188, "httparse", false, 5624755823816601330], [8008191657135824715, "thiserror", false, 10287661936079724500], [9010263965687315507, "http", false, 1960534886399851134], [10724389056617919257, "sha1", false, 18391876158157470966], [13208667028893622512, "rand", false, 9341833178250196594], [16066129441945555748, "bytes", false, 875704742383242426]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/tungstenite-259ac9780a279e61/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 6253457446599841207}