{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 1350602797799047379, "deps": [[40386456601120721, "percent_encoding", false, 17362389047065892903], [95042085696191081, "ipnet", false, 14966116094509204485], [264090853244900308, "sync_wrapper", false, 7674857081194675876], [784494742817713399, "tower_service", false, 13498446512614934318], [1044435446100926395, "hyper_rustls", false, 12205679812704579079], [1906322745568073236, "pin_project_lite", false, 13848817472509504521], [3150220818285335163, "url", false, 17863916626696813119], [3722963349756955755, "once_cell", false, 10840532378643838332], [4405182208873388884, "http", false, 9752162378891032398], [5986029879202738730, "log", false, 15134801711149163775], [7414427314941361239, "hyper", false, 11209443485133014464], [7620660491849607393, "futures_core", false, 16361054614432244940], [8915503303801890683, "http_body", false, 4860913556413215518], [9538054652646069845, "tokio", false, 17845185179709145024], [9689903380558560274, "serde", false, 16976241683348432041], [10229185211513642314, "mime", false, 10094228118659111302], [10629569228670356391, "futures_util", false, 12436447373513645546], [11295624341523567602, "rustls", false, 16526504777858610194], [13809605890706463735, "h2", false, 14849084729469314460], [14564311161534545801, "encoding_rs", false, 4889258978778499841], [15367738274754116744, "serde_json", false, 7587046818491582250], [16066129441945555748, "bytes", false, 875704742383242426], [16311359161338405624, "rustls_pemfile", false, 11371849167985269875], [16542808166767769916, "serde_urlencoded", false, 11475924378648811795], [16622232390123975175, "tokio_rustls", false, 5773663693804525521], [17652733826348741533, "webpki_roots", false, 5427243580665669673], [18066890886671768183, "base64", false, 11091021286042146437]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/reqwest-34bcaf20414fb9c9/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 6253457446599841207}