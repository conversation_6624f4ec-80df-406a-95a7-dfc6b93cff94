{"rustc": 14389903092037495548, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 14615259678829105530, "profile": 16690480377348987070, "path": 6564302187929348060, "deps": [[4439724684199411703, "build_script_build", false, 3467022967532360944], [8244776183334334055, "once_cell", false, 11242543432499844084], [12833142538252791333, "libc", false, 11184623255310398249], [15752510154921626426, "spin", false, 8428591822359135517], [16608859830347377199, "untrusted", false, 2483573682799886858]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/ring-4c3f82cce5ff2a01/dep-lib-ring"}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 5675962118468161319, "config": 2202906307356721367, "compile_kind": 5257165632161081476}