{"rustc": 14389903092037495548, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 11884987481660704207, "profile": 16690480377348987070, "path": 11047590855197583688, "deps": [[5682297152023424035, "cfg_if", false, 9359862543864041374], [12833142538252791333, "libc", false, 11184623255310398249]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/getrandom-45f7cf837fbb2868/dep-lib-getrandom"}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 12606519392706294666, "config": 2202906307356721367, "compile_kind": 5257165632161081476}