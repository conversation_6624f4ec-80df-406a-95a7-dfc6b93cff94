{"rustc": 14389903092037495548, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 18138894155633354567, "profile": 16690480377348987070, "path": 7626488794447814373, "deps": [[5682297152023424035, "cfg_if", false, 9359862543864041374], [10502524114991711738, "build_script_build", false, 5429889839100799710], [12833142538252791333, "libc", false, 11184623255310398249], [13902819013840624958, "smallvec", false, 5150140230879535227]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/parking_lot_core-af5cdc83dd87a6be/dep-lib-parking_lot_core"}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 2941687627020168538, "config": 2202906307356721367, "compile_kind": 5257165632161081476}