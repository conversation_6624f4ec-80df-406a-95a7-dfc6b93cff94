{"rustc": 14389903092037495548, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"hardware-lock-elision\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\"]", "target": 12002291437879235703, "profile": 16690480377348987070, "path": 13135114542928347871, "deps": [[4875221159611462770, "lock_api", false, 3902672765241515995], [10502524114991711738, "parking_lot_core", false, 17813677545950024998]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-musl/debug/.fingerprint/parking_lot-028cc7182cf59774/dep-lib-parking_lot"}}], "rustflags": ["-C", "target-feature=+crt-static"], "metadata": 3021512261575560469, "config": 2202906307356721367, "compile_kind": 5257165632161081476}