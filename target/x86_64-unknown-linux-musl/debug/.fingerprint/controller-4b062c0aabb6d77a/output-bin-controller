{"$message_type":"diagnostic","message":"unused import: `Permission`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/controller.rs","byte_start":320,"byte_end":330,"line_start":9,"line_end":9,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"    <PERSON>gger, AuthManager, Permission","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"controller/src/controller.rs","byte_start":318,"byte_end":330,"line_start":9,"line_end":9,"column_start":24,"column_end":36,"is_primary":true,"text":[{"text":"    Logger, AuthManager, Permission","highlight_start":24,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Permission`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/controller.rs:9:26\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Logger, AuthManager, Permission\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Ipv4Addr`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":121,"byte_end":129,"line_start":4,"line_end":4,"column_start":24,"column_end":32,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":24,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":119,"byte_end":129,"line_start":4,"line_end":4,"column_start":22,"column_end":32,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":22,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":112,"byte_end":113,"line_start":4,"line_end":4,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":129,"byte_end":130,"line_start":4,"line_end":4,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Ipv4Addr`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/reconnaissance.rs:4:24\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::net::{IpAddr, Ipv4Addr};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::process::Command`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":136,"byte_end":157,"line_start":5,"line_end":5,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use std::process::Command;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":132,"byte_end":159,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::process::Command;","highlight_start":1,"highlight_end":27},{"text":"use tokio::process::Command as AsyncCommand;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::process::Command`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/reconnaissance.rs:5:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::process::Command;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::convert::Infallible`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/ui.rs","byte_start":5875,"byte_end":5899,"line_start":169,"line_end":169,"column_start":9,"column_end":33,"is_primary":true,"text":[{"text":"    use std::convert::Infallible;","highlight_start":9,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"controller/src/ui.rs","byte_start":5871,"byte_end":5900,"line_start":169,"line_end":169,"column_start":5,"column_end":34,"is_primary":true,"text":[{"text":"    use std::convert::Infallible;","highlight_start":5,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::convert::Infallible`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/ui.rs:169:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m169\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use std::convert::Infallible;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `password`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/controller.rs","byte_start":2096,"byte_end":2104,"line_start":69,"line_end":69,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        if let Some(password) = password {","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"controller/src/controller.rs","byte_start":2096,"byte_end":2104,"line_start":69,"line_end":69,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        if let Some(password) = password {","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":"_password","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `password`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/controller.rs:69:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(password) = password {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_password`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `password`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/deployment.rs","byte_start":401,"byte_end":409,"line_start":16,"line_end":16,"column_start":70,"column_end":78,"is_primary":true,"text":[{"text":"    pub async fn deploy_via_ssh(&self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {","highlight_start":70,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"controller/src/deployment.rs","byte_start":401,"byte_end":409,"line_start":16,"line_end":16,"column_start":70,"column_end":78,"is_primary":true,"text":[{"text":"    pub async fn deploy_via_ssh(&self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {","highlight_start":70,"highlight_end":78}],"label":null,"suggested_replacement":"_password","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `password`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/deployment.rs:16:70\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn deploy_via_ssh(&self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_password`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `target`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/deployment.rs","byte_start":6292,"byte_end":6298,"line_start":185,"line_end":185,"column_start":52,"column_end":58,"is_primary":true,"text":[{"text":"    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {","highlight_start":52,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"controller/src/deployment.rs","byte_start":6292,"byte_end":6298,"line_start":185,"line_end":185,"column_start":52,"column_end":58,"is_primary":true,"text":[{"text":"    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {","highlight_start":52,"highlight_end":58}],"label":null,"suggested_replacement":"_target","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `target`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/deployment.rs:185:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_target`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/deployment.rs","byte_start":6306,"byte_end":6314,"line_start":185,"line_end":185,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"controller/src/deployment.rs","byte_start":6306,"byte_end":6314,"line_start":185,"line_end":185,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `username`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/deployment.rs:185:66\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `services`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":8537,"byte_end":8545,"line_start":268,"line_end":268,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"    async fn scan_vulnerabilities(&self, ip: IpAddr, services: &[ServiceInfo]) -> Result<Vec<VulnerabilityInfo>> {","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":8537,"byte_end":8545,"line_start":268,"line_end":268,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"    async fn scan_vulnerabilities(&self, ip: IpAddr, services: &[ServiceInfo]) -> Result<Vec<VulnerabilityInfo>> {","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":"_services","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `services`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/reconnaissance.rs:268:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn scan_vulnerabilities(&self, ip: IpAddr, services: &[ServiceInfo]) -> Result<Vec<VulnerabilityInfo>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_services`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `controller`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/ui.rs","byte_start":5339,"byte_end":5349,"line_start":149,"line_end":149,"column_start":34,"column_end":44,"is_primary":true,"text":[{"text":"pub async fn start_web_interface(controller: &mut Controller, port: u16) -> Result<()> {","highlight_start":34,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"controller/src/ui.rs","byte_start":5339,"byte_end":5349,"line_start":149,"line_end":149,"column_start":34,"column_end":44,"is_primary":true,"text":[{"text":"pub async fn start_web_interface(controller: &mut Controller, port: u16) -> Result<()> {","highlight_start":34,"highlight_end":44}],"label":null,"suggested_replacement":"_controller","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `controller`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/ui.rs:149:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn start_web_interface(controller: &mut Controller, port: u16) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_controller`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `ip_address`, `os_version`, and `last_seen` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/controller.rs","byte_start":370,"byte_end":379,"line_start":13,"line_end":13,"column_start":12,"column_end":21,"is_primary":false,"text":[{"text":"pub struct AgentInfo {","highlight_start":12,"highlight_end":21}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":434,"byte_end":444,"line_start":16,"line_end":16,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub ip_address: String,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":462,"byte_end":472,"line_start":17,"line_end":17,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub os_version: String,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":519,"byte_end":528,"line_start":19,"line_end":19,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub last_seen: chrono::DateTime<chrono::Utc>,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`AgentInfo` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `ip_address`, `os_version`, and `last_seen` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/controller.rs:16:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct AgentInfo {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub ip_address: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub os_version: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub status: AgentStatus,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub last_seen: chrono::DateTime<chrono::Utc>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `AgentInfo` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `Online`, `Offline`, and `Error` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/controller.rs","byte_start":637,"byte_end":648,"line_start":24,"line_end":24,"column_start":10,"column_end":21,"is_primary":false,"text":[{"text":"pub enum AgentStatus {","highlight_start":10,"highlight_end":21}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":655,"byte_end":661,"line_start":25,"line_end":25,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Online,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":667,"byte_end":674,"line_start":26,"line_end":26,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    Offline,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":696,"byte_end":701,"line_start":28,"line_end":28,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    Error(String),","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`AgentStatus` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variants `Online`, `Offline`, and `Error` are never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/controller.rs:25:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum AgentStatus {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Online,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Offline,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Connecting,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Error(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `AgentStatus` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `auth_manager` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/controller.rs","byte_start":1121,"byte_end":1131,"line_start":42,"line_end":42,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct Controller {","highlight_start":12,"highlight_end":22}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":1232,"byte_end":1244,"line_start":46,"line_end":46,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    auth_manager: AuthManager,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `auth_manager` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/controller.rs:46:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Controller {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    auth_manager: AuthManager,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `manage_process` and `start_heartbeat_monitoring` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/controller.rs","byte_start":1262,"byte_end":1277,"line_start":49,"line_end":49,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl Controller {","highlight_start":1,"highlight_end":16}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":7202,"byte_end":7216,"line_start":206,"line_end":206,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn manage_process(&mut self, target: &str, action: common::ProcessAction) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/controller.rs","byte_start":7878,"byte_end":7904,"line_start":226,"line_end":226,"column_start":18,"column_end":44,"is_primary":true,"text":[{"text":"    pub async fn start_heartbeat_monitoring(&self) {","highlight_start":18,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `manage_process` and `start_heartbeat_monitoring` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/controller.rs:206:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m49\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Controller {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn manage_process(&mut self, target: &str, action: common::ProcessAction) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn start_heartbeat_monitoring(&self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `DeploymentManager` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/deployment.rs","byte_start":159,"byte_end":176,"line_start":7,"line_end":7,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"pub struct DeploymentManager {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `DeploymentManager` is never constructed\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/deployment.rs:7:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DeploymentManager {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/deployment.rs","byte_start":213,"byte_end":235,"line_start":11,"line_end":11,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"impl DeploymentManager {","highlight_start":1,"highlight_end":23}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":249,"byte_end":252,"line_start":12,"line_end":12,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(agent_binary_path: String) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":349,"byte_end":363,"line_start":16,"line_end":16,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn deploy_via_ssh(&self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":2515,"byte_end":2534,"line_start":64,"line_end":64,"column_start":14,"column_end":33,"is_primary":true,"text":[{"text":"    async fn upload_file_via_scp(&self, target: &str, local_path: &str, remote_path: &str, key_file: Option<&str>) -> Result<()> {","highlight_start":14,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":3351,"byte_end":3370,"line_start":91,"line_end":91,"column_start":14,"column_end":33,"is_primary":true,"text":[{"text":"    async fn execute_ssh_command(&self, target: &str, command: &str, key_file: Option<&str>) -> Result<String> {","highlight_start":14,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":4194,"byte_end":4218,"line_start":119,"line_end":119,"column_start":8,"column_end":32,"is_primary":true,"text":[{"text":"    fn generate_systemd_service(&self) -> String {","highlight_start":8,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":4586,"byte_end":4600,"line_start":137,"line_end":137,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn deploy_via_web(&self, target_url: &str, auth_token: &str) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":5664,"byte_end":5675,"line_start":168,"line_end":168,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn build_agent(&self, target_arch: &str) -> Result<String> {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/deployment.rs","byte_start":6258,"byte_end":6284,"line_start":185,"line_end":185,"column_start":18,"column_end":44,"is_primary":true,"text":[{"text":"    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {","highlight_start":18,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/deployment.rs:12:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DeploymentManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(agent_binary_path: String) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn deploy_via_ssh(&self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m64\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn upload_file_via_scp(&self, target: &str, local_path: &str, remote_path: &str, key_file: Option<&str>) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m91\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_ssh_command(&self, target: &str, command: &str, key_file: Option<&str>) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn generate_systemd_service(&self) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn deploy_via_web(&self, target_url: &str, auth_token: &str) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn build_agent(&self, target_arch: &str) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ReconnaissanceEngine` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":1349,"byte_end":1369,"line_start":54,"line_end":54,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"pub struct ReconnaissanceEngine {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `ReconnaissanceEngine` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/reconnaissance.rs:54:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ReconnaissanceEngine {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/reconnaissance.rs","byte_start":1417,"byte_end":1442,"line_start":58,"line_end":58,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"impl ReconnaissanceEngine {","highlight_start":1,"highlight_end":26}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":1456,"byte_end":1459,"line_start":59,"line_end":59,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":1558,"byte_end":1570,"line_start":65,"line_end":65,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    pub async fn scan_network(&mut self, network: &str) -> Result<Vec<TargetInfo>> {","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":2027,"byte_end":2036,"line_start":79,"line_end":79,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn scan_host(&mut self, ip: IpAddr) -> Result<TargetInfo> {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":3064,"byte_end":3078,"line_start":111,"line_end":111,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn discover_hosts(&self, network: &str) -> Result<Vec<IpAddr>> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":3903,"byte_end":3919,"line_start":139,"line_end":139,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn resolve_hostname(&self, ip: IpAddr) -> Result<String> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":4623,"byte_end":4633,"line_start":160,"line_end":160,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    async fn scan_ports(&self, ip: IpAddr) -> Result<Vec<PortInfo>> {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":6148,"byte_end":6165,"line_start":200,"line_end":200,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"    async fn identify_services(&self, ip: IpAddr, ports: &[PortInfo]) -> Result<Vec<ServiceInfo>> {","highlight_start":14,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":6931,"byte_end":6949,"line_start":221,"line_end":221,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"    async fn get_service_banner(&self, ip: IpAddr, port: u16) -> Result<String> {","highlight_start":14,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":7462,"byte_end":7473,"line_start":236,"line_end":236,"column_start":14,"column_end":25,"is_primary":true,"text":[{"text":"    async fn identify_os(&self, ip: IpAddr) -> Result<OsInfo> {","highlight_start":14,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":8497,"byte_end":8517,"line_start":268,"line_end":268,"column_start":14,"column_end":34,"is_primary":true,"text":[{"text":"    async fn scan_vulnerabilities(&self, ip: IpAddr, services: &[ServiceInfo]) -> Result<Vec<VulnerabilityInfo>> {","highlight_start":14,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":9868,"byte_end":9883,"line_start":303,"line_end":303,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn get_target_info(&self, ip: IpAddr) -> Option<&TargetInfo> {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":9976,"byte_end":9991,"line_start":307,"line_end":307,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn get_all_targets(&self) -> Vec<&TargetInfo> {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":10079,"byte_end":10093,"line_start":311,"line_end":311,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn export_results(&self, format: &str) -> Result<String> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"controller/src/reconnaissance.rs","byte_start":10366,"byte_end":10376,"line_start":319,"line_end":319,"column_start":8,"column_end":18,"is_primary":true,"text":[{"text":"    fn export_csv(&self) -> Result<String> {","highlight_start":8,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/reconnaissance.rs:59:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ReconnaissanceEngine {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m59\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn scan_network(&mut self, network: &str) -> Result<Vec<TargetInfo>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn scan_host(&mut self, ip: IpAddr) -> Result<TargetInfo> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn discover_hosts(&self, network: &str) -> Result<Vec<IpAddr>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn resolve_hostname(&self, ip: IpAddr) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn scan_ports(&self, ip: IpAddr) -> Result<Vec<PortInfo>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn identify_services(&self, ip: IpAddr, ports: &[PortInfo]) -> Result<Vec<ServiceInfo>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_service_banner(&self, ip: IpAddr, port: u16) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn identify_os(&self, ip: IpAddr) -> Result<OsInfo> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn scan_vulnerabilities(&self, ip: IpAddr, services: &[ServiceInfo]) -> Result<Vec<VulnerabilityInfo>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_target_info(&self, ip: IpAddr) -> Option<&TargetInfo> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_all_targets(&self) -> Vec<&TargetInfo> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn export_results(&self, format: &str) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn export_csv(&self) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `start_simple_web_interface` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/ui.rs","byte_start":5783,"byte_end":5809,"line_start":168,"line_end":168,"column_start":14,"column_end":40,"is_primary":true,"text":[{"text":"pub async fn start_simple_web_interface(_controller: &mut Controller, port: u16) -> Result<()> {","highlight_start":14,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `start_simple_web_interface` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/ui.rs:168:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn start_simple_web_interface(_controller: &mut Controller, port: u16) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `create_dashboard_html` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"controller/src/ui.rs","byte_start":7041,"byte_end":7062,"line_start":208,"line_end":208,"column_start":4,"column_end":25,"is_primary":true,"text":[{"text":"fn create_dashboard_html() -> String {","highlight_start":4,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `create_dashboard_html` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcontroller/src/ui.rs:208:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn create_dashboard_html() -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
 WARN rustc_codegen_ssa::back::link Linker does not support -static-pie command line option. Retrying with -static instead.
{"$message_type":"diagnostic","message":"linking with `cc` failed: exit status: 1","code":null,"level":"error","spans":[],"children":[{"message":" \"cc\" \"-m64\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crt1.o\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crti.o\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crtbegin.o\" \"/var/folders/rr/vjcgxwp96zx2hb_sfv8lfbnh0000gn/T/rustchH3Y3E/symbols.o\" \"<257 object files omitted>\" \"-Wl,--as-needed\" \"-Wl,-Bstatic\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/deps/{libenv_logger-f9248b93d10205ab.rlib,libtermcolor-0098235363a24495.rlib,libis_terminal-3a6a5f9a0319c943.rlib,libhumantime-f002b302fe72e5ba.rlib,libregex-9caaf8b2cd7c93b7.rlib,libregex_automata-66fd237a2935f8ed.rlib,libaho_corasick-afe8b164c69a039d.rlib,libregex_syntax-4addb411c54a2755.rlib,libclap-476715dbceb13b0b.rlib,libclap_builder-e7dca33ddaed61b9.rlib,libstrsim-ae568d693293b4fb.rlib,libanstream-3781f5c96aa6f60e.rlib,libanstyle_query-d3a91f77ceb9f706.rlib,libis_terminal_polyfill-48378e4d6f2b5a7a.rlib,libcolorchoice-d0e45c7fca88bb17.rlib,libanstyle_parse-8a3b60bdc0e13a5c.rlib,libutf8parse-38ef80bd9c8549a3.rlib,libclap_lex-e4006a0834cc9e98.rlib,libanstyle-9bf17afc161330a6.rlib,libwarp-dc6da8c248549412.rlib,libmime_guess-7e87e6cb6e92655c.rlib,libunicase-5fb13ac88594d540.rlib,libscoped_tls-70b083bb852211de.rlib,libtokio_tungstenite-4851624a1c9546f7.rlib,libtungstenite-259ac9780a279e61.rlib,libutf8-09994257dd0760c6.rlib,libbyteorder-4e68e5b79bffe8d9.rlib,libhttp-8f9b8e93ff00b68b.rlib,libmulter-b401779c7209b72f.rlib,libspin-2d758fb0f4df0d2e.rlib,libheaders-c84ccc03a0b7a58a.rlib,libsha1-f7def8f33ff470c9.rlib,libheaders_core-a5d92ae5020510dc.rlib,libpin_project-b34e13673ae2405b.rlib,libcommon-606c8a99c0ee9155.rlib,libwebpki_roots-6be5b447b7a6e655.rlib,libwebpki-893832893fa28711.rlib,libx509_parser-2b8a9964d77e36fa.rlib,libdata_encoding-2946a4ae212c7634.rlib,liblazy_static-585be0c81da552ed.rlib,liboid_registry-85d234e478859675.rlib,libder_parser-0c90421c4166d723.rlib,libnum_bigint-b7d239cd5f3359e5.rlib,libnum_integer-35a54b0342339f9a.rlib,libasn1_rs-7030c52b71d02c35.rlib,libthiserror-59a0dc503afccd9e.rlib,librusticata_macros-262a0fd7cdcf727e.rlib,libnom-085e628817f774fe.rlib,librcgen-95ba24c714699635.rlib,libyasna-486dca0dad9a8a4f.rlib,libtime-aca012169a2f13b3.rlib,libtime_core-7cc6cdcf749a0851.rlib,libnum_conv-27df8856a6157e3c.rlib,libderanged-f9d0f8c79e585d83.rlib,libpowerfmt-ebeb7ecaaf2e4d29.rlib,libring-1d72229a234629ab.rlib,libspin-75925105f38b4a20.rlib,libuntrusted-9da735f561d15642.rlib,libpem-2bd81708c5962533.rlib,libbase64-51411a8fc778bfc1.rlib,libchrono-96da80c483f3372a.rlib,libiana_time_zone-34d9a454ee7b3e0b.rlib,libnum_traits-ee2776d2e3fb7528.rlib,libsha2-0412c6258f2eb978.rlib,libdigest-d5d9100af52fd198.rlib,libblock_buffer-3e910e19e6f1856b.rlib,libaes_gcm-241543841f2a2118.rlib,libctr-978078814531205c.rlib,libghash-1fafeb266110c5f5.rlib,libpolyval-2aea15a14aa9a488.rlib,libopaque_debug-76c4a2413df9dfb6.rlib,libuniversal_hash-fba1a9afb2ea353f.rlib,libsubtle-0835054feecce9be.rlib,libaes-bc0e29d9ad9b1edf.rlib,libcpufeatures-034cde7efab33605.rlib,libcipher-9942c25859fdd48b.rlib,libinout-194719d89c0b3bd7.rlib,libaead-8b23e1b601ff0bc7.rlib,libcrypto_common-fc7171ebf7df1d4a.rlib,libgeneric_array-7af1b03e5abfd8f3.rlib,libtypenum-cd00d26865906af9.rlib,librand-b80b0a853a583dd6.rlib,librand_chacha-31e6eebd469e1538.rlib,libppv_lite86-0f401d69f5a83452.rlib,libzerocopy-6d3513771287b352.rlib,librand_core-3a57a21e2debafe1.rlib,libreqwest-34bcaf20414fb9c9.rlib,librustls_pemfile-0cd3885f416d4ff7.rlib,libhyper_rustls-951d1e46e1fc6448.rlib,libserde_urlencoded-cd3434cd8f6348a6.rlib,libwebpki_roots-c808856b57c24956.rlib,libbase64-65047d27a1b80a29.rlib,libipnet-3f95026a3066d51a.rlib,libtokio_rustls-0b6906763af16195.rlib,libmime-0e9ec34397c53be9.rlib,libencoding_rs-ef1399134bf32e9e.rlib,libserde_json-792a6020721b1ed8.rlib,libmemchr-bec4cb6fc3f6667c.rlib,libryu-7eacd61edad0c654.rlib,librustls-176a1454f21bd181.rlib,libsct-6ece40bb384df7d9.rlib,libwebpki-f571fbd2e8a90096.rlib,libring-4ae9d45936aae378.rlib,libgetrandom-e468e60f2bfd0849.rlib,libuntrusted-0ff6a4b7e58ea8d5.rlib,libhyper-15cc3d37edfef637.rlib,libwant-25c53cb2765f05a6.rlib,libtry_lock-d2ad3ca7c6b108d7.rlib,libhttparse-64e2067453caa97c.rlib,libh2-b712c1cf386568ab.rlib,libindexmap-326b235fa77b7c20.rlib,libequivalent-f950363f051af0e1.rlib,libhashbrown-191766a760cfad49.rlib,libtokio_util-93f5b7ef4209d16c.rlib,libtower_service-bdbcd5961a6e7c88.rlib,libtracing-588a0f4b7e1bd70c.rlib,libtracing_core-88c650b8d5db753e.rlib,libonce_cell-840615805104978c.rlib,libfutures_channel-b85f4c26e796651b.rlib,libhttpdate-90c7818154711529.rlib,libfutures_util-236745f3e8d789b6.rlib,libslab-736088d705d12a7f.rlib,libfutures_sink-cefd685dd40c0587.rlib,libfutures_task-7b521edd44f3c6b8.rlib,libpin_utils-cc741b4e75833186.rlib,libsync_wrapper-5d0d50a450411de6.rlib,libhttp_body-f05916ce8552549c.rlib,liblog-25b97e903af95274.rlib,libfutures_core-46a6763be7b7925b.rlib,liburl-af63e9385612faf2.rlib,libidna-4435aac523554b46.rlib,libutf8_iter-cb9c2719b0f3090f.rlib,libidna_adapter-60b980d494fcd946.rlib,libicu_properties-0851b06a39770ffb.rlib,libicu_properties_data-682649cda6e37e2e.rlib,libicu_normalizer-9a735fc311ce3cdd.rlib,libicu_normalizer_data-86a79f93aa6185f7.rlib,libicu_collections-b078858655d47682.rlib,libpotential_utf-f62bf2c1ab109143.rlib,libicu_provider-debef169b6894a8c.rlib,libicu_locale_core-70f1c120f8c2a3fb.rlib,libtinystr-0f8e5e909cbb1f59.rlib,liblitemap-b7e48f5ed5c66601.rlib,libwriteable-a39cc087fdca1414.rlib,libzerovec-509993085710959e.rlib,libzerotrie-a6ab29fff63d6080.rlib,libyoke-22b84f592ce28c58.rlib,libstable_deref_trait-b308ae4052b1d364.rlib,libzerofrom-acadf16119919749.rlib,libform_urlencoded-05348703df7376b8.rlib,libpercent_encoding-2df529d3cdada6b8.rlib,libhttp-171553c734e64f18.rlib,libitoa-02dabe3f520f938d.rlib,libfnv-9da3c947da7376f5.rlib,libuuid-c46dceec0f6a7f52.rlib,libgetrandom-87916d806657893b.rlib,libserde-3058191bb4f0ec31.rlib,libtokio-1afc7c4f706aac05.rlib,libsignal_hook_registry-ce5e1ddf8dbc5643.rlib,libsocket2-0d8d3c62a4546c73.rlib,libbytes-cf039259d3bed735.rlib,libmio-c6e24b365d57c40f.rlib,libparking_lot-d934e5f389afbdd5.rlib,libparking_lot_core-70679fe0c03500d6.rlib,liblibc-a43a41ff26920743.rlib,libcfg_if-192679fa2e74736f.rlib,libsmallvec-98fe09afb32a3e1c.rlib,liblock_api-5d15c9515522ac40.rlib,libscopeguard-efb4e517c02eaa9f.rlib,libpin_project_lite-293692b401dc9125.rlib,libanyhow-4e35b0344267e1ac.rlib}.rlib\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*}.rlib\" \"-lunwind\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/{libcfg_if-*,liblibc-*}.rlib\" \"-lc\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/{liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-L\" \"/var/folders/rr/vjcgxwp96zx2hb_sfv8lfbnh0000gn/T/rustchH3Y3E/raw-dylibs\" \"-Wl,-Bdynamic\" \"-Wl,--eh-frame-hdr\" \"-Wl,-z,noexecstack\" \"-nostartfiles\" \"-L\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/build/ring-4c621507ba1f9d7a/out\" \"-L\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/build/ring-309cbac9de3c61cf/out\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib\" \"-o\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/deps/controller-4b062c0aabb6d77a\" \"-Wl,--gc-sections\" \"-static\" \"-Wl,-z,relro,-z,now\" \"-nodefaultlibs\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crtend.o\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crtn.o\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"ld: unknown option: --as-needed\nclang: error: linker command failed with exit code 1 (use -v to see invocation)\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: linking with `cc` failed: exit status: 1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m:  \"cc\" \"-m64\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crt1.o\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crti.o\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crtbegin.o\" \"/var/folders/rr/vjcgxwp96zx2hb_sfv8lfbnh0000gn/T/rustchH3Y3E/symbols.o\" \"<257 object files omitted>\" \"-Wl,--as-needed\" \"-Wl,-Bstatic\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/deps/{libenv_logger-f9248b93d10205ab.rlib,libtermcolor-0098235363a24495.rlib,libis_terminal-3a6a5f9a0319c943.rlib,libhumantime-f002b302fe72e5ba.rlib,libregex-9caaf8b2cd7c93b7.rlib,libregex_automata-66fd237a2935f8ed.rlib,libaho_corasick-afe8b164c69a039d.rlib,libregex_syntax-4addb411c54a2755.rlib,libclap-476715dbceb13b0b.rlib,libclap_builder-e7dca33ddaed61b9.rlib,libstrsim-ae568d693293b4fb.rlib,libanstream-3781f5c96aa6f60e.rlib,libanstyle_query-d3a91f77ceb9f706.rlib,libis_terminal_polyfill-48378e4d6f2b5a7a.rlib,libcolorchoice-d0e45c7fca88bb17.rlib,libanstyle_parse-8a3b60bdc0e13a5c.rlib,libutf8parse-38ef80bd9c8549a3.rlib,libclap_lex-e4006a0834cc9e98.rlib,libanstyle-9bf17afc161330a6.rlib,libwarp-dc6da8c248549412.rlib,libmime_guess-7e87e6cb6e92655c.rlib,libunicase-5fb13ac88594d540.rlib,libscoped_tls-70b083bb852211de.rlib,libtokio_tungstenite-4851624a1c9546f7.rlib,libtungstenite-259ac9780a279e61.rlib,libutf8-09994257dd0760c6.rlib,libbyteorder-4e68e5b79bffe8d9.rlib,libhttp-8f9b8e93ff00b68b.rlib,libmulter-b401779c7209b72f.rlib,libspin-2d758fb0f4df0d2e.rlib,libheaders-c84ccc03a0b7a58a.rlib,libsha1-f7def8f33ff470c9.rlib,libheaders_core-a5d92ae5020510dc.rlib,libpin_project-b34e13673ae2405b.rlib,libcommon-606c8a99c0ee9155.rlib,libwebpki_roots-6be5b447b7a6e655.rlib,libwebpki-893832893fa28711.rlib,libx509_parser-2b8a9964d77e36fa.rlib,libdata_encoding-2946a4ae212c7634.rlib,liblazy_static-585be0c81da552ed.rlib,liboid_registry-85d234e478859675.rlib,libder_parser-0c90421c4166d723.rlib,libnum_bigint-b7d239cd5f3359e5.rlib,libnum_integer-35a54b0342339f9a.rlib,libasn1_rs-7030c52b71d02c35.rlib,libthiserror-59a0dc503afccd9e.rlib,librusticata_macros-262a0fd7cdcf727e.rlib,libnom-085e628817f774fe.rlib,librcgen-95ba24c714699635.rlib,libyasna-486dca0dad9a8a4f.rlib,libtime-aca012169a2f13b3.rlib,libtime_core-7cc6cdcf749a0851.rlib,libnum_conv-27df8856a6157e3c.rlib,libderanged-f9d0f8c79e585d83.rlib,libpowerfmt-ebeb7ecaaf2e4d29.rlib,libring-1d72229a234629ab.rlib,libspin-75925105f38b4a20.rlib,libuntrusted-9da735f561d15642.rlib,libpem-2bd81708c5962533.rlib,libbase64-51411a8fc778bfc1.rlib,libchrono-96da80c483f3372a.rlib,libiana_time_zone-34d9a454ee7b3e0b.rlib,libnum_traits-ee2776d2e3fb7528.rlib,libsha2-0412c6258f2eb978.rlib,libdigest-d5d9100af52fd198.rlib,libblock_buffer-3e910e19e6f1856b.rlib,libaes_gcm-241543841f2a2118.rlib,libctr-978078814531205c.rlib,libghash-1fafeb266110c5f5.rlib,libpolyval-2aea15a14aa9a488.rlib,libopaque_debug-76c4a2413df9dfb6.rlib,libuniversal_hash-fba1a9afb2ea353f.rlib,libsubtle-0835054feecce9be.rlib,libaes-bc0e29d9ad9b1edf.rlib,libcpufeatures-034cde7efab33605.rlib,libcipher-9942c25859fdd48b.rlib,libinout-194719d89c0b3bd7.rlib,libaead-8b23e1b601ff0bc7.rlib,libcrypto_common-fc7171ebf7df1d4a.rlib,libgeneric_array-7af1b03e5abfd8f3.rlib,libtypenum-cd00d26865906af9.rlib,librand-b80b0a853a583dd6.rlib,librand_chacha-31e6eebd469e1538.rlib,libppv_lite86-0f401d69f5a83452.rlib,libzerocopy-6d3513771287b352.rlib,librand_core-3a57a21e2debafe1.rlib,libreqwest-34bcaf20414fb9c9.rlib,librustls_pemfile-0cd3885f416d4ff7.rlib,libhyper_rustls-951d1e46e1fc6448.rlib,libserde_urlencoded-cd3434cd8f6348a6.rlib,libwebpki_roots-c808856b57c24956.rlib,libbase64-65047d27a1b80a29.rlib,libipnet-3f95026a3066d51a.rlib,libtokio_rustls-0b6906763af16195.rlib,libmime-0e9ec34397c53be9.rlib,libencoding_rs-ef1399134bf32e9e.rlib,libserde_json-792a6020721b1ed8.rlib,libmemchr-bec4cb6fc3f6667c.rlib,libryu-7eacd61edad0c654.rlib,librustls-176a1454f21bd181.rlib,libsct-6ece40bb384df7d9.rlib,libwebpki-f571fbd2e8a90096.rlib,libring-4ae9d45936aae378.rlib,libgetrandom-e468e60f2bfd0849.rlib,libuntrusted-0ff6a4b7e58ea8d5.rlib,libhyper-15cc3d37edfef637.rlib,libwant-25c53cb2765f05a6.rlib,libtry_lock-d2ad3ca7c6b108d7.rlib,libhttparse-64e2067453caa97c.rlib,libh2-b712c1cf386568ab.rlib,libindexmap-326b235fa77b7c20.rlib,libequivalent-f950363f051af0e1.rlib,libhashbrown-191766a760cfad49.rlib,libtokio_util-93f5b7ef4209d16c.rlib,libtower_service-bdbcd5961a6e7c88.rlib,libtracing-588a0f4b7e1bd70c.rlib,libtracing_core-88c650b8d5db753e.rlib,libonce_cell-840615805104978c.rlib,libfutures_channel-b85f4c26e796651b.rlib,libhttpdate-90c7818154711529.rlib,libfutures_util-236745f3e8d789b6.rlib,libslab-736088d705d12a7f.rlib,libfutures_sink-cefd685dd40c0587.rlib,libfutures_task-7b521edd44f3c6b8.rlib,libpin_utils-cc741b4e75833186.rlib,libsync_wrapper-5d0d50a450411de6.rlib,libhttp_body-f05916ce8552549c.rlib,liblog-25b97e903af95274.rlib,libfutures_core-46a6763be7b7925b.rlib,liburl-af63e9385612faf2.rlib,libidna-4435aac523554b46.rlib,libutf8_iter-cb9c2719b0f3090f.rlib,libidna_adapter-60b980d494fcd946.rlib,libicu_properties-0851b06a39770ffb.rlib,libicu_properties_data-682649cda6e37e2e.rlib,libicu_normalizer-9a735fc311ce3cdd.rlib,libicu_normalizer_data-86a79f93aa6185f7.rlib,libicu_collections-b078858655d47682.rlib,libpotential_utf-f62bf2c1ab109143.rlib,libicu_provider-debef169b6894a8c.rlib,libicu_locale_core-70f1c120f8c2a3fb.rlib,libtinystr-0f8e5e909cbb1f59.rlib,liblitemap-b7e48f5ed5c66601.rlib,libwriteable-a39cc087fdca1414.rlib,libzerovec-509993085710959e.rlib,libzerotrie-a6ab29fff63d6080.rlib,libyoke-22b84f592ce28c58.rlib,libstable_deref_trait-b308ae4052b1d364.rlib,libzerofrom-acadf16119919749.rlib,libform_urlencoded-05348703df7376b8.rlib,libpercent_encoding-2df529d3cdada6b8.rlib,libhttp-171553c734e64f18.rlib,libitoa-02dabe3f520f938d.rlib,libfnv-9da3c947da7376f5.rlib,libuuid-c46dceec0f6a7f52.rlib,libgetrandom-87916d806657893b.rlib,libserde-3058191bb4f0ec31.rlib,libtokio-1afc7c4f706aac05.rlib,libsignal_hook_registry-ce5e1ddf8dbc5643.rlib,libsocket2-0d8d3c62a4546c73.rlib,libbytes-cf039259d3bed735.rlib,libmio-c6e24b365d57c40f.rlib,libparking_lot-d934e5f389afbdd5.rlib,libparking_lot_core-70679fe0c03500d6.rlib,liblibc-a43a41ff26920743.rlib,libcfg_if-192679fa2e74736f.rlib,libsmallvec-98fe09afb32a3e1c.rlib,liblock_api-5d15c9515522ac40.rlib,libscopeguard-efb4e517c02eaa9f.rlib,libpin_project_lite-293692b401dc9125.rlib,libanyhow-4e35b0344267e1ac.rlib}.rlib\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*}.rlib\" \"-lunwind\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/{libcfg_if-*,liblibc-*}.rlib\" \"-lc\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/{liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-L\" \"/var/folders/rr/vjcgxwp96zx2hb_sfv8lfbnh0000gn/T/rustchH3Y3E/raw-dylibs\" \"-Wl,-Bdynamic\" \"-Wl,--eh-frame-hdr\" \"-Wl,-z,noexecstack\" \"-nostartfiles\" \"-L\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/build/ring-4c621507ba1f9d7a/out\" \"-L\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/build/ring-309cbac9de3c61cf/out\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib\" \"-o\" \"/Users/<USER>/Downloads/\\xe7\\x94\\x9f\\xe6\\x88\\x90\\xe4\\xbb\\xa3\\xe7\\xa0\\x81/target/x86_64-unknown-linux-musl/debug/deps/controller-4b062c0aabb6d77a\" \"-Wl,--gc-sections\" \"-static\" \"-Wl,-z,relro,-z,now\" \"-nodefaultlibs\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crtend.o\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-musl/lib/self-contained/crtn.o\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: some arguments are omitted. use `--verbose` to show all linker arguments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: ld: unknown option: --as-needed\u001b[0m\n\u001b[0m          clang: error: linker command failed with exit code 1 (use -v to see invocation)\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 20 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error; 20 warnings emitted\u001b[0m\n\n"}
