<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `Cli` struct in crate `controller`."><title>Cli in controller - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="controller" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../static.files/storage-82c7156e.js"></script><script defer src="sidebar-items.js"></script><script defer src="../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc struct"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../controller/index.html">controller</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Cli</a></h2><h3><a href="#fields">Fields</a></h3><ul class="block structfield"><li><a href="#structfield.command" title="command">command</a></li><li><a href="#structfield.crypto_key" title="crypto_key">crypto_key</a></li><li><a href="#structfield.log_file" title="log_file">log_file</a></li><li><a href="#structfield.server_url" title="server_url">server_url</a></li></ul><h3><a href="#trait-implementations">Trait Implementations</a></h3><ul class="block trait-implementation"><li><a href="#impl-Args-for-Cli" title="Args">Args</a></li><li><a href="#impl-CommandFactory-for-Cli" title="CommandFactory">CommandFactory</a></li><li><a href="#impl-FromArgMatches-for-Cli" title="FromArgMatches">FromArgMatches</a></li><li><a href="#impl-Parser-for-Cli" title="Parser">Parser</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-Freeze-for-Cli" title="Freeze">Freeze</a></li><li><a href="#impl-RefUnwindSafe-for-Cli" title="RefUnwindSafe">RefUnwindSafe</a></li><li><a href="#impl-Send-for-Cli" title="Send">Send</a></li><li><a href="#impl-Sync-for-Cli" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-Cli" title="Unpin">Unpin</a></li><li><a href="#impl-UnwindSafe-for-Cli" title="UnwindSafe">UnwindSafe</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-AsTaggedExplicit%3C'a,+E%3E-for-T" title="AsTaggedExplicit&#60;&#39;a, E&#62;">AsTaggedExplicit&#60;&#39;a, E&#62;</a></li><li><a href="#impl-AsTaggedImplicit%3C'a,+E%3E-for-T" title="AsTaggedImplicit&#60;&#39;a, E&#62;">AsTaggedImplicit&#60;&#39;a, E&#62;</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-ErasedDestructor-for-T" title="ErasedDestructor">ErasedDestructor</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Instrument-for-T" title="Instrument">Instrument</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-Same-for-T" title="Same">Same</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li><li><a href="#impl-VZip%3CV%3E-for-T" title="VZip&#60;V&#62;">VZip&#60;V&#62;</a></li><li><a href="#impl-WithSubscriber-for-T" title="WithSubscriber">WithSubscriber</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="index.html">In crate controller</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="index.html">controller</a></div><h1>Struct <span class="struct">Cli</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/controller/main.rs.html#18-30">Source</a> </span></div><pre class="rust item-decl"><code>pub(crate) struct Cli {
    pub(crate) command: <a class="enum" href="enum.Commands.html" title="enum controller::Commands">Commands</a>,
    pub(crate) log_file: <a class="struct" href="https://doc.rust-lang.org/1.87.0/std/path/struct.PathBuf.html" title="struct std::path::PathBuf">PathBuf</a>,
    pub(crate) server_url: <a class="struct" href="https://doc.rust-lang.org/1.87.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>,
    pub(crate) crypto_key: <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.87.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>&gt;,
}</code></pre><h2 id="fields" class="fields section-header">Fields<a href="#fields" class="anchor">§</a></h2><span id="structfield.command" class="structfield section-header"><a href="#structfield.command" class="anchor field">§</a><code>command: <a class="enum" href="enum.Commands.html" title="enum controller::Commands">Commands</a></code></span><span id="structfield.log_file" class="structfield section-header"><a href="#structfield.log_file" class="anchor field">§</a><code>log_file: <a class="struct" href="https://doc.rust-lang.org/1.87.0/std/path/struct.PathBuf.html" title="struct std::path::PathBuf">PathBuf</a></code></span><span id="structfield.server_url" class="structfield section-header"><a href="#structfield.server_url" class="anchor field">§</a><code>server_url: <a class="struct" href="https://doc.rust-lang.org/1.87.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a></code></span><span id="structfield.crypto_key" class="structfield section-header"><a href="#structfield.crypto_key" class="anchor field">§</a><code>crypto_key: <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.87.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>&gt;</code></span><h2 id="trait-implementations" class="section-header">Trait Implementations<a href="#trait-implementations" class="anchor">§</a></h2><div id="trait-implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-Args-for-Cli" class="impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#impl-Args-for-Cli" class="anchor">§</a><h3 class="code-header">impl Args for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.group_id" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.group_id" class="anchor">§</a><h4 class="code-header">fn <a class="fn">group_id</a>() -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;Id&gt;</h4></section></summary><div class='docblock'>Report the [<code>ArgGroup::id</code>][crate::ArgGroup::id] for this set of arguments</div></details><details class="toggle method-toggle" open><summary><section id="method.augment_args" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.augment_args" class="anchor">§</a><h4 class="code-header">fn <a class="fn">augment_args</a>&lt;'b&gt;(__clap_app: Command) -&gt; Command</h4></section></summary><div class='docblock'>Append to [<code>Command</code>] so it can instantiate <code>Self</code> via
[<code>FromArgMatches::from_arg_matches_mut</code>] <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.augment_args_for_update" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.augment_args_for_update" class="anchor">§</a><h4 class="code-header">fn <a class="fn">augment_args_for_update</a>&lt;'b&gt;(__clap_app: Command) -&gt; Command</h4></section></summary><div class='docblock'>Append to [<code>Command</code>] so it can instantiate <code>self</code> via
[<code>FromArgMatches::update_from_arg_matches_mut</code>] <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-CommandFactory-for-Cli" class="impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#impl-CommandFactory-for-Cli" class="anchor">§</a><h3 class="code-header">impl CommandFactory for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.command" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.command" class="anchor">§</a><h4 class="code-header">fn <a class="fn">command</a>&lt;'b&gt;() -&gt; Command</h4></section></summary><div class='docblock'>Build a [<code>Command</code>] that can instantiate <code>Self</code>. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.command_for_update" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.command_for_update" class="anchor">§</a><h4 class="code-header">fn <a class="fn">command_for_update</a>&lt;'b&gt;() -&gt; Command</h4></section></summary><div class='docblock'>Build a [<code>Command</code>] that can update <code>self</code>. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-FromArgMatches-for-Cli" class="impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#impl-FromArgMatches-for-Cli" class="anchor">§</a><h3 class="code-header">impl FromArgMatches for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from_arg_matches" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.from_arg_matches" class="anchor">§</a><h4 class="code-header">fn <a class="fn">from_arg_matches</a>(__clap_arg_matches: &amp;ArgMatches) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, Error&gt;</h4></section></summary><div class='docblock'>Instantiate <code>Self</code> from [<code>ArgMatches</code>], parsing the arguments as needed. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.from_arg_matches_mut" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.from_arg_matches_mut" class="anchor">§</a><h4 class="code-header">fn <a class="fn">from_arg_matches_mut</a>(
    __clap_arg_matches: &amp;mut ArgMatches,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, Error&gt;</h4></section></summary><div class='docblock'>Instantiate <code>Self</code> from [<code>ArgMatches</code>], parsing the arguments as needed. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.update_from_arg_matches" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.update_from_arg_matches" class="anchor">§</a><h4 class="code-header">fn <a class="fn">update_from_arg_matches</a>(
    &amp;mut self,
    __clap_arg_matches: &amp;ArgMatches,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.unit.html">()</a>, Error&gt;</h4></section></summary><div class='docblock'>Assign values from <code>ArgMatches</code> to <code>self</code>.</div></details><details class="toggle method-toggle" open><summary><section id="method.update_from_arg_matches_mut" class="method trait-impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#method.update_from_arg_matches_mut" class="anchor">§</a><h4 class="code-header">fn <a class="fn">update_from_arg_matches_mut</a>(
    &amp;mut self,
    __clap_arg_matches: &amp;mut ArgMatches,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.unit.html">()</a>, Error&gt;</h4></section></summary><div class='docblock'>Assign values from <code>ArgMatches</code> to <code>self</code>.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Parser-for-Cli" class="impl"><a class="src rightside" href="../src/controller/main.rs.html#15">Source</a><a href="#impl-Parser-for-Cli" class="anchor">§</a><h3 class="code-header">impl Parser for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.parse" class="method trait-impl"><a href="#method.parse" class="anchor">§</a><h4 class="code-header">fn <a class="fn">parse</a>() -&gt; Self</h4></section></summary><div class='docblock'>Parse from <code>std::env::args_os()</code>, [exit][Error::exit] on error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_parse" class="method trait-impl"><a href="#method.try_parse" class="anchor">§</a><h4 class="code-header">fn <a class="fn">try_parse</a>() -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, Error&gt;</h4></section></summary><div class='docblock'>Parse from <code>std::env::args_os()</code>, return Err on error.</div></details><details class="toggle method-toggle" open><summary><section id="method.parse_from" class="method trait-impl"><a href="#method.parse_from" class="anchor">§</a><h4 class="code-header">fn <a class="fn">parse_from</a>&lt;I, T&gt;(itr: I) -&gt; Self<div class="where">where
    I: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/iter/traits/collect/trait.IntoIterator.html" title="trait core::iter::traits::collect::IntoIterator">IntoIterator</a>&lt;Item = T&gt;,
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.87.0/std/ffi/os_str/struct.OsString.html" title="struct std::ffi::os_str::OsString">OsString</a>&gt; + <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h4></section></summary><div class='docblock'>Parse from iterator, [exit][Error::exit] on error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_parse_from" class="method trait-impl"><a href="#method.try_parse_from" class="anchor">§</a><h4 class="code-header">fn <a class="fn">try_parse_from</a>&lt;I, T&gt;(itr: I) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, Error&gt;<div class="where">where
    I: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/iter/traits/collect/trait.IntoIterator.html" title="trait core::iter::traits::collect::IntoIterator">IntoIterator</a>&lt;Item = T&gt;,
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.87.0/std/ffi/os_str/struct.OsString.html" title="struct std::ffi::os_str::OsString">OsString</a>&gt; + <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h4></section></summary><div class='docblock'>Parse from iterator, return Err on error.</div></details><details class="toggle method-toggle" open><summary><section id="method.update_from" class="method trait-impl"><a href="#method.update_from" class="anchor">§</a><h4 class="code-header">fn <a class="fn">update_from</a>&lt;I, T&gt;(&amp;mut self, itr: I)<div class="where">where
    I: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/iter/traits/collect/trait.IntoIterator.html" title="trait core::iter::traits::collect::IntoIterator">IntoIterator</a>&lt;Item = T&gt;,
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.87.0/std/ffi/os_str/struct.OsString.html" title="struct std::ffi::os_str::OsString">OsString</a>&gt; + <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h4></section></summary><div class='docblock'>Update from iterator, [exit][Error::exit] on error. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.try_update_from" class="method trait-impl"><a href="#method.try_update_from" class="anchor">§</a><h4 class="code-header">fn <a class="fn">try_update_from</a>&lt;I, T&gt;(&amp;mut self, itr: I) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.unit.html">()</a>, Error&gt;<div class="where">where
    I: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/iter/traits/collect/trait.IntoIterator.html" title="trait core::iter::traits::collect::IntoIterator">IntoIterator</a>&lt;Item = T&gt;,
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.87.0/std/ffi/os_str/struct.OsString.html" title="struct std::ffi::os_str::OsString">OsString</a>&gt; + <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h4></section></summary><div class='docblock'>Update from iterator, return Err on error.</div></details></div></details></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-Cli" class="impl"><a href="#impl-Freeze-for-Cli" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section><section id="impl-RefUnwindSafe-for-Cli" class="impl"><a href="#impl-RefUnwindSafe-for-Cli" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section><section id="impl-Send-for-Cli" class="impl"><a href="#impl-Send-for-Cli" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section><section id="impl-Sync-for-Cli" class="impl"><a href="#impl-Sync-for-Cli" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section><section id="impl-Unpin-for-Cli" class="impl"><a href="#impl-Unpin-for-Cli" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section><section id="impl-UnwindSafe-for-Cli" class="impl"><a href="#impl-UnwindSafe-for-Cli" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="struct" href="struct.Cli.html" title="struct controller::Cli">Cli</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.87.0/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/1.87.0/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-AsTaggedExplicit%3C'a,+E%3E-for-T" class="impl"><a href="#impl-AsTaggedExplicit%3C'a,+E%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;'a, T, E&gt; AsTaggedExplicit&lt;'a, E&gt; for T<div class="where">where
    T: 'a,</div></h3></section></summary><div class="impl-items"><section id="method.explicit" class="method trait-impl"><a href="#method.explicit" class="anchor">§</a><h4 class="code-header">fn <a class="fn">explicit</a>(self, class: Class, tag: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u32.html">u32</a>) -&gt; TaggedParser&lt;'a, Explicit, Self, E&gt;</h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-AsTaggedImplicit%3C'a,+E%3E-for-T" class="impl"><a href="#impl-AsTaggedImplicit%3C'a,+E%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;'a, T, E&gt; AsTaggedImplicit&lt;'a, E&gt; for T<div class="where">where
    T: 'a,</div></h3></section></summary><div class="impl-items"><section id="method.implicit" class="method trait-impl"><a href="#method.implicit" class="anchor">§</a><h4 class="code-header">fn <a class="fn">implicit</a>(
    self,
    class: Class,
    constructed: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a>,
    tag: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u32.html">u32</a>,
) -&gt; TaggedParser&lt;'a, Implicit, Self, E&gt;</h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#767">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#770">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Instrument-for-T" class="impl"><a href="#impl-Instrument-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Instrument for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.instrument" class="method trait-impl"><a href="#method.instrument" class="anchor">§</a><h4 class="code-header">fn <a class="fn">instrument</a>(self, span: Span) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the provided [<code>Span</code>], returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.in_current_span" class="method trait-impl"><a href="#method.in_current_span" class="anchor">§</a><h4 class="code-header">fn <a class="fn">in_current_span</a>(self) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the <a href="super::Span::current()">current</a> <a href="crate::Span"><code>Span</code></a>, returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#750-752">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#760">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Same-for-T" class="impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#34">Source</a><a href="#impl-Same-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html" title="trait typenum::type_operators::Same">Same</a> for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Output" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#35">Source</a><a href="#associatedtype.Output" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html#associatedtype.Output" class="associatedtype">Output</a> = T</h4></section></summary><div class='docblock'>Should always be <code>Self</code></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#806-808">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#810">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#813">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#791-793">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#795">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#798">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-VZip%3CV%3E-for-T" class="impl"><a href="#impl-VZip%3CV%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;V, T&gt; VZip&lt;V&gt; for T<div class="where">where
    V: MultiLane&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><section id="method.vzip" class="method trait-impl"><a href="#method.vzip" class="anchor">§</a><h4 class="code-header">fn <a class="fn">vzip</a>(self) -&gt; V</h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-WithSubscriber-for-T" class="impl"><a href="#impl-WithSubscriber-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; WithSubscriber for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.with_subscriber" class="method trait-impl"><a href="#method.with_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_subscriber</a>&lt;S&gt;(self, subscriber: S) -&gt; WithDispatch&lt;Self&gt;<div class="where">where
    S: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;Dispatch&gt;,</div></h4></section></summary><div class='docblock'>Attaches the provided <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.with_current_subscriber" class="method trait-impl"><a href="#method.with_current_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_current_subscriber</a>(self) -&gt; WithDispatch&lt;Self&gt;</h4></section></summary><div class='docblock'>Attaches the current <a href="crate::dispatcher#setting-the-default-subscriber">default</a> <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details></div></details><section id="impl-ErasedDestructor-for-T" class="impl"><a href="#impl-ErasedDestructor-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; ErasedDestructor for T<div class="where">where
    T: 'static,</div></h3></section></div></section></div></main></body></html>