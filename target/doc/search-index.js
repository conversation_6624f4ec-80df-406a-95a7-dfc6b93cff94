var searchIndex = new Map(JSON.parse('[["agent",{"t":"FGPPPPCNNNNNNNNHHNNNNONOOHCCCNNNNNNNNOHHNNOHOCNNNNNNNHHNNNNNNFNNOOOONNNOONNNNONONNNONNNNFFNNNNNONNNOONNNONNNNNNNONONONNNNNNNNONNFNNNNNNNNNNNNNNNNNNPPFFGPFNNNNNNNNNNNNNNNNNNOONNNNNNNNNNNNNNNONONOOONNONOONNONNNNNNNNNNNNNNNNNFNNNNNNNNNNNNNNNNNNNNNNNNNN","n":["Cli","Commands","Install","Start","Status","Uninstall","agent","augment_args","augment_args_for_update","augment_subcommands","augment_subcommands_for_update","borrow","","borrow_mut","","check_launchd_status","check_status","clone","clone_into","clone_to_uninit","command","","command_for_update","crypto_key","daemon","daemonize","elf_loader","executor","file_manager","from","","from_arg_matches","","from_arg_matches_mut","","group_id","has_subcommand","heartbeat_interval","install_launchd_service","install_service","into","","log_file","main","server_url","system_info","to_owned","try_from","","try_into","","type_id","","uninstall_launchd_service","uninstall_service","update_from_arg_matches","","update_from_arg_matches_mut","","vzip","","Agent","borrow","borrow_mut","communicator","elf_loader","executor","file_manager","from","get_id","handle_message","heartbeat_interval","id","into","load_and_execute_elf","log_error","log_info","logger","new","running","send_initial_info","start","stop","system_info","try_from","try_into","type_id","vzip","ElfInfo","ElfLoader","analyze_elf","borrow","","borrow_mut","","class","cleanup_temp_file","create_temp_file","default","endianness","entry_point","execute_elf","execute_with_library","extract_strings","file_type","fmt","from","","into","","load_and_execute","load_shared_library","machine","new","os_abi","set_executable_permissions","temp_dir","try_from","","try_into","","type_id","","validate_elf_header","validate_shared_library","version","vzip","","CommandExecutor","borrow","borrow_mut","execute_command","execute_shell_script","from","get_process_info","into","is_command_allowed","kill_process","list_processes","manage_process","new","parse_ps_line","start_process","try_from","try_into","type_id","vzip","Directory","File","FileInfo","FileManager","FileType","Other","TempFileInfo","borrow","","","","borrow_mut","","","","cleanup_temp_files","clone","","clone_into","","clone_to_uninit","","copy_file","create_directory","delete_file","file_size","file_type","finalize_file_upload","fmt","","","from","","","","get_file_info","get_permissions","into","","","","list_directory","modified","move_file","name","new","path","","permissions","read_file_content","receive_file_chunk","received_chunks","send_file","size","temp_files","to_owned","","total_chunks","try_from","","","","try_into","","","","type_id","","","","vzip","","","","write_file_content","SystemInfoCollector","borrow","borrow_mut","collect","extract_ip_from_ifconfig_line","extract_ip_from_line","extract_mac_from_ifconfig_line","extract_mac_from_line","from","get_architecture","get_cpu_info","get_disk_info","get_hostname","get_interface_details","get_kernel_version","get_memory_available","get_memory_total","get_network_interfaces","get_os_version","into","new","parse_interface_line","parse_size","try_from","try_into","type_id","vzip"],"q":[[0,"agent"],[61,"agent::agent"],[88,"agent::elf_loader"],[128,"agent::executor"],[147,"agent::file_manager"],[222,"agent::system_info"],[249,"clap_builder::builder::command"],[250,"anyhow"],[251,"core::option"],[252,"clap_builder::parser::matches::arg_matches"],[253,"clap_builder"],[254,"core::result"],[255,"clap_builder::util::id"],[256,"std::path"],[257,"alloc::string"],[258,"core::any"],[259,"common::communication"],[260,"uuid"],[261,"common::protocol"],[262,"core::time"],[263,"common::logging"],[264,"alloc::vec"],[265,"core::fmt"],[266,"std::fs"],[267,"std::collections::hash::map"]],"i":"``l000`A`0110101``11100000````010101010``010`0`1010101``010101`Bh0000000000000000000000000``Bl0D`10011000111001010110101110101011010`Bn00000000000000000Ed0```0`C`EfEb32103203030322210210321032221032020210022120203121032103210321032`Cn0000000000000000000000000","f":"```````{bb}000{d{{d{c}}}{}}0{{{d{f}}}{{d{fc}}}{}}0{{}{{j{h}}}}0{{{d{l}}}l}{{d{d{fc}}}h{}}{{dn}h}{{}b}{A`Ab}10{A`Ad}6```{cc{}}0{{{d{Af}}}{{Aj{A`Ah}}}}{{{d{Af}}}{{Aj{lAh}}}}{{{d{fAf}}}{{Aj{A`Ah}}}}{{{d{fAf}}}{{Aj{lAh}}}}{{}{{Ab{Al}}}}{{{d{An}}}Ad}{A`B`}{{{d{A`}}}{{j{h}}}}0{{}c{}}0{A`Bb}{{}{{j{h}}}}{A`Bd}`{dc{}}{c{{Aj{e}}}{}{}}0{{}{{Aj{c}}}{}}0{dBf}055{{{d{fA`}}{d{Af}}}{{Aj{hAh}}}}{{{d{fl}}{d{Af}}}{{Aj{hAh}}}}{{{d{fA`}}{d{fAf}}}{{Aj{hAh}}}}{{{d{fl}}{d{fAf}}}{{Aj{hAh}}}}{{}c{}}0`{d{{d{c}}}{}}{{{d{f}}}{{d{fc}}}{}}{BhBj}{BhBl}{BhBn}{BhC`}{cc{}}{{{d{Bh}}}Cb}{{{d{fBh}}Cd}{{j{h}}}}{BhCf}{BhCb}{{}c{}}{{{d{fBh}}{d{{Ch{n}}}}{d{{Ch{Bd}}}}}{{j{Cj}}}}{{{d{Bh}}{d{An}}}{{j{h}}}}0{BhCl}{{Bd{d{{Ch{n}}}}ClB`}{{j{Bh}}}}{BhAd}{{{d{Bh}}}{{j{h}}}}{{{d{fBh}}}{{j{h}}}}{{{d{fBh}}}h}{BhCn}{c{{Aj{e}}}{}{}}{{}{{Aj{c}}}{}}{dBf}{{}c{}}``{{{d{Bl}}{d{{Ch{n}}}}}{{j{D`}}}}{d{{d{c}}}{}}0{{{d{f}}}{{d{fc}}}{}}0{D`Bd}{{{d{Bl}}{d{Bb}}}{{j{h}}}}{{{d{Bl}}{d{{Ch{n}}}}}{{j{Bb}}}}{{}D`}3{D`B`}{{{d{Bl}}{d{Bb}}{d{{Ch{Bd}}}}}{{j{Cj}}}}{{{d{Bl}}{d{{Ch{n}}}}{d{{Ch{n}}}}{d{{Ch{Bd}}}}}{{j{Cj}}}}{{{d{Bl}}{d{{Ch{n}}}}}{{j{{Db{Bd}}}}}}7{{{d{D`}}{d{fDd}}}Df}{cc{}}0{{}c{}}0{{{d{Bl}}{d{{Ch{n}}}}{d{{Ch{Bd}}}}}{{j{Cj}}}}9;{{}Bl}<;{BlBb}{c{{Aj{e}}}{}{}}0{{}{{Aj{c}}}{}}0{dBf}0{{{d{Bl}}{d{{Ch{n}}}}}{{j{h}}}}0{D`n}{{}c{}}0`{d{{d{c}}}{}}{{{d{f}}}{{d{fc}}}{}}{{{d{Bn}}{d{An}}{d{{Ch{Bd}}}}{Ab{{d{An}}}}{Ab{B`}}}{{j{Dh}}}}{{{d{Bn}}{d{An}}{Ab{B`}}}{{j{Dh}}}}>{{{d{Bn}}Dj}{{j{{Ab{Dl}}}}}}>{{{d{Bn}}{d{An}}}Ad}{{{d{Bn}}Dj}{{j{h}}}}{{{d{Bn}}}{{j{Dn}}}}{{{d{Bn}}E`}{{j{Dn}}}}{{}Bn}{{{d{Bn}}{d{An}}}{{Ab{Dl}}}}{{{d{Bn}}{d{An}}{d{{Ch{Bd}}}}}{{j{Dn}}}}{c{{Aj{e}}}{}{}}{{}{{Aj{c}}}{}}{dBf}?```````>>>>===={{{d{fC`}}}{{j{h}}}}{{{d{Eb}}}Eb}{{{d{Ed}}}Ed}{{d{d{fc}}}h{}}0{{dn}h}0{{{d{C`}}{d{An}}{d{An}}}{{j{h}}}}{{{d{C`}}{d{An}}}{{j{h}}}}0{EfB`}{EbEd}{{{d{fC`}}{d{An}}}{{j{h}}}}{{{d{Ef}}{d{fDd}}}Df}{{{d{Eb}}{d{fDd}}}Df}{{{d{Ed}}{d{fDd}}}Df}{cc{}}000{{{d{C`}}{d{An}}}{{j{Eb}}}}{{{d{C`}}{d{Eh}}}Bd}{{}c{}}000{{{d{C`}}{d{An}}}{{j{{Db{Eb}}}}}}{EbAb}={EbBd}{{}C`}{EfBb}22{{{d{C`}}{d{An}}{Ab{Ej}}}{{j{{Db{n}}}}}}{{{d{fC`}}{d{An}}DjDj{d{{Ch{n}}}}}{{j{h}}}}{EfDb}{{{d{C`}}{d{An}}Dj}{{j{{Db{El}}}}}}{EbB`}{C`En}{dc{}}0{EfDj}{c{{Aj{e}}}{}{}}000{{}{{Aj{c}}}{}}000{dBf}000{{}c{}}000{{{d{C`}}{d{An}}{d{{Ch{n}}}}}{{j{h}}}}`{d{{d{c}}}{}}{{{d{f}}}{{d{fc}}}{}}{{{d{Cn}}}{{j{F`}}}}{{{d{Cn}}{d{An}}}{{Ab{Bd}}}}000{cc{}}{{{d{Cn}}}{{j{Bd}}}}0{{{d{Cn}}}{{j{{Db{Fb}}}}}}1{{{d{Cn}}{d{An}}}{{j{Fd}}}}2{{{d{Cn}}}{{j{B`}}}}0{{{d{Cn}}}{{j{{Db{Fd}}}}}}4{{}c{}}{{}Cn}8{{{d{Cn}}{d{An}}}{{Ab{B`}}}}{c{{Aj{e}}}{}{}}{{}{{Aj{c}}}{}}{dBf}{{}c{}}","D":"Cd","p":[[5,"Command",249],[1,"reference",null,null,1],[0,"mut"],[1,"unit"],[8,"Result",250],[6,"Commands",0],[1,"u8"],[5,"Cli",0],[6,"Option",251,null,1],[1,"bool"],[5,"ArgMatches",252],[8,"Error",253],[6,"Result",254,null,1],[5,"Id",255],[1,"str"],[1,"u64"],[5,"PathBuf",256],[5,"String",257],[5,"TypeId",258],[5,"Agent",61],[5,"HttpCommunicator",259],[5,"ElfLoader",88],[5,"CommandExecutor",128],[5,"FileManager",147],[5,"Uuid",260],[5,"Message",261],[5,"Duration",262],[1,"slice"],[1,"i32"],[5,"Logger",263],[5,"SystemInfoCollector",222],[5,"ElfInfo",88],[5,"Vec",264],[5,"Formatter",265],[8,"Result",265],[5,"CommandResult",261],[1,"u32"],[5,"ProcessDetails",261],[5,"ProcessInfo",261],[6,"ProcessAction",261],[5,"FileInfo",147],[6,"FileType",147],[5,"TempFileInfo",147],[5,"Metadata",266],[1,"usize"],[5,"FileData",261],[5,"HashMap",267],[5,"SystemInfoResult",261],[5,"DiskInfo",261],[5,"NetworkInterface",261]],"r":[],"b":[],"c":"OjAAAAAAAAA=","e":"OzAAAAEAAN8ADQAAAAIABwAWACAACAArABkARgADAEsAHwBvABYAhwAAAIkAKQC3AAEAvQApAOgACQDzAAYA","P":[[11,"T"],[15,""],[18,"T"],[19,""],[29,"T"],[31,""],[40,"U"],[42,""],[46,"T"],[47,"U,T"],[49,"U"],[51,""],[59,"V"],[62,"T"],[64,""],[68,"T"],[69,""],[73,"U"],[74,""],[84,"U,T"],[85,"U"],[86,""],[87,"V"],[90,""],[91,"T"],[95,""],[106,"T"],[108,"U"],[110,""],[117,"U,T"],[119,"U"],[121,""],[126,"V"],[129,"T"],[131,""],[133,"T"],[134,""],[135,"U"],[136,""],[143,"U,T"],[144,"U"],[145,""],[146,"V"],[154,"T"],[162,""],[165,"T"],[167,""],[178,"T"],[182,""],[184,"U"],[188,""],[202,"T"],[204,""],[205,"U,T"],[209,"U"],[213,""],[217,"V"],[221,""],[223,"T"],[225,""],[230,"T"],[231,""],[241,"U"],[242,""],[245,"U,T"],[246,"U"],[247,""],[248,"V"]]}],["common",{"t":"CCCCCCPFFFPPPFFGPPHNNNNNNNNNNNNNNOONNNNNNNNNNNNNNNNOONNNNNNNNNNNNNONNNNNNONOOOONNNNNNOONNNONNNNNNNNNNNNNNNNNNOONNNNNNNFNNNNNNNNNNNNNNNNNNNFNNNNNNNNNNNNNNNNNNPPPPPPPPPGFGFPPPPNNNNNNNNONNNNNNNNNNNNONNNNNNNNONNNNONNNNNNONNNNOONNNNNNNNNNNNNNNONNNNPPFFPFPFFPPFPFPPPFGFGFFPPFPPFPOOOONNNNNNNNNNNNNNNNNNNNNNNNNNNNNNOOOONNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNOOOOONNNNNNNNNNNNNNNOOOOOOOOOOOONNNNNNNNNNNNNNNNNNNNNNNNNNNNNNOONNNNNNNNNNNNNNNOOOOOOOOOOOOONOOOOOONNNNNNNNNNNNNNNOOOOONNNNNNNNNNNNNNNOOONNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNOFFFNNNNNNONNNNNNNNONNNNNNNNNNONNOOOOONNNNNNNNNNNNNNNNN","n":["auth","communication","crypto","logging","protocol","tls","AdminAccess","AuthManager","AuthToken","ClientInfo","CommandExecution","FileDownload","FileUpload","LoginRequest","LoginResponse","Permission","ProcessManagement","SystemInfo","authenticate_user","borrow","","","","","","borrow_mut","","","","","","check_permission","cleanup_expired_tokens","client_info","client_version","clone","","","clone_into","","","clone_to_uninit","","","create_token","deserialize","","","","","eq","error_message","expires_at","fmt","","","","","","from","","","","","","get_active_sessions","hostname","into","","","","","","issued_at","new","os_version","password","permissions","","revoke_token","serialize","","","","","session_id","success","to_owned","","","token","try_from","","","","","","try_into","","","","","","type_id","","","","","","user_id","username","verify_token","vzip","","","","","","HttpCommunicator","borrow","borrow_mut","clone","clone_into","clone_to_uninit","fmt","from","get_session_id","into","new","receive_message","send_heartbeat","send_message","start_heartbeat_loop","to_owned","try_from","try_into","type_id","vzip","CryptoManager","borrow","borrow_mut","clone","clone_into","clone_to_uninit","decrypt","encrypt","fmt","from","generate_key","hash_data","into","new","to_owned","try_from","try_into","type_id","vzip","Authentication","Command","Communication","Critical","Debug","Error","","FileOperation","Info","LogCategory","LogEntry","LogLevel","Logger","ProcessManagement","Security","SystemInfo","Warning","borrow","","","","borrow_mut","","","","category","clone","","","clone_into","","","clone_to_uninit","","","deserialize","","","details","ensure_log_directory","fmt","","","from","","","","id","into","","","","level","log","log_authentication","log_command","log_error","log_file_operation","log_security_event","message","new","serialize","","","session_id","timestamp","to_owned","","","try_from","","","","try_into","","","","type_id","","","","user_id","vzip","","","","Ack","Command","CommandMessage","CommandResult","","DiskInfo","Error","ErrorMessage","FileData","","FileDownload","FileDownloadMessage","FileUpload","FileUploadMessage","Heartbeat","Kill","List","Message","MessageType","NetworkInterface","ProcessAction","ProcessDetails","ProcessInfo","","ProcessManagement","ProcessMessage","Start","SystemInfo","SystemInfoResult","","action","architecture","args","available_space","borrow","","","","","","","","","","","","","","","borrow_mut","","","","","","","","","","","","","","","checksum","chunk_index","","chunk_size","clone","","","","","","","","","","","","","","","clone_into","","","","","","","","","","","","","","","clone_to_uninit","","","","","","","","","","","","","","","command","cpu_info","cpu_usage","data","","deserialize","","","","","","","","","","","","","","","details","device","disk_info","error_code","error_message","execution_time","exit_code","file_path","","","file_size","filesystem","fmt","","","","","","","","","","","","","","","from","","","","","","","","","","","","","","","hostname","id","into","","","","","","","","","","","","","","","ip_addresses","is_complete","is_up","kernel_version","mac_address","memory_available","memory_total","memory_usage","message_type","mount_point","name","","network_interfaces","new","os_version","payload","pid","process_id","process_name","processes","serialize","","","","","","","","","","","","","","","status","stderr","stdout","timeout","timestamp","to_owned","","","","","","","","","","","","","","","total_chunks","","total_space","try_from","","","","","","","","","","","","","","","try_into","","","","","","","","","","","","","","","type_id","","","","","","","","","","","","","","","verify_checksum","vzip","","","","","","","","","","","","","","","working_dir","CertificateInfo","TlsConfig","TlsManager","borrow","","","borrow_mut","","","client_config","clone","clone_into","clone_to_uninit","create_client_config","create_client_config_with_cert","create_secure_client","create_self_signed_cert","create_server_config","fingerprint","fmt","from","","","get_certificate_info","get_client_config","get_server_config","into","","","issuer","new","","not_after","not_before","serial_number","server_config","subject","to_owned","try_from","","","try_into","","","type_id","","","validate_server_certificate","verify_certificate_chain","vzip","","","with_client_config","with_server_config"],"q":[[0,"common"],[6,"common::auth"],[118,"common::communication"],[138,"common::crypto"],[157,"common::logging"],[243,"common::protocol"],[555,"common::tls"],[609,"alloc::vec"],[610,"anyhow"],[611,"alloc::string"],[612,"core::result"],[613,"serde::de"],[614,"core::option"],[615,"core::fmt"],[616,"serde::ser"],[617,"uuid"],[618,"core::any"],[619,"core::time"],[620,"serde_json::value"],[621,"chrono::datetime"],[622,"rustls::client::client_conn"],[623,"alloc::sync"],[624,"reqwest::async_impl::client"],[625,"rustls::server::server_conn"],[626,"rustls::key"]],"i":"``````f```000```00`A`1nAfAhBb453210332145345345334521050445321045321031453210431240345210404530453210453210453210423453210`Cf000000000000000000`Cl00000000000000000D`00Db00110````1110DdCn2310230023023023023010231023010230111111010230002310231023102301023El0``0`0``00`0`0Dl0``````11`01`1DjDnE`EbCh62EnEdEjEh89F`Fb97FdFf8>:7654<=32;91086458>:7654<=32;9108>:7654<=32;9108>:7654<=32;910:;2648>:7654<=32;91009;0077654698>:7654<=32;9108>:7654<=32;910;88>:7654<=32;910141;1;;28921;8;82<<38>:7654<=32;910277:88>:7654<=32;9106498>:7654<=32;9108>:7654<=32;9108>:7654<=32;91088>:7654<=32;910:```FlGdFn210200022122002102112100210002002102102101221022","f":"``````````````````{{{d{b}}{d{b}}}{{j{{h{f}}}}}}{d{{d{c}}}{}}00000{{{d{l}}}{{d{lc}}}{}}00000{{{d{n}}{d{A`}}{d{f}}}Ab}{{{d{ln}}}{{j{Ad}}}}{AfAh}{AhAj}{{{d{A`}}}A`}{{{d{f}}}f}{{{d{n}}}n}{{d{d{lc}}}Ad{}}00{{dAl}Ad}00{{{d{ln}}Aj{h{f}}}{{j{Aj}}}}{c{{An{A`}}}B`}{c{{An{f}}}B`}{c{{An{Af}}}B`}{c{{An{Ah}}}B`}{c{{An{Bb}}}B`}{{{d{f}}{d{f}}}Ab}{BbBd}{A`Bf}{{{d{A`}}{d{lBh}}}Bj}{{{d{f}}{d{lBh}}}Bj}{{{d{n}}{d{lBh}}}Bj}{{{d{Af}}{d{lBh}}}Bj}{{{d{Ah}}{d{lBh}}}Bj}{{{d{Bb}}{d{lBh}}}Bj}{cc{}}00000{{{d{n}}}{{h{{Bl{AjA`}}}}}}{AhAj}{{}c{}}00000:{{{d{{Bn{Al}}}}Bf}{{j{n}}}}2{AfAj}{A`h}{Bbh}{{{d{ln}}{d{b}}}Ad}{{{d{A`}}c}AnC`}{{{d{f}}c}AnC`}{{{d{Af}}c}AnC`}{{{d{Ah}}c}AnC`}{{{d{Bb}}c}AnC`}{A`Cb}{BbAb}{dc{}}00{BbBd}{c{{An{e}}}{}{}}00000{{}{{An{c}}}{}}00000{dCd}00000{A`Aj}{AfAj}{{{d{n}}{d{b}}}{{j{A`}}}}{{}c{}}00000`{d{{d{c}}}{}}{{{d{l}}}{{d{lc}}}{}}{{{d{Cf}}}Cf}{{d{d{lc}}}Ad{}}{{dAl}Ad}{{{d{Cf}}{d{lBh}}}Bj}{cc{}}{{{d{Cf}}}Cb}{{}c{}}{{Aj{d{{Bn{Al}}}}}{{j{Cf}}}}{{{d{Cf}}}{{j{{Bd{Ch}}}}}}{{{d{Cf}}}{{j{Ad}}}}{{{d{Cf}}{d{Ch}}}{{j{{Bd{Ch}}}}}}{{{d{Cf}}Cj}Ad}{dc{}}{c{{An{e}}}{}{}}{{}{{An{c}}}{}}{dCd}{{}c{}}`{d{{d{c}}}{}}{{{d{l}}}{{d{lc}}}{}}{{{d{Cl}}}Cl}{{d{d{lc}}}Ad{}}{{dAl}Ad}{{{d{Cl}}{d{{Bn{Al}}}}}{{j{{h{Al}}}}}}0{{{d{Cl}}{d{lBh}}}Bj}{cc{}}{{}{{h{Al}}}}{{{d{{Bn{Al}}}}}Aj}{{}c{}}{{{d{{Bn{Al}}}}}{{j{Cl}}}}{dc{}}{c{{An{e}}}{}{}}{{}{{An{c}}}{}}{dCd}{{}c{}}`````````````````{d{{d{c}}}{}}000{{{d{l}}}{{d{lc}}}{}}000{CnD`}{{{d{Cn}}}Cn}{{{d{Db}}}Db}{{{d{D`}}}D`}{{d{d{lc}}}Ad{}}00{{dAl}Ad}00{c{{An{Cn}}}B`}{c{{An{Db}}}B`}{c{{An{D`}}}B`}{CnBd}{{{d{Dd}}}{{j{Ad}}}}{{{d{Cn}}{d{lBh}}}Bj}{{{d{Db}}{d{lBh}}}Bj}{{{d{D`}}{d{lBh}}}Bj}{cc{}}000{CnCb}{{}c{}}000{CnDb}{{{d{Dd}}Cn}{{j{Ad}}}}{{{d{Dd}}{d{b}}Ab{Bd{Df}}}{{j{Ad}}}}{{{d{Dd}}{d{b}}Cb{d{b}}Ab}{{j{Ad}}}}{{{d{Dd}}{d{b}}{Bd{Df}}}{{j{Ad}}}}{{{d{Dd}}{d{b}}Cb{d{b}}{d{b}}Ab}{{j{Ad}}}}1{CnAj}{AjDd}{{{d{Cn}}c}AnC`}{{{d{Db}}c}AnC`}{{{d{D`}}c}AnC`}{CnBd}{CnDh}{dc{}}00{c{{An{e}}}{}{}}000{{}{{An{c}}}{}}000{dCd}0005{{}c{}}000``````````````````````````````{DjDl}{DnAj}{E`h}{EbBf}{d{{d{c}}}{}}00000000000000{{{d{l}}}{{d{lc}}}{}}00000000000000{ChAj}{EdEf}{EhEf}{EjBd}{{{d{Ch}}}Ch}{{{d{El}}}El}{{{d{E`}}}E`}{{{d{En}}}En}{{{d{Ed}}}Ed}{{{d{Ej}}}Ej}{{{d{Eh}}}Eh}{{{d{Dj}}}Dj}{{{d{Dl}}}Dl}{{{d{F`}}}F`}{{{d{Fb}}}Fb}{{{d{Dn}}}Dn}{{{d{Eb}}}Eb}{{{d{Fd}}}Fd}{{{d{Ff}}}Ff}{{d{d{lc}}}Ad{}}00000000000000{{dAl}Ad}00000000000000{E`Aj}{DnAj}{FbFh}{Edh}{Ehh}{c{{An{Ch}}}B`}{c{{An{El}}}B`}{c{{An{E`}}}B`}{c{{An{En}}}B`}{c{{An{Ed}}}B`}{c{{An{Ej}}}B`}{c{{An{Eh}}}B`}{c{{An{Dj}}}B`}{c{{An{Dl}}}B`}{c{{An{F`}}}B`}{c{{An{Fb}}}B`}{c{{An{Dn}}}B`}{c{{An{Eb}}}B`}{c{{An{Fd}}}B`}{c{{An{Ff}}}B`}{FfBd}{EbAj}{Dnh}{FfEf}{FfAj}{EnBf}{EnFj}{EdAj}{EjAj}{EhAj}{EdBf}9{{{d{Ch}}{d{lBh}}}Bj}{{{d{El}}{d{lBh}}}Bj}{{{d{E`}}{d{lBh}}}Bj}{{{d{En}}{d{lBh}}}Bj}{{{d{Ed}}{d{lBh}}}Bj}{{{d{Ej}}{d{lBh}}}Bj}{{{d{Eh}}{d{lBh}}}Bj}{{{d{Dj}}{d{lBh}}}Bj}{{{d{Dl}}{d{lBh}}}Bj}{{{d{F`}}{d{lBh}}}Bj}{{{d{Fb}}{d{lBh}}}Bj}{{{d{Dn}}{d{lBh}}}Bj}{{{d{Eb}}{d{lBh}}}Bj}{{{d{Fd}}{d{lBh}}}Bj}{{{d{Ff}}{d{lBh}}}Bj}{cc{}}00000000000000{DnAj}{ChCb}{{}c{}}00000000000000{Fdh}{EhAb}{FdAb}5{FdAj}{DnBf}0{FbBf}{ChEl}{EbAj}{FbAj}5{Dnh}{ElCh}={Chh}{FbEf}{DjBd}0{F`h}{{{d{Ch}}c}AnC`}{{{d{El}}c}AnC`}{{{d{E`}}c}AnC`}{{{d{En}}c}AnC`}{{{d{Ed}}c}AnC`}{{{d{Ej}}c}AnC`}{{{d{Eh}}c}AnC`}{{{d{Dj}}c}AnC`}{{{d{Dl}}c}AnC`}{{{d{F`}}c}AnC`}{{{d{Fb}}c}AnC`}{{{d{Dn}}c}AnC`}{{{d{Eb}}c}AnC`}{{{d{Fd}}c}AnC`}{{{d{Ff}}c}AnC`}{FbAj}{EnAj}0{E`Bd}{ChDh}{dc{}}00000000000000{EdEf}{EhEf}{EbBf}{c{{An{e}}}{}{}}00000000000000{{}{{An{c}}}{}}00000000000000{dCd}00000000000000{{{d{Ch}}}Ab}{{}c{}}00000000000000:```{d{{d{c}}}{}}00{{{d{l}}}{{d{lc}}}{}}00{FlBd}{{{d{Fn}}}Fn}{{d{d{lc}}}Ad{}}{{dAl}Ad}{{}{{j{{Gb{G`}}}}}}{{{d{b}}{d{b}}}{{j{{Gb{G`}}}}}}{{{d{Gd}}{d{b}}}{{j{Gf}}}}{{}{{j{{Bl{{h{Al}}{h{Al}}}}}}}}{{{d{b}}{d{b}}}{{j{{Gb{Gh}}}}}}{FnAj}{{{d{Fn}}{d{lBh}}}Bj}{cc{}}00{{{d{Gj}}}{{j{Fn}}}}{{{d{Gd}}}{{Bd{{d{{Gb{G`}}}}}}}}{{{d{Gd}}}{{Bd{{d{{Gb{Gh}}}}}}}}{{}c{}}006{{}Fl}{FlGd}{FnGl}09{FlBd}:{dc{}}{c{{An{e}}}{}{}}00{{}{{An{c}}}{}}00{dCd}00{{{d{Gd}}{d{{Bn{Gj}}}}{d{b}}}{{j{Ad}}}}{{{d{{Bn{Gj}}}}}{{j{Ad}}}}{{}c{}}00{{Fl{Gb{G`}}}Fl}{{Fl{Gb{Gh}}}Fl}","D":"Gh","p":[[1,"str"],[1,"reference",null,null,1],[6,"Permission",6],[5,"Vec",609],[8,"Result",610],[0,"mut"],[5,"AuthManager",6],[5,"AuthToken",6],[1,"bool"],[1,"unit"],[5,"LoginRequest",6],[5,"ClientInfo",6],[5,"String",611],[1,"u8"],[6,"Result",612,null,1],[10,"Deserializer",613],[5,"LoginResponse",6],[6,"Option",614,null,1],[1,"u64"],[5,"Formatter",615],[8,"Result",615],[1,"tuple",null,null,1],[1,"slice"],[10,"Serializer",616],[5,"Uuid",617],[5,"TypeId",618],[5,"HttpCommunicator",118],[5,"Message",243],[5,"Duration",619],[5,"CryptoManager",138],[5,"LogEntry",157],[6,"LogCategory",157],[6,"LogLevel",157],[5,"Logger",157],[6,"Value",620],[5,"DateTime",621],[5,"ProcessMessage",243],[6,"ProcessAction",243],[5,"SystemInfoResult",243],[5,"CommandMessage",243],[5,"DiskInfo",243],[5,"FileUploadMessage",243],[1,"u32"],[5,"FileData",243],[5,"FileDownloadMessage",243],[6,"MessageType",243],[5,"CommandResult",243],[5,"ProcessInfo",243],[5,"ProcessDetails",243],[5,"NetworkInterface",243],[5,"ErrorMessage",243],[1,"f32"],[1,"i32"],[5,"TlsConfig",555],[5,"CertificateInfo",555],[5,"ClientConfig",622],[5,"Arc",623,null,1],[5,"TlsManager",555],[5,"Client",624],[5,"ServerConfig",625],[5,"Certificate",626],[1,"i64"]],"r":[],"b":[],"c":"OjAAAAAAAAA=","e":"OzAAAAEAACUCDQAAADsAQgABAEoAMwB/AAAAgQASAJUAAQCYADAAzQAAANIAwQCjAQEAtAGLAEMCAgBJAhgA","P":[[19,"T"],[31,""],[38,"T"],[41,""],[45,"__D"],[50,""],[59,"T"],[65,""],[67,"U"],[73,""],[80,"__S"],[85,""],[87,"T"],[90,""],[91,"U,T"],[97,"U"],[103,""],[112,"V"],[119,"T"],[121,""],[122,"T"],[123,""],[125,"T"],[126,""],[127,"U"],[128,""],[133,"T"],[134,"U,T"],[135,"U"],[136,""],[137,"V"],[139,"T"],[141,""],[142,"T"],[143,""],[147,"T"],[148,""],[150,"U"],[151,""],[152,"T"],[153,"U,T"],[154,"U"],[155,""],[156,"V"],[174,"T"],[182,""],[186,"T"],[189,""],[192,"__D"],[195,""],[200,"T"],[204,""],[205,"U"],[209,""],[218,"__S"],[221,""],[223,"T"],[226,"U,T"],[230,"U"],[234,""],[239,"V"],[273,""],[277,"T"],[307,""],[326,"T"],[341,""],[361,"__D"],[376,""],[403,"T"],[418,""],[420,"U"],[435,""],[455,"__S"],[470,""],[475,"T"],[490,""],[493,"U,T"],[508,"U"],[523,""],[539,"V"],[554,""],[558,"T"],[564,""],[566,"T"],[567,""],[575,"T"],[578,""],[581,"U"],[584,""],[592,"T"],[593,"U,T"],[596,"U"],[599,""],[604,"V"],[607,""]]}],["controller",{"t":"FGPPPPPNNNNNNNNNONCOCNNNNNNNNNNOHCONNNNNNCNNNNNNOOOOOOOFGPFPPPOONNNNNNNNNNNNOONNNNNNNNNNNOONNNOONONNONONNNNNNNNNNNNNNNNFONNNNNNNNNNNNNNNNFFFFFFOOONNNNNNNNNNNNNNNNNNNNNNNNNNNOONNNNNNNNNNNNNNNNNNNNNNOONNNNNNNNOOOOONOOOOONNNNNNNNNNOOOOONNNNNNNNNNNNNNNNNNNNNNNOOOONNNNNNHHHHHH","n":["Cli","Commands","Deploy","Execute","Interactive","List","Web","augment_args","augment_args_for_update","augment_subcommands","augment_subcommands_for_update","borrow","","borrow_mut","","command","","command_for_update","controller","crypto_key","deployment","from","","from_arg_matches","","from_arg_matches_mut","","group_id","has_subcommand","into","","log_file","main","reconnaissance","server_url","try_from","","try_into","","type_id","","ui","update_from_arg_matches","","update_from_arg_matches_mut","","vzip","","command","key_file","password","port","target","","username","AgentInfo","AgentStatus","Connecting","Controller","Error","Offline","Online","agents","auth_manager","borrow","","","borrow_mut","","","clone","","clone_into","","clone_to_uninit","","communicator","","deploy_agent","download_file","execute_command","find_agent_by_hostname","fmt","","","from","","","get_system_info","hostname","id","into","","","ip_address","last_seen","list_agents","logger","manage_process","new","os_version","start_heartbeat_monitoring","status","to_owned","","to_string","try_from","","","try_into","","","type_id","","","upload_file","vzip","","","DeploymentManager","agent_binary_path","borrow","borrow_mut","build_agent","deploy_via_ssh","deploy_via_web","execute_ssh_command","from","generate_deployment_script","generate_systemd_service","into","new","try_from","try_into","type_id","upload_file_via_scp","vzip","OsInfo","PortInfo","ReconnaissanceEngine","ServiceInfo","TargetInfo","VulnerabilityInfo","affected_service","architecture","banner","borrow","","","","","","borrow_mut","","","","","","clone","","","","","clone_into","","","","","clone_to_uninit","","","","","cve_id","description","deserialize","","","","","discover_hosts","export_csv","export_results","fmt","","","","","from","","","","","","get_all_targets","get_service_banner","get_target_info","hostname","id","identify_os","identify_services","into","","","","","","ip_address","kernel_version","last_scanned","name","","new","open_ports","os_info","port","","protocol","resolve_hostname","scan_host","scan_network","scan_ports","scan_vulnerabilities","serialize","","","","","service","services","severity","state","targets","to_owned","","","","","try_from","","","","","","try_into","","","","","","type_id","","","","","","version","","","vulnerabilities","vzip","","","","","","create_dashboard_html","handle_command","print_help","start_interactive_mode","start_simple_web_interface","start_web_interface"],"q":[[0,"controller"],[48,"controller::Commands"],[55,"controller::controller"],[119,"controller::deployment"],[137,"controller::reconnaissance"],[266,"controller::ui"],[272,"clap_builder::builder::command"],[273,"core::option"],[274,"clap_builder::parser::matches::arg_matches"],[275,"clap_builder"],[276,"core::result"],[277,"clap_builder::util::id"],[278,"std::path"],[279,"anyhow"],[280,"alloc::string"],[281,"core::any"],[282,"std::collections::hash::map"],[283,"common::auth"],[284,"common::communication"],[285,"common::protocol"],[286,"core::fmt"],[287,"uuid"],[288,"chrono::datetime"],[289,"alloc::vec"],[290,"common::logging"],[291,"serde::de"],[292,"core::net::ip_addr"],[293,"serde::ser"]],"i":"``j0000h0110101000`0`01010101010``0010101`010101BdBf0Bh121``Cd`000Bl00Cb21020202021011110221021001020011110100221021021021102`Dn0000000000000000``````E`EbEdElEf3Eh35214035140351403514035551403522214035214035222112221403514143211030222221403501502140352140352140352140354031214035``````","f":"```````{bb}000{d{{d{c}}}{}}0{{{d{f}}}{{d{fc}}}{}}0{{}b}{hj}1`{hl}`{cc{}}0{{{d{n}}}{{Ab{hA`}}}}{{{d{n}}}{{Ab{jA`}}}}{{{d{fn}}}{{Ab{hA`}}}}{{{d{fn}}}{{Ab{jA`}}}}{{}{{l{Ad}}}}{{{d{Af}}}Ah}{{}c{}}0{hAj}{{}{{An{Al}}}}`{hB`}{c{{Ab{e}}}{}{}}0{{}{{Ab{c}}}{}}0{dBb}0`{{{d{fh}}{d{n}}}{{Ab{AlA`}}}}{{{d{fj}}{d{n}}}{{Ab{AlA`}}}}{{{d{fh}}{d{fn}}}{{Ab{AlA`}}}}{{{d{fj}}{d{fn}}}{{Ab{AlA`}}}}{{}c{}}0{BdB`}{Bfl}0{BhBj}{BfB`}30```````{BlBn}{BlC`}{d{{d{c}}}{}}00{{{d{f}}}{{d{fc}}}{}}00{{{d{Cb}}}Cb}{{{d{Cd}}}Cd}{{d{d{fc}}}Al{}}0{{dCf}Al}0{BlCh}{CbCh}{{{d{fBl}}{d{Af}}{d{Af}}{l{{d{Af}}}}{l{{d{Af}}}}}{{An{Al}}}}{{{d{fBl}}{d{Af}}{d{Af}}{d{Af}}}{{An{Al}}}}{{{d{fBl}}{d{Af}}{d{Af}}}{{An{Cj}}}}{{{d{Bl}}{d{Af}}}{{l{{d{Cb}}}}}}{{{d{Cb}}{d{fCl}}}Cn}{{{d{Cd}}{d{fCl}}}Cn}0{cc{}}00{{{d{fBl}}{d{Af}}}{{An{D`}}}}{CbB`}{CbDb}{{}c{}}002{CbDd}{{{d{Bl}}}{{An{{Df{Cb}}}}}}{BlDh}{{{d{fBl}}{d{Af}}Dj}{{An{Al}}}}{{B`{d{{Dl{Cf}}}}Dh}{{An{Bl}}}}7{{{d{Bl}}}Al}{CbCd}{dc{}}0{dB`}{c{{Ab{e}}}{}{}}00{{}{{Ab{c}}}{}}00{dBb}00{{{d{fBl}}{d{Af}}{d{Af}}{d{Af}}}{{An{Al}}}}{{}c{}}00`{DnB`}{d{{d{c}}}{}}{{{d{f}}}{{d{fc}}}{}}{{{d{Dn}}{d{Af}}}{{An{B`}}}}{{{d{Dn}}{d{Af}}{d{Af}}{l{{d{Af}}}}{l{{d{Af}}}}}{{An{Al}}}}{{{d{Dn}}{d{Af}}{d{Af}}}{{An{Al}}}}{{{d{Dn}}{d{Af}}{d{Af}}{l{{d{Af}}}}}{{An{B`}}}}{cc{}}{{{d{Dn}}{d{Af}}{d{Af}}}{{An{B`}}}}{{{d{Dn}}}B`}{{}c{}}{B`Dn}{c{{Ab{e}}}{}{}}{{}{{Ab{c}}}{}}{dBb}{{{d{Dn}}{d{Af}}{d{Af}}{d{Af}}{l{{d{Af}}}}}{{An{Al}}}}{{}c{}}``````{E`B`}{EbB`}{Edl}{d{{d{c}}}{}}00000{{{d{f}}}{{d{fc}}}{}}00000{{{d{Ef}}}Ef}{{{d{Eb}}}Eb}{{{d{Eh}}}Eh}{{{d{Ed}}}Ed}{{{d{E`}}}E`}{{d{d{fc}}}Al{}}0000{{dCf}Al}0000;;{c{{Ab{Ef}}}Ej}{c{{Ab{Eb}}}Ej}{c{{Ab{Eh}}}Ej}{c{{Ab{Ed}}}Ej}{c{{Ab{E`}}}Ej}{{{d{El}}{d{Af}}}{{An{{Df{En}}}}}}{{{d{El}}}{{An{B`}}}}{{{d{El}}{d{Af}}}{{An{B`}}}}{{{d{Ef}}{d{fCl}}}Cn}{{{d{Eb}}{d{fCl}}}Cn}{{{d{Eh}}{d{fCl}}}Cn}{{{d{Ed}}{d{fCl}}}Cn}{{{d{E`}}{d{fCl}}}Cn}{cc{}}00000{{{d{El}}}{{Df{{d{Ef}}}}}}{{{d{El}}EnBj}{{An{B`}}}}{{{d{El}}En}{{l{{d{Ef}}}}}}{Efl}{EfDb}{{{d{El}}En}{{An{Eb}}}}{{{d{El}}En{d{{Dl{Eh}}}}}{{An{{Df{Ed}}}}}}{{}c{}}00000{EfEn}{EbB`}{EfDd}1{EdB`}{{}El}{EfDf}:{EhBj}{EdBj}{EhB`}{{{d{El}}En}{{An{B`}}}}{{{d{fEl}}En}{{An{Ef}}}}{{{d{fEl}}{d{Af}}}{{An{{Df{Ef}}}}}}{{{d{El}}En}{{An{{Df{Eh}}}}}}{{{d{El}}En{d{{Dl{Ed}}}}}{{An{{Df{E`}}}}}}{{{d{Ef}}c}AbF`}{{{d{Eb}}c}AbF`}{{{d{Eh}}c}AbF`}{{{d{Ed}}c}AbF`}{{{d{E`}}c}AbF`}{Ehl}>{E`B`}<{ElBn}{dc{}}0000{c{{Ab{e}}}{}{}}00000{{}{{Ab{c}}}{}}00000{dBb}00000{EbB`}7{EdB`}{EfDf}{{}c{}}00000{{}B`}{{{d{fBl}}{d{Af}}}{{An{Al}}}}{{}Al}{{{d{fBl}}}{{An{Al}}}}{{{d{fBl}}Bj}{{An{Al}}}}0","D":"Cj","p":[[5,"Command",272],[1,"reference",null,null,1],[0,"mut"],[5,"Cli",0],[6,"Commands",0],[6,"Option",273,null,1],[5,"ArgMatches",274],[8,"Error",275],[6,"Result",276,null,1],[5,"Id",277],[1,"str"],[1,"bool"],[5,"PathBuf",278],[1,"unit"],[8,"Result",279],[5,"String",280],[5,"TypeId",281],[15,"Execute",48],[15,"Deploy",48],[15,"Web",48],[1,"u16"],[5,"Controller",55],[5,"HashMap",282],[5,"AuthManager",283],[5,"AgentInfo",55],[6,"AgentStatus",55],[1,"u8"],[5,"HttpCommunicator",284],[5,"CommandResult",285],[5,"Formatter",286],[8,"Result",286],[5,"SystemInfoResult",285],[5,"Uuid",287],[5,"DateTime",288],[5,"Vec",289],[5,"Logger",290],[6,"ProcessAction",285],[1,"slice"],[5,"DeploymentManager",119],[5,"VulnerabilityInfo",137],[5,"OsInfo",137],[5,"ServiceInfo",137],[5,"TargetInfo",137],[5,"PortInfo",137],[10,"Deserializer",291],[5,"ReconnaissanceEngine",137],[6,"IpAddr",292],[10,"Serializer",293]],"r":[],"b":[[83,"impl-Debug-for-AgentStatus"],[84,"impl-Display-for-AgentStatus"]],"c":"OjAAAAAAAAA=","e":"OzAAAAEAAPMACgAAAAIACAANABgABQAgADUAWQACAF8AIACBAAEAhAA4AMMABgDQAEAA","P":[[11,"T"],[15,""],[21,"T"],[23,""],[29,"U"],[31,""],[35,"U,T"],[37,"U"],[39,""],[46,"V"],[48,""],[64,"T"],[70,""],[72,"T"],[74,""],[85,"T"],[88,""],[91,"U"],[94,""],[103,"T"],[105,""],[106,"U,T"],[109,"U"],[112,""],[116,"V"],[120,""],[121,"T"],[123,""],[127,"T"],[128,""],[130,"U"],[131,""],[132,"U,T"],[133,"U"],[134,""],[136,"V"],[143,""],[146,"T"],[158,""],[163,"T"],[168,""],[175,"__D"],[180,""],[188,"T"],[194,""],[201,"U"],[207,""],[223,"__S"],[228,""],[233,"T"],[238,"U,T"],[244,"U"],[250,""],[260,"V"],[266,""]]}]]'));
if (typeof exports !== 'undefined') exports.searchIndex = searchIndex;
else if (window.initSearch) window.initSearch(searchIndex);
//{"start":39,"fragment_lengths":[8267,12610,7916]}