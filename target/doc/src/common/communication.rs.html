<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `common/src/communication.rs`."><title>communication.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">common/</div>communication.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::crypto::CryptoManager;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::protocol::{Message, MessageType};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>base64::prelude::<span class="kw-2">*</span>;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>rand::Rng;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>reqwest::{
<a href=#7 id=7 data-nosnippet>7</a>    header::{HeaderMap, HeaderValue, CONTENT_TYPE, USER_AGENT},
<a href=#8 id=8 data-nosnippet>8</a>    Client,
<a href=#9 id=9 data-nosnippet>9</a>};
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">use </span>std::collections::HashMap;
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">use </span>std::time::Duration;
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">use </span>tokio::time::sleep;
<a href=#14 id=14 data-nosnippet>14</a><span class="kw">use </span>uuid::Uuid;
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a><span class="attr">#[derive(Debug, Clone)]
<a href=#17 id=17 data-nosnippet>17</a></span><span class="kw">pub struct </span>HttpCommunicator {
<a href=#18 id=18 data-nosnippet>18</a>    client: Client,
<a href=#19 id=19 data-nosnippet>19</a>    base_url: String,
<a href=#20 id=20 data-nosnippet>20</a>    session_id: Uuid,
<a href=#21 id=21 data-nosnippet>21</a>    crypto: CryptoManager,
<a href=#22 id=22 data-nosnippet>22</a>    user_agents: Vec&lt;String&gt;,
<a href=#23 id=23 data-nosnippet>23</a>    fake_endpoints: Vec&lt;String&gt;,
<a href=#24 id=24 data-nosnippet>24</a>}
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#27 id=27 data-nosnippet>27</a></span><span class="kw">struct </span>HttpRequest {
<a href=#28 id=28 data-nosnippet>28</a>    session_id: String,
<a href=#29 id=29 data-nosnippet>29</a>    data: String, <span class="comment">// Base64编码的加密数据
<a href=#30 id=30 data-nosnippet>30</a>    </span>timestamp: i64,
<a href=#31 id=31 data-nosnippet>31</a>    fake_params: HashMap&lt;String, String&gt;,
<a href=#32 id=32 data-nosnippet>32</a>}
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#35 id=35 data-nosnippet>35</a></span><span class="kw">struct </span>HttpResponse {
<a href=#36 id=36 data-nosnippet>36</a>    status: String,
<a href=#37 id=37 data-nosnippet>37</a>    data: <span class="prelude-ty">Option</span>&lt;String&gt;, <span class="comment">// Base64编码的加密数据
<a href=#38 id=38 data-nosnippet>38</a>    </span>timestamp: i64,
<a href=#39 id=39 data-nosnippet>39</a>    fake_content: String,
<a href=#40 id=40 data-nosnippet>40</a>}
<a href=#41 id=41 data-nosnippet>41</a>
<a href=#42 id=42 data-nosnippet>42</a><span class="kw">impl </span>HttpCommunicator {
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub fn </span>new(base_url: String, crypto_key: <span class="kw-2">&amp;</span>[u8]) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#44 id=44 data-nosnippet>44</a>        <span class="kw">let </span><span class="kw-2">mut </span>headers = HeaderMap::new();
<a href=#45 id=45 data-nosnippet>45</a>        headers.insert(CONTENT_TYPE, HeaderValue::from_static(<span class="string">"application/json"</span>));
<a href=#46 id=46 data-nosnippet>46</a>
<a href=#47 id=47 data-nosnippet>47</a>        <span class="kw">let </span>client = Client::builder()
<a href=#48 id=48 data-nosnippet>48</a>            .timeout(Duration::from_secs(<span class="number">30</span>))
<a href=#49 id=49 data-nosnippet>49</a>            .default_headers(headers)
<a href=#50 id=50 data-nosnippet>50</a>            .build()<span class="question-mark">?</span>;
<a href=#51 id=51 data-nosnippet>51</a>
<a href=#52 id=52 data-nosnippet>52</a>        <span class="kw">let </span>user_agents = <span class="macro">vec!</span>[
<a href=#53 id=53 data-nosnippet>53</a>            <span class="string">"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"</span>.to_string(),
<a href=#54 id=54 data-nosnippet>54</a>            <span class="string">"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"</span>.to_string(),
<a href=#55 id=55 data-nosnippet>55</a>            <span class="string">"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"</span>.to_string(),
<a href=#56 id=56 data-nosnippet>56</a>        ];
<a href=#57 id=57 data-nosnippet>57</a>
<a href=#58 id=58 data-nosnippet>58</a>        <span class="kw">let </span>fake_endpoints = <span class="macro">vec!</span>[
<a href=#59 id=59 data-nosnippet>59</a>            <span class="string">"/api/v1/status"</span>.to_string(),
<a href=#60 id=60 data-nosnippet>60</a>            <span class="string">"/api/v1/health"</span>.to_string(),
<a href=#61 id=61 data-nosnippet>61</a>            <span class="string">"/api/v1/metrics"</span>.to_string(),
<a href=#62 id=62 data-nosnippet>62</a>            <span class="string">"/api/v1/config"</span>.to_string(),
<a href=#63 id=63 data-nosnippet>63</a>        ];
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#66 id=66 data-nosnippet>66</a>            client,
<a href=#67 id=67 data-nosnippet>67</a>            base_url,
<a href=#68 id=68 data-nosnippet>68</a>            session_id: Uuid::new_v4(),
<a href=#69 id=69 data-nosnippet>69</a>            crypto: CryptoManager::new(crypto_key)<span class="question-mark">?</span>,
<a href=#70 id=70 data-nosnippet>70</a>            user_agents,
<a href=#71 id=71 data-nosnippet>71</a>            fake_endpoints,
<a href=#72 id=72 data-nosnippet>72</a>        })
<a href=#73 id=73 data-nosnippet>73</a>    }
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a>    <span class="kw">pub async fn </span>send_message(<span class="kw-2">&amp;</span><span class="self">self</span>, message: <span class="kw-2">&amp;</span>Message) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;Message&gt;&gt; {
<a href=#76 id=76 data-nosnippet>76</a>        <span class="comment">// 序列化并加密消息
<a href=#77 id=77 data-nosnippet>77</a>        </span><span class="kw">let </span>serialized = serde_json::to_vec(message)<span class="question-mark">?</span>;
<a href=#78 id=78 data-nosnippet>78</a>        <span class="kw">let </span>encrypted = <span class="self">self</span>.crypto.encrypt(<span class="kw-2">&amp;</span>serialized)<span class="question-mark">?</span>;
<a href=#79 id=79 data-nosnippet>79</a>        <span class="kw">let </span>encoded = BASE64_STANDARD.encode(<span class="kw-2">&amp;</span>encrypted);
<a href=#80 id=80 data-nosnippet>80</a>
<a href=#81 id=81 data-nosnippet>81</a>        <span class="comment">// 创建伪装的HTTP请求
<a href=#82 id=82 data-nosnippet>82</a>        </span><span class="kw">let </span>fake_params = <span class="self">self</span>.generate_fake_params();
<a href=#83 id=83 data-nosnippet>83</a>        <span class="kw">let </span>request = HttpRequest {
<a href=#84 id=84 data-nosnippet>84</a>            session_id: <span class="self">self</span>.session_id.to_string(),
<a href=#85 id=85 data-nosnippet>85</a>            data: encoded,
<a href=#86 id=86 data-nosnippet>86</a>            timestamp: chrono::Utc::now().timestamp(),
<a href=#87 id=87 data-nosnippet>87</a>            fake_params,
<a href=#88 id=88 data-nosnippet>88</a>        };
<a href=#89 id=89 data-nosnippet>89</a>
<a href=#90 id=90 data-nosnippet>90</a>        <span class="comment">// 选择随机的用户代理和端点
<a href=#91 id=91 data-nosnippet>91</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>rng = rand::rngs::OsRng;
<a href=#92 id=92 data-nosnippet>92</a>        <span class="kw">let </span>user_agent = <span class="kw-2">&amp;</span><span class="self">self</span>.user_agents[rng.gen_range(<span class="number">0</span>..<span class="self">self</span>.user_agents.len())];
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">let </span>endpoint = <span class="kw-2">&amp;</span><span class="self">self</span>.fake_endpoints[rng.gen_range(<span class="number">0</span>..<span class="self">self</span>.fake_endpoints.len())];
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">let </span>url = <span class="macro">format!</span>(<span class="string">"{}{}"</span>, <span class="self">self</span>.base_url, endpoint);
<a href=#95 id=95 data-nosnippet>95</a>
<a href=#96 id=96 data-nosnippet>96</a>        <span class="comment">// 发送HTTP请求
<a href=#97 id=97 data-nosnippet>97</a>        </span><span class="kw">let </span>response = <span class="self">self
<a href=#98 id=98 data-nosnippet>98</a>            </span>.client
<a href=#99 id=99 data-nosnippet>99</a>            .post(<span class="kw-2">&amp;</span>url)
<a href=#100 id=100 data-nosnippet>100</a>            .header(USER_AGENT, user_agent)
<a href=#101 id=101 data-nosnippet>101</a>            .json(<span class="kw-2">&amp;</span>request)
<a href=#102 id=102 data-nosnippet>102</a>            .send()
<a href=#103 id=103 data-nosnippet>103</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>        <span class="kw">if </span>!response.status().is_success() {
<a href=#106 id=106 data-nosnippet>106</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"HTTP request failed: {}"</span>, response.status()));
<a href=#107 id=107 data-nosnippet>107</a>        }
<a href=#108 id=108 data-nosnippet>108</a>
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">let </span>http_response: HttpResponse = response.json().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>        <span class="comment">// 解密响应数据
<a href=#112 id=112 data-nosnippet>112</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(data) = http_response.data {
<a href=#113 id=113 data-nosnippet>113</a>            <span class="kw">let </span>decoded = BASE64_STANDARD.decode(<span class="kw-2">&amp;</span>data)<span class="question-mark">?</span>;
<a href=#114 id=114 data-nosnippet>114</a>            <span class="kw">let </span>decrypted = <span class="self">self</span>.crypto.decrypt(<span class="kw-2">&amp;</span>decoded)<span class="question-mark">?</span>;
<a href=#115 id=115 data-nosnippet>115</a>            <span class="kw">let </span>message: Message = serde_json::from_slice(<span class="kw-2">&amp;</span>decrypted)<span class="question-mark">?</span>;
<a href=#116 id=116 data-nosnippet>116</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(message))
<a href=#117 id=117 data-nosnippet>117</a>        } <span class="kw">else </span>{
<a href=#118 id=118 data-nosnippet>118</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#119 id=119 data-nosnippet>119</a>        }
<a href=#120 id=120 data-nosnippet>120</a>    }
<a href=#121 id=121 data-nosnippet>121</a>
<a href=#122 id=122 data-nosnippet>122</a>    <span class="kw">pub async fn </span>receive_message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;Message&gt;&gt; {
<a href=#123 id=123 data-nosnippet>123</a>        <span class="comment">// 模拟轮询接收消息
<a href=#124 id=124 data-nosnippet>124</a>        </span><span class="kw">let </span>endpoint = <span class="string">"/api/v1/poll"</span>;
<a href=#125 id=125 data-nosnippet>125</a>        <span class="kw">let </span>url = <span class="macro">format!</span>(<span class="string">"{}{}"</span>, <span class="self">self</span>.base_url, endpoint);
<a href=#126 id=126 data-nosnippet>126</a>
<a href=#127 id=127 data-nosnippet>127</a>        <span class="kw">let </span>fake_params = <span class="self">self</span>.generate_fake_params();
<a href=#128 id=128 data-nosnippet>128</a>        <span class="kw">let </span>request = HttpRequest {
<a href=#129 id=129 data-nosnippet>129</a>            session_id: <span class="self">self</span>.session_id.to_string(),
<a href=#130 id=130 data-nosnippet>130</a>            data: String::new(),
<a href=#131 id=131 data-nosnippet>131</a>            timestamp: chrono::Utc::now().timestamp(),
<a href=#132 id=132 data-nosnippet>132</a>            fake_params,
<a href=#133 id=133 data-nosnippet>133</a>        };
<a href=#134 id=134 data-nosnippet>134</a>
<a href=#135 id=135 data-nosnippet>135</a>        <span class="kw">let </span><span class="kw-2">mut </span>rng = rand::rngs::OsRng;
<a href=#136 id=136 data-nosnippet>136</a>        <span class="kw">let </span>user_agent = <span class="kw-2">&amp;</span><span class="self">self</span>.user_agents[rng.gen_range(<span class="number">0</span>..<span class="self">self</span>.user_agents.len())];
<a href=#137 id=137 data-nosnippet>137</a>
<a href=#138 id=138 data-nosnippet>138</a>        <span class="kw">let </span>response = <span class="self">self
<a href=#139 id=139 data-nosnippet>139</a>            </span>.client
<a href=#140 id=140 data-nosnippet>140</a>            .post(<span class="kw-2">&amp;</span>url)
<a href=#141 id=141 data-nosnippet>141</a>            .header(USER_AGENT, user_agent)
<a href=#142 id=142 data-nosnippet>142</a>            .json(<span class="kw-2">&amp;</span>request)
<a href=#143 id=143 data-nosnippet>143</a>            .send()
<a href=#144 id=144 data-nosnippet>144</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#145 id=145 data-nosnippet>145</a>
<a href=#146 id=146 data-nosnippet>146</a>        <span class="kw">if </span>!response.status().is_success() {
<a href=#147 id=147 data-nosnippet>147</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>);
<a href=#148 id=148 data-nosnippet>148</a>        }
<a href=#149 id=149 data-nosnippet>149</a>
<a href=#150 id=150 data-nosnippet>150</a>        <span class="kw">let </span>http_response: HttpResponse = response.json().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(data) = http_response.data {
<a href=#153 id=153 data-nosnippet>153</a>            <span class="kw">let </span>decoded = BASE64_STANDARD.decode(<span class="kw-2">&amp;</span>data)<span class="question-mark">?</span>;
<a href=#154 id=154 data-nosnippet>154</a>            <span class="kw">let </span>decrypted = <span class="self">self</span>.crypto.decrypt(<span class="kw-2">&amp;</span>decoded)<span class="question-mark">?</span>;
<a href=#155 id=155 data-nosnippet>155</a>            <span class="kw">let </span>message: Message = serde_json::from_slice(<span class="kw-2">&amp;</span>decrypted)<span class="question-mark">?</span>;
<a href=#156 id=156 data-nosnippet>156</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(message))
<a href=#157 id=157 data-nosnippet>157</a>        } <span class="kw">else </span>{
<a href=#158 id=158 data-nosnippet>158</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#159 id=159 data-nosnippet>159</a>        }
<a href=#160 id=160 data-nosnippet>160</a>    }
<a href=#161 id=161 data-nosnippet>161</a>
<a href=#162 id=162 data-nosnippet>162</a>    <span class="kw">pub async fn </span>send_heartbeat(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#163 id=163 data-nosnippet>163</a>        <span class="kw">let </span>heartbeat = Message::new(MessageType::Heartbeat);
<a href=#164 id=164 data-nosnippet>164</a>        <span class="self">self</span>.send_message(<span class="kw-2">&amp;</span>heartbeat).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#165 id=165 data-nosnippet>165</a>        <span class="prelude-val">Ok</span>(())
<a href=#166 id=166 data-nosnippet>166</a>    }
<a href=#167 id=167 data-nosnippet>167</a>
<a href=#168 id=168 data-nosnippet>168</a>    <span class="kw">pub async fn </span>start_heartbeat_loop(<span class="kw-2">&amp;</span><span class="self">self</span>, interval: Duration) {
<a href=#169 id=169 data-nosnippet>169</a>        <span class="kw">loop </span>{
<a href=#170 id=170 data-nosnippet>170</a>            <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = <span class="self">self</span>.send_heartbeat().<span class="kw">await </span>{
<a href=#171 id=171 data-nosnippet>171</a>                <span class="macro">log::error!</span>(<span class="string">"Failed to send heartbeat: {}"</span>, e);
<a href=#172 id=172 data-nosnippet>172</a>            }
<a href=#173 id=173 data-nosnippet>173</a>            sleep(interval).<span class="kw">await</span>;
<a href=#174 id=174 data-nosnippet>174</a>        }
<a href=#175 id=175 data-nosnippet>175</a>    }
<a href=#176 id=176 data-nosnippet>176</a>
<a href=#177 id=177 data-nosnippet>177</a>    <span class="kw">fn </span>generate_fake_params(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; HashMap&lt;String, String&gt; {
<a href=#178 id=178 data-nosnippet>178</a>        <span class="kw">let </span><span class="kw-2">mut </span>params = HashMap::new();
<a href=#179 id=179 data-nosnippet>179</a>        params.insert(<span class="string">"version"</span>.to_string(), <span class="string">"1.0"</span>.to_string());
<a href=#180 id=180 data-nosnippet>180</a>        params.insert(<span class="string">"client"</span>.to_string(), <span class="string">"web"</span>.to_string());
<a href=#181 id=181 data-nosnippet>181</a>        params.insert(
<a href=#182 id=182 data-nosnippet>182</a>            <span class="string">"timestamp"</span>.to_string(),
<a href=#183 id=183 data-nosnippet>183</a>            chrono::Utc::now().timestamp().to_string(),
<a href=#184 id=184 data-nosnippet>184</a>        );
<a href=#185 id=185 data-nosnippet>185</a>        params.insert(<span class="string">"nonce"</span>.to_string(), Uuid::new_v4().to_string());
<a href=#186 id=186 data-nosnippet>186</a>        params
<a href=#187 id=187 data-nosnippet>187</a>    }
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a>    <span class="kw">pub fn </span>get_session_id(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Uuid {
<a href=#190 id=190 data-nosnippet>190</a>        <span class="self">self</span>.session_id
<a href=#191 id=191 data-nosnippet>191</a>    }
<a href=#192 id=192 data-nosnippet>192</a>}</code></pre></div></section></main></body></html>