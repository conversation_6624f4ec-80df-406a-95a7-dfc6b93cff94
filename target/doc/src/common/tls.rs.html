<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `common/src/tls.rs`."><title>tls.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">common/</div>tls.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>rustls::{Certificate, ClientConfig, PrivateKey, ServerConfig};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>rustls_pemfile::{certs, pkcs8_private_keys};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::fs::File;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::io::BufReader;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::sync::Arc;
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub struct </span>TlsConfig {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>client_config: <span class="prelude-ty">Option</span>&lt;Arc&lt;ClientConfig&gt;&gt;,
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>server_config: <span class="prelude-ty">Option</span>&lt;Arc&lt;ServerConfig&gt;&gt;,
<a href=#11 id=11 data-nosnippet>11</a>}
<a href=#12 id=12 data-nosnippet>12</a>
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">impl </span>TlsConfig {
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#15 id=15 data-nosnippet>15</a>        <span class="self">Self </span>{
<a href=#16 id=16 data-nosnippet>16</a>            client_config: <span class="prelude-val">None</span>,
<a href=#17 id=17 data-nosnippet>17</a>            server_config: <span class="prelude-val">None</span>,
<a href=#18 id=18 data-nosnippet>18</a>        }
<a href=#19 id=19 data-nosnippet>19</a>    }
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub fn </span>with_client_config(<span class="kw-2">mut </span><span class="self">self</span>, config: Arc&lt;ClientConfig&gt;) -&gt; <span class="self">Self </span>{
<a href=#22 id=22 data-nosnippet>22</a>        <span class="self">self</span>.client_config = <span class="prelude-val">Some</span>(config);
<a href=#23 id=23 data-nosnippet>23</a>        <span class="self">self
<a href=#24 id=24 data-nosnippet>24</a>    </span>}
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a>    <span class="kw">pub fn </span>with_server_config(<span class="kw-2">mut </span><span class="self">self</span>, config: Arc&lt;ServerConfig&gt;) -&gt; <span class="self">Self </span>{
<a href=#27 id=27 data-nosnippet>27</a>        <span class="self">self</span>.server_config = <span class="prelude-val">Some</span>(config);
<a href=#28 id=28 data-nosnippet>28</a>        <span class="self">self
<a href=#29 id=29 data-nosnippet>29</a>    </span>}
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub fn </span>create_client_config() -&gt; <span class="prelude-ty">Result</span>&lt;Arc&lt;ClientConfig&gt;&gt; {
<a href=#32 id=32 data-nosnippet>32</a>        <span class="kw">let </span><span class="kw-2">mut </span>root_store = rustls::RootCertStore::empty();
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a>        <span class="comment">// 添加系统根证书
<a href=#35 id=35 data-nosnippet>35</a>        </span>root_store.add_trust_anchors(webpki_roots::TLS_SERVER_ROOTS.<span class="number">0</span>.iter().map(|ta| {
<a href=#36 id=36 data-nosnippet>36</a>            rustls::OwnedTrustAnchor::from_subject_spki_name_constraints(
<a href=#37 id=37 data-nosnippet>37</a>                ta.subject,
<a href=#38 id=38 data-nosnippet>38</a>                ta.spki,
<a href=#39 id=39 data-nosnippet>39</a>                ta.name_constraints,
<a href=#40 id=40 data-nosnippet>40</a>            )
<a href=#41 id=41 data-nosnippet>41</a>        }));
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a>        <span class="kw">let </span>config = ClientConfig::builder()
<a href=#44 id=44 data-nosnippet>44</a>            .with_safe_defaults()
<a href=#45 id=45 data-nosnippet>45</a>            .with_root_certificates(root_store)
<a href=#46 id=46 data-nosnippet>46</a>            .with_no_client_auth();
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a>        <span class="prelude-val">Ok</span>(Arc::new(config))
<a href=#49 id=49 data-nosnippet>49</a>    }
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub fn </span>create_client_config_with_cert(
<a href=#52 id=52 data-nosnippet>52</a>        cert_path: <span class="kw-2">&amp;</span>str,
<a href=#53 id=53 data-nosnippet>53</a>        key_path: <span class="kw-2">&amp;</span>str,
<a href=#54 id=54 data-nosnippet>54</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;Arc&lt;ClientConfig&gt;&gt; {
<a href=#55 id=55 data-nosnippet>55</a>        <span class="kw">let </span><span class="kw-2">mut </span>root_store = rustls::RootCertStore::empty();
<a href=#56 id=56 data-nosnippet>56</a>
<a href=#57 id=57 data-nosnippet>57</a>        <span class="comment">// 添加系统根证书
<a href=#58 id=58 data-nosnippet>58</a>        </span>root_store.add_trust_anchors(webpki_roots::TLS_SERVER_ROOTS.<span class="number">0</span>.iter().map(|ta| {
<a href=#59 id=59 data-nosnippet>59</a>            rustls::OwnedTrustAnchor::from_subject_spki_name_constraints(
<a href=#60 id=60 data-nosnippet>60</a>                ta.subject,
<a href=#61 id=61 data-nosnippet>61</a>                ta.spki,
<a href=#62 id=62 data-nosnippet>62</a>                ta.name_constraints,
<a href=#63 id=63 data-nosnippet>63</a>            )
<a href=#64 id=64 data-nosnippet>64</a>        }));
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a>        <span class="comment">// 加载客户端证书和私钥
<a href=#67 id=67 data-nosnippet>67</a>        </span><span class="kw">let </span>cert_file = File::open(cert_path)<span class="question-mark">?</span>;
<a href=#68 id=68 data-nosnippet>68</a>        <span class="kw">let </span><span class="kw-2">mut </span>cert_reader = BufReader::new(cert_file);
<a href=#69 id=69 data-nosnippet>69</a>        <span class="kw">let </span>cert_chain = certs(<span class="kw-2">&amp;mut </span>cert_reader)<span class="question-mark">?
<a href=#70 id=70 data-nosnippet>70</a>            </span>.into_iter()
<a href=#71 id=71 data-nosnippet>71</a>            .map(Certificate)
<a href=#72 id=72 data-nosnippet>72</a>            .collect();
<a href=#73 id=73 data-nosnippet>73</a>
<a href=#74 id=74 data-nosnippet>74</a>        <span class="kw">let </span>key_file = File::open(key_path)<span class="question-mark">?</span>;
<a href=#75 id=75 data-nosnippet>75</a>        <span class="kw">let </span><span class="kw-2">mut </span>key_reader = BufReader::new(key_file);
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw">let </span><span class="kw-2">mut </span>keys = pkcs8_private_keys(<span class="kw-2">&amp;mut </span>key_reader)<span class="question-mark">?</span>;
<a href=#77 id=77 data-nosnippet>77</a>
<a href=#78 id=78 data-nosnippet>78</a>        <span class="kw">if </span>keys.is_empty() {
<a href=#79 id=79 data-nosnippet>79</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"No private keys found"</span>));
<a href=#80 id=80 data-nosnippet>80</a>        }
<a href=#81 id=81 data-nosnippet>81</a>
<a href=#82 id=82 data-nosnippet>82</a>        <span class="kw">let </span>key = PrivateKey(keys.remove(<span class="number">0</span>));
<a href=#83 id=83 data-nosnippet>83</a>
<a href=#84 id=84 data-nosnippet>84</a>        <span class="kw">let </span>config = ClientConfig::builder()
<a href=#85 id=85 data-nosnippet>85</a>            .with_safe_defaults()
<a href=#86 id=86 data-nosnippet>86</a>            .with_root_certificates(root_store)
<a href=#87 id=87 data-nosnippet>87</a>            .with_client_auth_cert(cert_chain, key)<span class="question-mark">?</span>;
<a href=#88 id=88 data-nosnippet>88</a>
<a href=#89 id=89 data-nosnippet>89</a>        <span class="prelude-val">Ok</span>(Arc::new(config))
<a href=#90 id=90 data-nosnippet>90</a>    }
<a href=#91 id=91 data-nosnippet>91</a>
<a href=#92 id=92 data-nosnippet>92</a>    <span class="kw">pub fn </span>create_server_config(cert_path: <span class="kw-2">&amp;</span>str, key_path: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;Arc&lt;ServerConfig&gt;&gt; {
<a href=#93 id=93 data-nosnippet>93</a>        <span class="comment">// 加载服务器证书和私钥
<a href=#94 id=94 data-nosnippet>94</a>        </span><span class="kw">let </span>cert_file = File::open(cert_path)<span class="question-mark">?</span>;
<a href=#95 id=95 data-nosnippet>95</a>        <span class="kw">let </span><span class="kw-2">mut </span>cert_reader = BufReader::new(cert_file);
<a href=#96 id=96 data-nosnippet>96</a>        <span class="kw">let </span>cert_chain = certs(<span class="kw-2">&amp;mut </span>cert_reader)<span class="question-mark">?
<a href=#97 id=97 data-nosnippet>97</a>            </span>.into_iter()
<a href=#98 id=98 data-nosnippet>98</a>            .map(Certificate)
<a href=#99 id=99 data-nosnippet>99</a>            .collect();
<a href=#100 id=100 data-nosnippet>100</a>
<a href=#101 id=101 data-nosnippet>101</a>        <span class="kw">let </span>key_file = File::open(key_path)<span class="question-mark">?</span>;
<a href=#102 id=102 data-nosnippet>102</a>        <span class="kw">let </span><span class="kw-2">mut </span>key_reader = BufReader::new(key_file);
<a href=#103 id=103 data-nosnippet>103</a>        <span class="kw">let </span><span class="kw-2">mut </span>keys = pkcs8_private_keys(<span class="kw-2">&amp;mut </span>key_reader)<span class="question-mark">?</span>;
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>        <span class="kw">if </span>keys.is_empty() {
<a href=#106 id=106 data-nosnippet>106</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"No private keys found"</span>));
<a href=#107 id=107 data-nosnippet>107</a>        }
<a href=#108 id=108 data-nosnippet>108</a>
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">let </span>key = PrivateKey(keys.remove(<span class="number">0</span>));
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>        <span class="kw">let </span>config = ServerConfig::builder()
<a href=#112 id=112 data-nosnippet>112</a>            .with_safe_defaults()
<a href=#113 id=113 data-nosnippet>113</a>            .with_no_client_auth()
<a href=#114 id=114 data-nosnippet>114</a>            .with_single_cert(cert_chain, key)<span class="question-mark">?</span>;
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>        <span class="prelude-val">Ok</span>(Arc::new(config))
<a href=#117 id=117 data-nosnippet>117</a>    }
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">pub fn </span>create_self_signed_cert() -&gt; <span class="prelude-ty">Result</span>&lt;(Vec&lt;u8&gt;, Vec&lt;u8&gt;)&gt; {
<a href=#120 id=120 data-nosnippet>120</a>        <span class="comment">// 生成自签名证书（用于测试）
<a href=#121 id=121 data-nosnippet>121</a>        </span><span class="kw">use </span>rcgen::{Certificate <span class="kw">as </span>RcgenCert, CertificateParams, DistinguishedName};
<a href=#122 id=122 data-nosnippet>122</a>
<a href=#123 id=123 data-nosnippet>123</a>        <span class="kw">let </span><span class="kw-2">mut </span>params = CertificateParams::new(<span class="macro">vec!</span>[<span class="string">"localhost"</span>.to_string()]);
<a href=#124 id=124 data-nosnippet>124</a>        params.distinguished_name = DistinguishedName::new();
<a href=#125 id=125 data-nosnippet>125</a>        params
<a href=#126 id=126 data-nosnippet>126</a>            .distinguished_name
<a href=#127 id=127 data-nosnippet>127</a>            .push(rcgen::DnType::CommonName, <span class="string">"Remote Control Server"</span>);
<a href=#128 id=128 data-nosnippet>128</a>        params
<a href=#129 id=129 data-nosnippet>129</a>            .distinguished_name
<a href=#130 id=130 data-nosnippet>130</a>            .push(rcgen::DnType::OrganizationName, <span class="string">"Remote Control"</span>);
<a href=#131 id=131 data-nosnippet>131</a>
<a href=#132 id=132 data-nosnippet>132</a>        <span class="kw">let </span>cert = RcgenCert::from_params(params)<span class="question-mark">?</span>;
<a href=#133 id=133 data-nosnippet>133</a>        <span class="kw">let </span>cert_pem = cert.serialize_pem()<span class="question-mark">?</span>;
<a href=#134 id=134 data-nosnippet>134</a>        <span class="kw">let </span>key_pem = cert.serialize_private_key_pem();
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>        <span class="prelude-val">Ok</span>((cert_pem.into_bytes(), key_pem.into_bytes()))
<a href=#137 id=137 data-nosnippet>137</a>    }
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>    <span class="kw">pub fn </span>verify_certificate_chain(cert_chain: <span class="kw-2">&amp;</span>[Certificate]) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#140 id=140 data-nosnippet>140</a>        <span class="kw">if </span>cert_chain.is_empty() {
<a href=#141 id=141 data-nosnippet>141</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Empty certificate chain"</span>));
<a href=#142 id=142 data-nosnippet>142</a>        }
<a href=#143 id=143 data-nosnippet>143</a>
<a href=#144 id=144 data-nosnippet>144</a>        <span class="comment">// 这里可以添加更多的证书验证逻辑
<a href=#145 id=145 data-nosnippet>145</a>        // 例如：检查证书有效期、吊销状态等
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>        </span><span class="prelude-val">Ok</span>(())
<a href=#148 id=148 data-nosnippet>148</a>    }
<a href=#149 id=149 data-nosnippet>149</a>
<a href=#150 id=150 data-nosnippet>150</a>    <span class="kw">pub fn </span>get_certificate_info(cert: <span class="kw-2">&amp;</span>Certificate) -&gt; <span class="prelude-ty">Result</span>&lt;CertificateInfo&gt; {
<a href=#151 id=151 data-nosnippet>151</a>        <span class="kw">use </span>x509_parser::prelude::<span class="kw-2">*</span>;
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>        <span class="kw">let </span>(<span class="kw">_</span>, parsed_cert) = X509Certificate::from_der(<span class="kw-2">&amp;</span>cert.<span class="number">0</span>)<span class="question-mark">?</span>;
<a href=#154 id=154 data-nosnippet>154</a>
<a href=#155 id=155 data-nosnippet>155</a>        <span class="prelude-val">Ok</span>(CertificateInfo {
<a href=#156 id=156 data-nosnippet>156</a>            subject: parsed_cert.subject().to_string(),
<a href=#157 id=157 data-nosnippet>157</a>            issuer: parsed_cert.issuer().to_string(),
<a href=#158 id=158 data-nosnippet>158</a>            serial_number: <span class="macro">format!</span>(<span class="string">"{:x}"</span>, parsed_cert.serial),
<a href=#159 id=159 data-nosnippet>159</a>            not_before: parsed_cert.validity().not_before.timestamp(),
<a href=#160 id=160 data-nosnippet>160</a>            not_after: parsed_cert.validity().not_after.timestamp(),
<a href=#161 id=161 data-nosnippet>161</a>            fingerprint: <span class="self">Self</span>::calculate_fingerprint(<span class="kw-2">&amp;</span>cert.<span class="number">0</span>),
<a href=#162 id=162 data-nosnippet>162</a>        })
<a href=#163 id=163 data-nosnippet>163</a>    }
<a href=#164 id=164 data-nosnippet>164</a>
<a href=#165 id=165 data-nosnippet>165</a>    <span class="kw">fn </span>calculate_fingerprint(cert_der: <span class="kw-2">&amp;</span>[u8]) -&gt; String {
<a href=#166 id=166 data-nosnippet>166</a>        <span class="kw">use </span>sha2::{Digest, Sha256};
<a href=#167 id=167 data-nosnippet>167</a>        <span class="kw">let </span><span class="kw-2">mut </span>hasher = Sha256::new();
<a href=#168 id=168 data-nosnippet>168</a>        hasher.update(cert_der);
<a href=#169 id=169 data-nosnippet>169</a>        <span class="macro">format!</span>(<span class="string">"{:x}"</span>, hasher.finalize())
<a href=#170 id=170 data-nosnippet>170</a>    }
<a href=#171 id=171 data-nosnippet>171</a>}
<a href=#172 id=172 data-nosnippet>172</a>
<a href=#173 id=173 data-nosnippet>173</a><span class="attr">#[derive(Debug, Clone)]
<a href=#174 id=174 data-nosnippet>174</a></span><span class="kw">pub struct </span>CertificateInfo {
<a href=#175 id=175 data-nosnippet>175</a>    <span class="kw">pub </span>subject: String,
<a href=#176 id=176 data-nosnippet>176</a>    <span class="kw">pub </span>issuer: String,
<a href=#177 id=177 data-nosnippet>177</a>    <span class="kw">pub </span>serial_number: String,
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">pub </span>not_before: i64,
<a href=#179 id=179 data-nosnippet>179</a>    <span class="kw">pub </span>not_after: i64,
<a href=#180 id=180 data-nosnippet>180</a>    <span class="kw">pub </span>fingerprint: String,
<a href=#181 id=181 data-nosnippet>181</a>}
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a><span class="kw">pub struct </span>TlsManager {
<a href=#184 id=184 data-nosnippet>184</a>    config: TlsConfig,
<a href=#185 id=185 data-nosnippet>185</a>}
<a href=#186 id=186 data-nosnippet>186</a>
<a href=#187 id=187 data-nosnippet>187</a><span class="kw">impl </span>TlsManager {
<a href=#188 id=188 data-nosnippet>188</a>    <span class="kw">pub fn </span>new(config: TlsConfig) -&gt; <span class="self">Self </span>{
<a href=#189 id=189 data-nosnippet>189</a>        <span class="self">Self </span>{ config }
<a href=#190 id=190 data-nosnippet>190</a>    }
<a href=#191 id=191 data-nosnippet>191</a>
<a href=#192 id=192 data-nosnippet>192</a>    <span class="kw">pub fn </span>get_client_config(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Arc&lt;ClientConfig&gt;&gt; {
<a href=#193 id=193 data-nosnippet>193</a>        <span class="self">self</span>.config.client_config.as_ref()
<a href=#194 id=194 data-nosnippet>194</a>    }
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>    <span class="kw">pub fn </span>get_server_config(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Arc&lt;ServerConfig&gt;&gt; {
<a href=#197 id=197 data-nosnippet>197</a>        <span class="self">self</span>.config.server_config.as_ref()
<a href=#198 id=198 data-nosnippet>198</a>    }
<a href=#199 id=199 data-nosnippet>199</a>
<a href=#200 id=200 data-nosnippet>200</a>    <span class="kw">pub async fn </span>create_secure_client(<span class="kw-2">&amp;</span><span class="self">self</span>, server_name: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;reqwest::Client&gt; {
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(client_config) = <span class="kw-2">&amp;</span><span class="self">self</span>.config.client_config {
<a href=#202 id=202 data-nosnippet>202</a>            <span class="kw">let </span>client = reqwest::Client::builder()
<a href=#203 id=203 data-nosnippet>203</a>                .use_preconfigured_tls(client_config.clone())
<a href=#204 id=204 data-nosnippet>204</a>                .build()<span class="question-mark">?</span>;
<a href=#205 id=205 data-nosnippet>205</a>            <span class="prelude-val">Ok</span>(client)
<a href=#206 id=206 data-nosnippet>206</a>        } <span class="kw">else </span>{
<a href=#207 id=207 data-nosnippet>207</a>            <span class="comment">// 使用默认TLS配置
<a href=#208 id=208 data-nosnippet>208</a>            </span><span class="kw">let </span>client = reqwest::Client::builder().https_only(<span class="bool-val">true</span>).build()<span class="question-mark">?</span>;
<a href=#209 id=209 data-nosnippet>209</a>            <span class="prelude-val">Ok</span>(client)
<a href=#210 id=210 data-nosnippet>210</a>        }
<a href=#211 id=211 data-nosnippet>211</a>    }
<a href=#212 id=212 data-nosnippet>212</a>
<a href=#213 id=213 data-nosnippet>213</a>    <span class="kw">pub fn </span>validate_server_certificate(
<a href=#214 id=214 data-nosnippet>214</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#215 id=215 data-nosnippet>215</a>        cert_chain: <span class="kw-2">&amp;</span>[Certificate],
<a href=#216 id=216 data-nosnippet>216</a>        server_name: <span class="kw-2">&amp;</span>str,
<a href=#217 id=217 data-nosnippet>217</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#218 id=218 data-nosnippet>218</a>        <span class="comment">// 验证证书链
<a href=#219 id=219 data-nosnippet>219</a>        </span>TlsConfig::verify_certificate_chain(cert_chain)<span class="question-mark">?</span>;
<a href=#220 id=220 data-nosnippet>220</a>
<a href=#221 id=221 data-nosnippet>221</a>        <span class="comment">// 验证服务器名称
<a href=#222 id=222 data-nosnippet>222</a>        </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(cert_info) = TlsConfig::get_certificate_info(<span class="kw-2">&amp;</span>cert_chain[<span class="number">0</span>]) {
<a href=#223 id=223 data-nosnippet>223</a>            <span class="kw">if </span>!cert_info.subject.contains(server_name) {
<a href=#224 id=224 data-nosnippet>224</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Server name mismatch"</span>));
<a href=#225 id=225 data-nosnippet>225</a>            }
<a href=#226 id=226 data-nosnippet>226</a>        }
<a href=#227 id=227 data-nosnippet>227</a>
<a href=#228 id=228 data-nosnippet>228</a>        <span class="prelude-val">Ok</span>(())
<a href=#229 id=229 data-nosnippet>229</a>    }
<a href=#230 id=230 data-nosnippet>230</a>}
<a href=#231 id=231 data-nosnippet>231</a>
<a href=#232 id=232 data-nosnippet>232</a><span class="attr">#[cfg(test)]
<a href=#233 id=233 data-nosnippet>233</a></span><span class="kw">mod </span>tests {
<a href=#234 id=234 data-nosnippet>234</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#235 id=235 data-nosnippet>235</a>
<a href=#236 id=236 data-nosnippet>236</a>    <span class="attr">#[test]
<a href=#237 id=237 data-nosnippet>237</a>    </span><span class="kw">fn </span>test_self_signed_cert_generation() {
<a href=#238 id=238 data-nosnippet>238</a>        <span class="kw">let </span>result = TlsConfig::create_self_signed_cert();
<a href=#239 id=239 data-nosnippet>239</a>        <span class="macro">assert!</span>(result.is_ok());
<a href=#240 id=240 data-nosnippet>240</a>
<a href=#241 id=241 data-nosnippet>241</a>        <span class="kw">let </span>(cert_pem, key_pem) = result.unwrap();
<a href=#242 id=242 data-nosnippet>242</a>        <span class="macro">assert!</span>(!cert_pem.is_empty());
<a href=#243 id=243 data-nosnippet>243</a>        <span class="macro">assert!</span>(!key_pem.is_empty());
<a href=#244 id=244 data-nosnippet>244</a>
<a href=#245 id=245 data-nosnippet>245</a>        <span class="comment">// 验证PEM格式
<a href=#246 id=246 data-nosnippet>246</a>        </span><span class="macro">assert!</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>cert_pem).contains(<span class="string">"-----BEGIN CERTIFICATE-----"</span>));
<a href=#247 id=247 data-nosnippet>247</a>        <span class="macro">assert!</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>key_pem).contains(<span class="string">"-----BEGIN PRIVATE KEY-----"</span>));
<a href=#248 id=248 data-nosnippet>248</a>    }
<a href=#249 id=249 data-nosnippet>249</a>
<a href=#250 id=250 data-nosnippet>250</a>    <span class="attr">#[test]
<a href=#251 id=251 data-nosnippet>251</a>    </span><span class="kw">fn </span>test_client_config_creation() {
<a href=#252 id=252 data-nosnippet>252</a>        <span class="kw">let </span>result = TlsConfig::create_client_config();
<a href=#253 id=253 data-nosnippet>253</a>        <span class="macro">assert!</span>(result.is_ok());
<a href=#254 id=254 data-nosnippet>254</a>    }
<a href=#255 id=255 data-nosnippet>255</a>}</code></pre></div></section></main></body></html>