<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `common/src/crypto.rs`."><title>crypto.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">common/</div>crypto.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>aes_gcm::{
<a href=#2 id=2 data-nosnippet>2</a>    aead::{generic_array::GenericArray, Aead, KeyInit},
<a href=#3 id=3 data-nosnippet>3</a>    Aes256Gcm,
<a href=#4 id=4 data-nosnippet>4</a>};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>sha2::{Digest, Sha256};
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub struct </span>CryptoManager {
<a href=#9 id=9 data-nosnippet>9</a>    cipher: Aes256Gcm,
<a href=#10 id=10 data-nosnippet>10</a>    key_hash: [u8; <span class="number">32</span>],
<a href=#11 id=11 data-nosnippet>11</a>}
<a href=#12 id=12 data-nosnippet>12</a>
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">impl </span>CryptoManager {
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub fn </span>new(key: <span class="kw-2">&amp;</span>[u8]) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#15 id=15 data-nosnippet>15</a>        <span class="comment">// 使用SHA256哈希来确保密钥长度为32字节
<a href=#16 id=16 data-nosnippet>16</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>hasher = Sha256::new();
<a href=#17 id=17 data-nosnippet>17</a>        hasher.update(key);
<a href=#18 id=18 data-nosnippet>18</a>        <span class="kw">let </span>key_hash = hasher.finalize();
<a href=#19 id=19 data-nosnippet>19</a>
<a href=#20 id=20 data-nosnippet>20</a>        <span class="kw">let </span>key_array: [u8; <span class="number">32</span>] = key_hash.into();
<a href=#21 id=21 data-nosnippet>21</a>        <span class="kw">let </span>key = GenericArray::from_slice(<span class="kw-2">&amp;</span>key_array);
<a href=#22 id=22 data-nosnippet>22</a>        <span class="kw">let </span>cipher = Aes256Gcm::new(key);
<a href=#23 id=23 data-nosnippet>23</a>
<a href=#24 id=24 data-nosnippet>24</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#25 id=25 data-nosnippet>25</a>            cipher,
<a href=#26 id=26 data-nosnippet>26</a>            key_hash: key_array,
<a href=#27 id=27 data-nosnippet>27</a>        })
<a href=#28 id=28 data-nosnippet>28</a>    }
<a href=#29 id=29 data-nosnippet>29</a>
<a href=#30 id=30 data-nosnippet>30</a>    <span class="kw">pub fn </span>encrypt(<span class="kw-2">&amp;</span><span class="self">self</span>, data: <span class="kw-2">&amp;</span>[u8]) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;u8&gt;&gt; {
<a href=#31 id=31 data-nosnippet>31</a>        <span class="comment">// 生成随机nonce
<a href=#32 id=32 data-nosnippet>32</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>nonce_bytes = [<span class="number">0u8</span>; <span class="number">12</span>];
<a href=#33 id=33 data-nosnippet>33</a>        <span class="kw">use </span>rand::RngCore;
<a href=#34 id=34 data-nosnippet>34</a>        rand::rngs::OsRng.fill_bytes(<span class="kw-2">&amp;mut </span>nonce_bytes);
<a href=#35 id=35 data-nosnippet>35</a>        <span class="kw">let </span>nonce = GenericArray::from_slice(<span class="kw-2">&amp;</span>nonce_bytes);
<a href=#36 id=36 data-nosnippet>36</a>
<a href=#37 id=37 data-nosnippet>37</a>        <span class="comment">// 加密数据
<a href=#38 id=38 data-nosnippet>38</a>        </span><span class="kw">let </span>ciphertext = <span class="self">self
<a href=#39 id=39 data-nosnippet>39</a>            </span>.cipher
<a href=#40 id=40 data-nosnippet>40</a>            .encrypt(nonce, data)
<a href=#41 id=41 data-nosnippet>41</a>            .map_err(|e| <span class="macro">anyhow!</span>(<span class="string">"Encryption failed: {}"</span>, e))<span class="question-mark">?</span>;
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a>        <span class="comment">// 将nonce和密文组合
<a href=#44 id=44 data-nosnippet>44</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>result = Vec::with_capacity(<span class="number">12 </span>+ ciphertext.len());
<a href=#45 id=45 data-nosnippet>45</a>        result.extend_from_slice(<span class="kw-2">&amp;</span>nonce_bytes);
<a href=#46 id=46 data-nosnippet>46</a>        result.extend_from_slice(<span class="kw-2">&amp;</span>ciphertext);
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a>        <span class="prelude-val">Ok</span>(result)
<a href=#49 id=49 data-nosnippet>49</a>    }
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub fn </span>decrypt(<span class="kw-2">&amp;</span><span class="self">self</span>, data: <span class="kw-2">&amp;</span>[u8]) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;u8&gt;&gt; {
<a href=#52 id=52 data-nosnippet>52</a>        <span class="kw">if </span>data.len() &lt; <span class="number">12 </span>{
<a href=#53 id=53 data-nosnippet>53</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Invalid encrypted data length"</span>));
<a href=#54 id=54 data-nosnippet>54</a>        }
<a href=#55 id=55 data-nosnippet>55</a>
<a href=#56 id=56 data-nosnippet>56</a>        <span class="comment">// 分离nonce和密文
<a href=#57 id=57 data-nosnippet>57</a>        </span><span class="kw">let </span>(nonce_bytes, ciphertext) = data.split_at(<span class="number">12</span>);
<a href=#58 id=58 data-nosnippet>58</a>        <span class="kw">let </span>nonce = GenericArray::from_slice(nonce_bytes);
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a>        <span class="comment">// 解密数据
<a href=#61 id=61 data-nosnippet>61</a>        </span><span class="kw">let </span>plaintext = <span class="self">self
<a href=#62 id=62 data-nosnippet>62</a>            </span>.cipher
<a href=#63 id=63 data-nosnippet>63</a>            .decrypt(nonce, ciphertext)
<a href=#64 id=64 data-nosnippet>64</a>            .map_err(|e| <span class="macro">anyhow!</span>(<span class="string">"Decryption failed: {}"</span>, e))<span class="question-mark">?</span>;
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a>        <span class="prelude-val">Ok</span>(plaintext)
<a href=#67 id=67 data-nosnippet>67</a>    }
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a>    <span class="kw">pub fn </span>generate_key() -&gt; Vec&lt;u8&gt; {
<a href=#70 id=70 data-nosnippet>70</a>        <span class="kw">let </span><span class="kw-2">mut </span>key = <span class="macro">vec!</span>[<span class="number">0u8</span>; <span class="number">32</span>];
<a href=#71 id=71 data-nosnippet>71</a>        <span class="kw">use </span>rand::RngCore;
<a href=#72 id=72 data-nosnippet>72</a>        rand::rngs::OsRng.fill_bytes(<span class="kw-2">&amp;mut </span>key);
<a href=#73 id=73 data-nosnippet>73</a>        key
<a href=#74 id=74 data-nosnippet>74</a>    }
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>    <span class="kw">pub fn </span>hash_data(data: <span class="kw-2">&amp;</span>[u8]) -&gt; String {
<a href=#77 id=77 data-nosnippet>77</a>        <span class="kw">let </span><span class="kw-2">mut </span>hasher = Sha256::new();
<a href=#78 id=78 data-nosnippet>78</a>        hasher.update(data);
<a href=#79 id=79 data-nosnippet>79</a>        <span class="macro">format!</span>(<span class="string">"{:x}"</span>, hasher.finalize())
<a href=#80 id=80 data-nosnippet>80</a>    }
<a href=#81 id=81 data-nosnippet>81</a>}
<a href=#82 id=82 data-nosnippet>82</a>
<a href=#83 id=83 data-nosnippet>83</a><span class="attr">#[cfg(test)]
<a href=#84 id=84 data-nosnippet>84</a></span><span class="kw">mod </span>tests {
<a href=#85 id=85 data-nosnippet>85</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#86 id=86 data-nosnippet>86</a>
<a href=#87 id=87 data-nosnippet>87</a>    <span class="attr">#[test]
<a href=#88 id=88 data-nosnippet>88</a>    </span><span class="kw">fn </span>test_encrypt_decrypt() {
<a href=#89 id=89 data-nosnippet>89</a>        <span class="kw">let </span>key = CryptoManager::generate_key();
<a href=#90 id=90 data-nosnippet>90</a>        <span class="kw">let </span>crypto = CryptoManager::new(<span class="kw-2">&amp;</span>key).unwrap();
<a href=#91 id=91 data-nosnippet>91</a>
<a href=#92 id=92 data-nosnippet>92</a>        <span class="kw">let </span>data = <span class="string">b"Hello, World!"</span>;
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">let </span>encrypted = crypto.encrypt(data).unwrap();
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">let </span>decrypted = crypto.decrypt(<span class="kw-2">&amp;</span>encrypted).unwrap();
<a href=#95 id=95 data-nosnippet>95</a>
<a href=#96 id=96 data-nosnippet>96</a>        <span class="macro">assert_eq!</span>(data, decrypted.as_slice());
<a href=#97 id=97 data-nosnippet>97</a>    }
<a href=#98 id=98 data-nosnippet>98</a>
<a href=#99 id=99 data-nosnippet>99</a>    <span class="attr">#[test]
<a href=#100 id=100 data-nosnippet>100</a>    </span><span class="kw">fn </span>test_hash_data() {
<a href=#101 id=101 data-nosnippet>101</a>        <span class="kw">let </span>data = <span class="string">b"test data"</span>;
<a href=#102 id=102 data-nosnippet>102</a>        <span class="kw">let </span>hash1 = CryptoManager::hash_data(data);
<a href=#103 id=103 data-nosnippet>103</a>        <span class="kw">let </span>hash2 = CryptoManager::hash_data(data);
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>        <span class="macro">assert_eq!</span>(hash1, hash2);
<a href=#106 id=106 data-nosnippet>106</a>        <span class="macro">assert_eq!</span>(hash1.len(), <span class="number">64</span>); <span class="comment">// SHA256 produces 64 character hex string
<a href=#107 id=107 data-nosnippet>107</a>    </span>}
<a href=#108 id=108 data-nosnippet>108</a>}
<a href=#109 id=109 data-nosnippet>109</a>
<a href=#110 id=110 data-nosnippet>110</a><span class="kw">impl </span>std::fmt::Debug <span class="kw">for </span>CryptoManager {
<a href=#111 id=111 data-nosnippet>111</a>    <span class="kw">fn </span>fmt(<span class="kw-2">&amp;</span><span class="self">self</span>, f: <span class="kw-2">&amp;mut </span>std::fmt::Formatter&lt;<span class="lifetime">'_</span>&gt;) -&gt; std::fmt::Result {
<a href=#112 id=112 data-nosnippet>112</a>        f.debug_struct(<span class="string">"CryptoManager"</span>)
<a href=#113 id=113 data-nosnippet>113</a>            .field(<span class="string">"key_hash"</span>, <span class="kw-2">&amp;</span><span class="string">"[REDACTED]"</span>)
<a href=#114 id=114 data-nosnippet>114</a>            .finish()
<a href=#115 id=115 data-nosnippet>115</a>    }
<a href=#116 id=116 data-nosnippet>116</a>}
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a><span class="kw">impl </span>Clone <span class="kw">for </span>CryptoManager {
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">fn </span>clone(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="self">Self </span>{
<a href=#120 id=120 data-nosnippet>120</a>        <span class="kw">let </span>key = GenericArray::from_slice(<span class="kw-2">&amp;</span><span class="self">self</span>.key_hash);
<a href=#121 id=121 data-nosnippet>121</a>        <span class="kw">let </span>cipher = Aes256Gcm::new(key);
<a href=#122 id=122 data-nosnippet>122</a>        <span class="self">Self </span>{
<a href=#123 id=123 data-nosnippet>123</a>            cipher,
<a href=#124 id=124 data-nosnippet>124</a>            key_hash: <span class="self">self</span>.key_hash,
<a href=#125 id=125 data-nosnippet>125</a>        }
<a href=#126 id=126 data-nosnippet>126</a>    }
<a href=#127 id=127 data-nosnippet>127</a>}</code></pre></div></section></main></body></html>