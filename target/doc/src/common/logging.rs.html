<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `common/src/logging.rs`."><title>logging.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">common/</div>logging.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::Result;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>chrono::{DateTime, Utc};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::fs::OpenOptions;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::io::Write;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::path::Path;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>uuid::Uuid;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#10 id=10 data-nosnippet>10</a></span><span class="kw">pub struct </span>LogEntry {
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>id: Uuid,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>timestamp: DateTime&lt;Utc&gt;,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>level: LogLevel,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>category: LogCategory,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>user_id: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>session_id: <span class="prelude-ty">Option</span>&lt;Uuid&gt;,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>message: String,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>details: <span class="prelude-ty">Option</span>&lt;serde_json::Value&gt;,
<a href=#19 id=19 data-nosnippet>19</a>}
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#22 id=22 data-nosnippet>22</a></span><span class="kw">pub enum </span>LogLevel {
<a href=#23 id=23 data-nosnippet>23</a>    Debug,
<a href=#24 id=24 data-nosnippet>24</a>    Info,
<a href=#25 id=25 data-nosnippet>25</a>    Warning,
<a href=#26 id=26 data-nosnippet>26</a>    Error,
<a href=#27 id=27 data-nosnippet>27</a>    Critical,
<a href=#28 id=28 data-nosnippet>28</a>}
<a href=#29 id=29 data-nosnippet>29</a>
<a href=#30 id=30 data-nosnippet>30</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#31 id=31 data-nosnippet>31</a></span><span class="kw">pub enum </span>LogCategory {
<a href=#32 id=32 data-nosnippet>32</a>    Authentication,
<a href=#33 id=33 data-nosnippet>33</a>    Command,
<a href=#34 id=34 data-nosnippet>34</a>    FileOperation,
<a href=#35 id=35 data-nosnippet>35</a>    ProcessManagement,
<a href=#36 id=36 data-nosnippet>36</a>    SystemInfo,
<a href=#37 id=37 data-nosnippet>37</a>    Communication,
<a href=#38 id=38 data-nosnippet>38</a>    Security,
<a href=#39 id=39 data-nosnippet>39</a>    Error,
<a href=#40 id=40 data-nosnippet>40</a>}
<a href=#41 id=41 data-nosnippet>41</a>
<a href=#42 id=42 data-nosnippet>42</a><span class="kw">pub struct </span>Logger {
<a href=#43 id=43 data-nosnippet>43</a>    log_file_path: String,
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="kw">impl </span>Logger {
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">pub fn </span>new(log_file_path: String) -&gt; <span class="self">Self </span>{
<a href=#48 id=48 data-nosnippet>48</a>        <span class="self">Self </span>{ log_file_path }
<a href=#49 id=49 data-nosnippet>49</a>    }
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub fn </span>log(<span class="kw-2">&amp;</span><span class="self">self</span>, entry: LogEntry) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#52 id=52 data-nosnippet>52</a>        <span class="comment">// 写入到文件
<a href=#53 id=53 data-nosnippet>53</a>        </span><span class="self">self</span>.write_to_file(<span class="kw-2">&amp;</span>entry)<span class="question-mark">?</span>;
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a>        <span class="comment">// 同时输出到控制台（根据日志级别）
<a href=#56 id=56 data-nosnippet>56</a>        </span><span class="kw">match </span>entry.level {
<a href=#57 id=57 data-nosnippet>57</a>            LogLevel::Debug =&gt; <span class="macro">log::debug!</span>(<span class="string">"{}"</span>, <span class="self">self</span>.format_entry(<span class="kw-2">&amp;</span>entry)),
<a href=#58 id=58 data-nosnippet>58</a>            LogLevel::Info =&gt; <span class="macro">log::info!</span>(<span class="string">"{}"</span>, <span class="self">self</span>.format_entry(<span class="kw-2">&amp;</span>entry)),
<a href=#59 id=59 data-nosnippet>59</a>            LogLevel::Warning =&gt; <span class="macro">log::warn!</span>(<span class="string">"{}"</span>, <span class="self">self</span>.format_entry(<span class="kw-2">&amp;</span>entry)),
<a href=#60 id=60 data-nosnippet>60</a>            LogLevel::Error =&gt; <span class="macro">log::error!</span>(<span class="string">"{}"</span>, <span class="self">self</span>.format_entry(<span class="kw-2">&amp;</span>entry)),
<a href=#61 id=61 data-nosnippet>61</a>            LogLevel::Critical =&gt; <span class="macro">log::error!</span>(<span class="string">"CRITICAL: {}"</span>, <span class="self">self</span>.format_entry(<span class="kw-2">&amp;</span>entry)),
<a href=#62 id=62 data-nosnippet>62</a>        }
<a href=#63 id=63 data-nosnippet>63</a>
<a href=#64 id=64 data-nosnippet>64</a>        <span class="prelude-val">Ok</span>(())
<a href=#65 id=65 data-nosnippet>65</a>    }
<a href=#66 id=66 data-nosnippet>66</a>
<a href=#67 id=67 data-nosnippet>67</a>    <span class="kw">pub fn </span>log_authentication(
<a href=#68 id=68 data-nosnippet>68</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#69 id=69 data-nosnippet>69</a>        user_id: <span class="kw-2">&amp;</span>str,
<a href=#70 id=70 data-nosnippet>70</a>        success: bool,
<a href=#71 id=71 data-nosnippet>71</a>        details: <span class="prelude-ty">Option</span>&lt;serde_json::Value&gt;,
<a href=#72 id=72 data-nosnippet>72</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#73 id=73 data-nosnippet>73</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#74 id=74 data-nosnippet>74</a>            id: Uuid::new_v4(),
<a href=#75 id=75 data-nosnippet>75</a>            timestamp: Utc::now(),
<a href=#76 id=76 data-nosnippet>76</a>            level: <span class="kw">if </span>success {
<a href=#77 id=77 data-nosnippet>77</a>                LogLevel::Info
<a href=#78 id=78 data-nosnippet>78</a>            } <span class="kw">else </span>{
<a href=#79 id=79 data-nosnippet>79</a>                LogLevel::Warning
<a href=#80 id=80 data-nosnippet>80</a>            },
<a href=#81 id=81 data-nosnippet>81</a>            category: LogCategory::Authentication,
<a href=#82 id=82 data-nosnippet>82</a>            user_id: <span class="prelude-val">Some</span>(user_id.to_string()),
<a href=#83 id=83 data-nosnippet>83</a>            session_id: <span class="prelude-val">None</span>,
<a href=#84 id=84 data-nosnippet>84</a>            message: <span class="macro">format!</span>(
<a href=#85 id=85 data-nosnippet>85</a>                <span class="string">"Authentication {} for user: {}"</span>,
<a href=#86 id=86 data-nosnippet>86</a>                <span class="kw">if </span>success { <span class="string">"successful" </span>} <span class="kw">else </span>{ <span class="string">"failed" </span>},
<a href=#87 id=87 data-nosnippet>87</a>                user_id
<a href=#88 id=88 data-nosnippet>88</a>            ),
<a href=#89 id=89 data-nosnippet>89</a>            details,
<a href=#90 id=90 data-nosnippet>90</a>        };
<a href=#91 id=91 data-nosnippet>91</a>        <span class="self">self</span>.log(entry)
<a href=#92 id=92 data-nosnippet>92</a>    }
<a href=#93 id=93 data-nosnippet>93</a>
<a href=#94 id=94 data-nosnippet>94</a>    <span class="kw">pub fn </span>log_command(
<a href=#95 id=95 data-nosnippet>95</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#96 id=96 data-nosnippet>96</a>        user_id: <span class="kw-2">&amp;</span>str,
<a href=#97 id=97 data-nosnippet>97</a>        session_id: Uuid,
<a href=#98 id=98 data-nosnippet>98</a>        command: <span class="kw-2">&amp;</span>str,
<a href=#99 id=99 data-nosnippet>99</a>        success: bool,
<a href=#100 id=100 data-nosnippet>100</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#101 id=101 data-nosnippet>101</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#102 id=102 data-nosnippet>102</a>            id: Uuid::new_v4(),
<a href=#103 id=103 data-nosnippet>103</a>            timestamp: Utc::now(),
<a href=#104 id=104 data-nosnippet>104</a>            level: <span class="kw">if </span>success {
<a href=#105 id=105 data-nosnippet>105</a>                LogLevel::Info
<a href=#106 id=106 data-nosnippet>106</a>            } <span class="kw">else </span>{
<a href=#107 id=107 data-nosnippet>107</a>                LogLevel::Error
<a href=#108 id=108 data-nosnippet>108</a>            },
<a href=#109 id=109 data-nosnippet>109</a>            category: LogCategory::Command,
<a href=#110 id=110 data-nosnippet>110</a>            user_id: <span class="prelude-val">Some</span>(user_id.to_string()),
<a href=#111 id=111 data-nosnippet>111</a>            session_id: <span class="prelude-val">Some</span>(session_id),
<a href=#112 id=112 data-nosnippet>112</a>            message: <span class="macro">format!</span>(
<a href=#113 id=113 data-nosnippet>113</a>                <span class="string">"Command execution {}: {}"</span>,
<a href=#114 id=114 data-nosnippet>114</a>                <span class="kw">if </span>success { <span class="string">"successful" </span>} <span class="kw">else </span>{ <span class="string">"failed" </span>},
<a href=#115 id=115 data-nosnippet>115</a>                command
<a href=#116 id=116 data-nosnippet>116</a>            ),
<a href=#117 id=117 data-nosnippet>117</a>            details: <span class="prelude-val">None</span>,
<a href=#118 id=118 data-nosnippet>118</a>        };
<a href=#119 id=119 data-nosnippet>119</a>        <span class="self">self</span>.log(entry)
<a href=#120 id=120 data-nosnippet>120</a>    }
<a href=#121 id=121 data-nosnippet>121</a>
<a href=#122 id=122 data-nosnippet>122</a>    <span class="kw">pub fn </span>log_file_operation(
<a href=#123 id=123 data-nosnippet>123</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#124 id=124 data-nosnippet>124</a>        user_id: <span class="kw-2">&amp;</span>str,
<a href=#125 id=125 data-nosnippet>125</a>        session_id: Uuid,
<a href=#126 id=126 data-nosnippet>126</a>        operation: <span class="kw-2">&amp;</span>str,
<a href=#127 id=127 data-nosnippet>127</a>        file_path: <span class="kw-2">&amp;</span>str,
<a href=#128 id=128 data-nosnippet>128</a>        success: bool,
<a href=#129 id=129 data-nosnippet>129</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#130 id=130 data-nosnippet>130</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#131 id=131 data-nosnippet>131</a>            id: Uuid::new_v4(),
<a href=#132 id=132 data-nosnippet>132</a>            timestamp: Utc::now(),
<a href=#133 id=133 data-nosnippet>133</a>            level: <span class="kw">if </span>success {
<a href=#134 id=134 data-nosnippet>134</a>                LogLevel::Info
<a href=#135 id=135 data-nosnippet>135</a>            } <span class="kw">else </span>{
<a href=#136 id=136 data-nosnippet>136</a>                LogLevel::Error
<a href=#137 id=137 data-nosnippet>137</a>            },
<a href=#138 id=138 data-nosnippet>138</a>            category: LogCategory::FileOperation,
<a href=#139 id=139 data-nosnippet>139</a>            user_id: <span class="prelude-val">Some</span>(user_id.to_string()),
<a href=#140 id=140 data-nosnippet>140</a>            session_id: <span class="prelude-val">Some</span>(session_id),
<a href=#141 id=141 data-nosnippet>141</a>            message: <span class="macro">format!</span>(
<a href=#142 id=142 data-nosnippet>142</a>                <span class="string">"File {} {}: {}"</span>,
<a href=#143 id=143 data-nosnippet>143</a>                operation,
<a href=#144 id=144 data-nosnippet>144</a>                <span class="kw">if </span>success { <span class="string">"successful" </span>} <span class="kw">else </span>{ <span class="string">"failed" </span>},
<a href=#145 id=145 data-nosnippet>145</a>                file_path
<a href=#146 id=146 data-nosnippet>146</a>            ),
<a href=#147 id=147 data-nosnippet>147</a>            details: <span class="prelude-val">None</span>,
<a href=#148 id=148 data-nosnippet>148</a>        };
<a href=#149 id=149 data-nosnippet>149</a>        <span class="self">self</span>.log(entry)
<a href=#150 id=150 data-nosnippet>150</a>    }
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>    <span class="kw">pub fn </span>log_security_event(
<a href=#153 id=153 data-nosnippet>153</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#154 id=154 data-nosnippet>154</a>        event: <span class="kw-2">&amp;</span>str,
<a href=#155 id=155 data-nosnippet>155</a>        details: <span class="prelude-ty">Option</span>&lt;serde_json::Value&gt;,
<a href=#156 id=156 data-nosnippet>156</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#157 id=157 data-nosnippet>157</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#158 id=158 data-nosnippet>158</a>            id: Uuid::new_v4(),
<a href=#159 id=159 data-nosnippet>159</a>            timestamp: Utc::now(),
<a href=#160 id=160 data-nosnippet>160</a>            level: LogLevel::Critical,
<a href=#161 id=161 data-nosnippet>161</a>            category: LogCategory::Security,
<a href=#162 id=162 data-nosnippet>162</a>            user_id: <span class="prelude-val">None</span>,
<a href=#163 id=163 data-nosnippet>163</a>            session_id: <span class="prelude-val">None</span>,
<a href=#164 id=164 data-nosnippet>164</a>            message: <span class="macro">format!</span>(<span class="string">"Security event: {}"</span>, event),
<a href=#165 id=165 data-nosnippet>165</a>            details,
<a href=#166 id=166 data-nosnippet>166</a>        };
<a href=#167 id=167 data-nosnippet>167</a>        <span class="self">self</span>.log(entry)
<a href=#168 id=168 data-nosnippet>168</a>    }
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>    <span class="kw">pub fn </span>log_error(<span class="kw-2">&amp;</span><span class="self">self</span>, error: <span class="kw-2">&amp;</span>str, details: <span class="prelude-ty">Option</span>&lt;serde_json::Value&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#171 id=171 data-nosnippet>171</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#172 id=172 data-nosnippet>172</a>            id: Uuid::new_v4(),
<a href=#173 id=173 data-nosnippet>173</a>            timestamp: Utc::now(),
<a href=#174 id=174 data-nosnippet>174</a>            level: LogLevel::Error,
<a href=#175 id=175 data-nosnippet>175</a>            category: LogCategory::Error,
<a href=#176 id=176 data-nosnippet>176</a>            user_id: <span class="prelude-val">None</span>,
<a href=#177 id=177 data-nosnippet>177</a>            session_id: <span class="prelude-val">None</span>,
<a href=#178 id=178 data-nosnippet>178</a>            message: error.to_string(),
<a href=#179 id=179 data-nosnippet>179</a>            details,
<a href=#180 id=180 data-nosnippet>180</a>        };
<a href=#181 id=181 data-nosnippet>181</a>        <span class="self">self</span>.log(entry)
<a href=#182 id=182 data-nosnippet>182</a>    }
<a href=#183 id=183 data-nosnippet>183</a>
<a href=#184 id=184 data-nosnippet>184</a>    <span class="kw">fn </span>write_to_file(<span class="kw-2">&amp;</span><span class="self">self</span>, entry: <span class="kw-2">&amp;</span>LogEntry) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#185 id=185 data-nosnippet>185</a>        <span class="kw">let </span>json_line = serde_json::to_string(entry)<span class="question-mark">?</span>;
<a href=#186 id=186 data-nosnippet>186</a>
<a href=#187 id=187 data-nosnippet>187</a>        <span class="kw">let </span><span class="kw-2">mut </span>file = OpenOptions::new()
<a href=#188 id=188 data-nosnippet>188</a>            .create(<span class="bool-val">true</span>)
<a href=#189 id=189 data-nosnippet>189</a>            .append(<span class="bool-val">true</span>)
<a href=#190 id=190 data-nosnippet>190</a>            .open(<span class="kw-2">&amp;</span><span class="self">self</span>.log_file_path)<span class="question-mark">?</span>;
<a href=#191 id=191 data-nosnippet>191</a>
<a href=#192 id=192 data-nosnippet>192</a>        <span class="macro">writeln!</span>(file, <span class="string">"{}"</span>, json_line)<span class="question-mark">?</span>;
<a href=#193 id=193 data-nosnippet>193</a>        file.flush()<span class="question-mark">?</span>;
<a href=#194 id=194 data-nosnippet>194</a>
<a href=#195 id=195 data-nosnippet>195</a>        <span class="prelude-val">Ok</span>(())
<a href=#196 id=196 data-nosnippet>196</a>    }
<a href=#197 id=197 data-nosnippet>197</a>
<a href=#198 id=198 data-nosnippet>198</a>    <span class="kw">fn </span>format_entry(<span class="kw-2">&amp;</span><span class="self">self</span>, entry: <span class="kw-2">&amp;</span>LogEntry) -&gt; String {
<a href=#199 id=199 data-nosnippet>199</a>        <span class="macro">format!</span>(
<a href=#200 id=200 data-nosnippet>200</a>            <span class="string">"[{}] [{:?}] [{:?}] {} - {}"</span>,
<a href=#201 id=201 data-nosnippet>201</a>            entry.timestamp.format(<span class="string">"%Y-%m-%d %H:%M:%S UTC"</span>),
<a href=#202 id=202 data-nosnippet>202</a>            entry.level,
<a href=#203 id=203 data-nosnippet>203</a>            entry.category,
<a href=#204 id=204 data-nosnippet>204</a>            entry.user_id.as_deref().unwrap_or(<span class="string">"system"</span>),
<a href=#205 id=205 data-nosnippet>205</a>            entry.message
<a href=#206 id=206 data-nosnippet>206</a>        )
<a href=#207 id=207 data-nosnippet>207</a>    }
<a href=#208 id=208 data-nosnippet>208</a>
<a href=#209 id=209 data-nosnippet>209</a>    <span class="kw">pub fn </span>ensure_log_directory(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#210 id=210 data-nosnippet>210</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = Path::new(<span class="kw-2">&amp;</span><span class="self">self</span>.log_file_path).parent() {
<a href=#211 id=211 data-nosnippet>211</a>            std::fs::create_dir_all(parent)<span class="question-mark">?</span>;
<a href=#212 id=212 data-nosnippet>212</a>        }
<a href=#213 id=213 data-nosnippet>213</a>        <span class="prelude-val">Ok</span>(())
<a href=#214 id=214 data-nosnippet>214</a>    }
<a href=#215 id=215 data-nosnippet>215</a>}
<a href=#216 id=216 data-nosnippet>216</a>
<a href=#217 id=217 data-nosnippet>217</a><span class="attr">#[cfg(test)]
<a href=#218 id=218 data-nosnippet>218</a></span><span class="kw">mod </span>tests {
<a href=#219 id=219 data-nosnippet>219</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#220 id=220 data-nosnippet>220</a>    <span class="kw">use </span>tempfile::NamedTempFile;
<a href=#221 id=221 data-nosnippet>221</a>
<a href=#222 id=222 data-nosnippet>222</a>    <span class="attr">#[test]
<a href=#223 id=223 data-nosnippet>223</a>    </span><span class="kw">fn </span>test_logging() {
<a href=#224 id=224 data-nosnippet>224</a>        <span class="kw">let </span>temp_file = NamedTempFile::new().unwrap();
<a href=#225 id=225 data-nosnippet>225</a>        <span class="kw">let </span>logger = Logger::new(temp_file.path().to_string_lossy().to_string());
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#228 id=228 data-nosnippet>228</a>            id: Uuid::new_v4(),
<a href=#229 id=229 data-nosnippet>229</a>            timestamp: Utc::now(),
<a href=#230 id=230 data-nosnippet>230</a>            level: LogLevel::Info,
<a href=#231 id=231 data-nosnippet>231</a>            category: LogCategory::Authentication,
<a href=#232 id=232 data-nosnippet>232</a>            user_id: <span class="prelude-val">Some</span>(<span class="string">"test_user"</span>.to_string()),
<a href=#233 id=233 data-nosnippet>233</a>            session_id: <span class="prelude-val">None</span>,
<a href=#234 id=234 data-nosnippet>234</a>            message: <span class="string">"Test log entry"</span>.to_string(),
<a href=#235 id=235 data-nosnippet>235</a>            details: <span class="prelude-val">None</span>,
<a href=#236 id=236 data-nosnippet>236</a>        };
<a href=#237 id=237 data-nosnippet>237</a>
<a href=#238 id=238 data-nosnippet>238</a>        logger.log(entry).unwrap();
<a href=#239 id=239 data-nosnippet>239</a>
<a href=#240 id=240 data-nosnippet>240</a>        <span class="comment">// 验证文件内容
<a href=#241 id=241 data-nosnippet>241</a>        </span><span class="kw">let </span>content = std::fs::read_to_string(temp_file.path()).unwrap();
<a href=#242 id=242 data-nosnippet>242</a>        <span class="macro">assert!</span>(content.contains(<span class="string">"Test log entry"</span>));
<a href=#243 id=243 data-nosnippet>243</a>    }
<a href=#244 id=244 data-nosnippet>244</a>}</code></pre></div></section></main></body></html>