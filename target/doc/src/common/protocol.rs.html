<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `common/src/protocol.rs`."><title>protocol.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">common/</div>protocol.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>chrono::{DateTime, Utc};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>sha2::{Digest, Sha256};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>uuid::Uuid;
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#7 id=7 data-nosnippet>7</a></span><span class="kw">pub struct </span>Message {
<a href=#8 id=8 data-nosnippet>8</a>    <span class="kw">pub </span>id: Uuid,
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>timestamp: DateTime&lt;Utc&gt;,
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>message_type: MessageType,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>payload: Vec&lt;u8&gt;,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>checksum: String,
<a href=#13 id=13 data-nosnippet>13</a>}
<a href=#14 id=14 data-nosnippet>14</a>
<a href=#15 id=15 data-nosnippet>15</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#16 id=16 data-nosnippet>16</a></span><span class="kw">pub enum </span>MessageType {
<a href=#17 id=17 data-nosnippet>17</a>    <span class="comment">// 控制端发送的消息
<a href=#18 id=18 data-nosnippet>18</a>    </span>Command(CommandMessage),
<a href=#19 id=19 data-nosnippet>19</a>    FileUpload(FileUploadMessage),
<a href=#20 id=20 data-nosnippet>20</a>    FileDownload(FileDownloadMessage),
<a href=#21 id=21 data-nosnippet>21</a>    ProcessManagement(ProcessMessage),
<a href=#22 id=22 data-nosnippet>22</a>    SystemInfo,
<a href=#23 id=23 data-nosnippet>23</a>
<a href=#24 id=24 data-nosnippet>24</a>    <span class="comment">// 受控端发送的消息
<a href=#25 id=25 data-nosnippet>25</a>    </span>CommandResult(CommandResult),
<a href=#26 id=26 data-nosnippet>26</a>    FileData(FileData),
<a href=#27 id=27 data-nosnippet>27</a>    ProcessInfo(ProcessInfo),
<a href=#28 id=28 data-nosnippet>28</a>    SystemInfoResult(SystemInfoResult),
<a href=#29 id=29 data-nosnippet>29</a>    Heartbeat,
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>    <span class="comment">// 通用消息
<a href=#32 id=32 data-nosnippet>32</a>    </span>Error(ErrorMessage),
<a href=#33 id=33 data-nosnippet>33</a>    Ack,
<a href=#34 id=34 data-nosnippet>34</a>}
<a href=#35 id=35 data-nosnippet>35</a>
<a href=#36 id=36 data-nosnippet>36</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#37 id=37 data-nosnippet>37</a></span><span class="kw">pub struct </span>CommandMessage {
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>command: String,
<a href=#39 id=39 data-nosnippet>39</a>    <span class="kw">pub </span>args: Vec&lt;String&gt;,
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">pub </span>working_dir: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">pub </span>timeout: <span class="prelude-ty">Option</span>&lt;u64&gt;,
<a href=#42 id=42 data-nosnippet>42</a>}
<a href=#43 id=43 data-nosnippet>43</a>
<a href=#44 id=44 data-nosnippet>44</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#45 id=45 data-nosnippet>45</a></span><span class="kw">pub struct </span>CommandResult {
<a href=#46 id=46 data-nosnippet>46</a>    <span class="kw">pub </span>exit_code: i32,
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">pub </span>stdout: String,
<a href=#48 id=48 data-nosnippet>48</a>    <span class="kw">pub </span>stderr: String,
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>execution_time: u64,
<a href=#50 id=50 data-nosnippet>50</a>}
<a href=#51 id=51 data-nosnippet>51</a>
<a href=#52 id=52 data-nosnippet>52</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#53 id=53 data-nosnippet>53</a></span><span class="kw">pub struct </span>FileUploadMessage {
<a href=#54 id=54 data-nosnippet>54</a>    <span class="kw">pub </span>file_path: String,
<a href=#55 id=55 data-nosnippet>55</a>    <span class="kw">pub </span>file_size: u64,
<a href=#56 id=56 data-nosnippet>56</a>    <span class="kw">pub </span>chunk_index: u32,
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">pub </span>total_chunks: u32,
<a href=#58 id=58 data-nosnippet>58</a>    <span class="kw">pub </span>data: Vec&lt;u8&gt;,
<a href=#59 id=59 data-nosnippet>59</a>}
<a href=#60 id=60 data-nosnippet>60</a>
<a href=#61 id=61 data-nosnippet>61</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#62 id=62 data-nosnippet>62</a></span><span class="kw">pub struct </span>FileDownloadMessage {
<a href=#63 id=63 data-nosnippet>63</a>    <span class="kw">pub </span>file_path: String,
<a href=#64 id=64 data-nosnippet>64</a>    <span class="kw">pub </span>chunk_size: <span class="prelude-ty">Option</span>&lt;u32&gt;,
<a href=#65 id=65 data-nosnippet>65</a>}
<a href=#66 id=66 data-nosnippet>66</a>
<a href=#67 id=67 data-nosnippet>67</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#68 id=68 data-nosnippet>68</a></span><span class="kw">pub struct </span>FileData {
<a href=#69 id=69 data-nosnippet>69</a>    <span class="kw">pub </span>file_path: String,
<a href=#70 id=70 data-nosnippet>70</a>    <span class="kw">pub </span>chunk_index: u32,
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">pub </span>total_chunks: u32,
<a href=#72 id=72 data-nosnippet>72</a>    <span class="kw">pub </span>data: Vec&lt;u8&gt;,
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">pub </span>is_complete: bool,
<a href=#74 id=74 data-nosnippet>74</a>}
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#77 id=77 data-nosnippet>77</a></span><span class="kw">pub struct </span>ProcessMessage {
<a href=#78 id=78 data-nosnippet>78</a>    <span class="kw">pub </span>action: ProcessAction,
<a href=#79 id=79 data-nosnippet>79</a>    <span class="kw">pub </span>process_id: <span class="prelude-ty">Option</span>&lt;u32&gt;,
<a href=#80 id=80 data-nosnippet>80</a>    <span class="kw">pub </span>process_name: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#81 id=81 data-nosnippet>81</a>}
<a href=#82 id=82 data-nosnippet>82</a>
<a href=#83 id=83 data-nosnippet>83</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#84 id=84 data-nosnippet>84</a></span><span class="kw">pub enum </span>ProcessAction {
<a href=#85 id=85 data-nosnippet>85</a>    List,
<a href=#86 id=86 data-nosnippet>86</a>    Kill,
<a href=#87 id=87 data-nosnippet>87</a>    Start(String, Vec&lt;String&gt;),
<a href=#88 id=88 data-nosnippet>88</a>}
<a href=#89 id=89 data-nosnippet>89</a>
<a href=#90 id=90 data-nosnippet>90</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#91 id=91 data-nosnippet>91</a></span><span class="kw">pub struct </span>ProcessInfo {
<a href=#92 id=92 data-nosnippet>92</a>    <span class="kw">pub </span>processes: Vec&lt;ProcessDetails&gt;,
<a href=#93 id=93 data-nosnippet>93</a>}
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#96 id=96 data-nosnippet>96</a></span><span class="kw">pub struct </span>ProcessDetails {
<a href=#97 id=97 data-nosnippet>97</a>    <span class="kw">pub </span>pid: u32,
<a href=#98 id=98 data-nosnippet>98</a>    <span class="kw">pub </span>name: String,
<a href=#99 id=99 data-nosnippet>99</a>    <span class="kw">pub </span>cpu_usage: f32,
<a href=#100 id=100 data-nosnippet>100</a>    <span class="kw">pub </span>memory_usage: u64,
<a href=#101 id=101 data-nosnippet>101</a>    <span class="kw">pub </span>status: String,
<a href=#102 id=102 data-nosnippet>102</a>}
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#105 id=105 data-nosnippet>105</a></span><span class="kw">pub struct </span>SystemInfoResult {
<a href=#106 id=106 data-nosnippet>106</a>    <span class="kw">pub </span>hostname: String,
<a href=#107 id=107 data-nosnippet>107</a>    <span class="kw">pub </span>os_version: String,
<a href=#108 id=108 data-nosnippet>108</a>    <span class="kw">pub </span>kernel_version: String,
<a href=#109 id=109 data-nosnippet>109</a>    <span class="kw">pub </span>architecture: String,
<a href=#110 id=110 data-nosnippet>110</a>    <span class="kw">pub </span>cpu_info: String,
<a href=#111 id=111 data-nosnippet>111</a>    <span class="kw">pub </span>memory_total: u64,
<a href=#112 id=112 data-nosnippet>112</a>    <span class="kw">pub </span>memory_available: u64,
<a href=#113 id=113 data-nosnippet>113</a>    <span class="kw">pub </span>disk_info: Vec&lt;DiskInfo&gt;,
<a href=#114 id=114 data-nosnippet>114</a>    <span class="kw">pub </span>network_interfaces: Vec&lt;NetworkInterface&gt;,
<a href=#115 id=115 data-nosnippet>115</a>}
<a href=#116 id=116 data-nosnippet>116</a>
<a href=#117 id=117 data-nosnippet>117</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#118 id=118 data-nosnippet>118</a></span><span class="kw">pub struct </span>DiskInfo {
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">pub </span>device: String,
<a href=#120 id=120 data-nosnippet>120</a>    <span class="kw">pub </span>mount_point: String,
<a href=#121 id=121 data-nosnippet>121</a>    <span class="kw">pub </span>total_space: u64,
<a href=#122 id=122 data-nosnippet>122</a>    <span class="kw">pub </span>available_space: u64,
<a href=#123 id=123 data-nosnippet>123</a>    <span class="kw">pub </span>filesystem: String,
<a href=#124 id=124 data-nosnippet>124</a>}
<a href=#125 id=125 data-nosnippet>125</a>
<a href=#126 id=126 data-nosnippet>126</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#127 id=127 data-nosnippet>127</a></span><span class="kw">pub struct </span>NetworkInterface {
<a href=#128 id=128 data-nosnippet>128</a>    <span class="kw">pub </span>name: String,
<a href=#129 id=129 data-nosnippet>129</a>    <span class="kw">pub </span>ip_addresses: Vec&lt;String&gt;,
<a href=#130 id=130 data-nosnippet>130</a>    <span class="kw">pub </span>mac_address: String,
<a href=#131 id=131 data-nosnippet>131</a>    <span class="kw">pub </span>is_up: bool,
<a href=#132 id=132 data-nosnippet>132</a>}
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#135 id=135 data-nosnippet>135</a></span><span class="kw">pub struct </span>ErrorMessage {
<a href=#136 id=136 data-nosnippet>136</a>    <span class="kw">pub </span>error_code: u32,
<a href=#137 id=137 data-nosnippet>137</a>    <span class="kw">pub </span>error_message: String,
<a href=#138 id=138 data-nosnippet>138</a>    <span class="kw">pub </span>details: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#139 id=139 data-nosnippet>139</a>}
<a href=#140 id=140 data-nosnippet>140</a>
<a href=#141 id=141 data-nosnippet>141</a><span class="kw">impl </span>Message {
<a href=#142 id=142 data-nosnippet>142</a>    <span class="kw">pub fn </span>new(message_type: MessageType) -&gt; <span class="self">Self </span>{
<a href=#143 id=143 data-nosnippet>143</a>        <span class="kw">let </span>payload = serde_json::to_vec(<span class="kw-2">&amp;</span>message_type).unwrap_or_default();
<a href=#144 id=144 data-nosnippet>144</a>        <span class="kw">let </span>checksum = <span class="macro">format!</span>(<span class="string">"{:x}"</span>, Sha256::digest(<span class="kw-2">&amp;</span>payload));
<a href=#145 id=145 data-nosnippet>145</a>
<a href=#146 id=146 data-nosnippet>146</a>        <span class="self">Self </span>{
<a href=#147 id=147 data-nosnippet>147</a>            id: Uuid::new_v4(),
<a href=#148 id=148 data-nosnippet>148</a>            timestamp: Utc::now(),
<a href=#149 id=149 data-nosnippet>149</a>            message_type,
<a href=#150 id=150 data-nosnippet>150</a>            payload,
<a href=#151 id=151 data-nosnippet>151</a>            checksum,
<a href=#152 id=152 data-nosnippet>152</a>        }
<a href=#153 id=153 data-nosnippet>153</a>    }
<a href=#154 id=154 data-nosnippet>154</a>
<a href=#155 id=155 data-nosnippet>155</a>    <span class="kw">pub fn </span>verify_checksum(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#156 id=156 data-nosnippet>156</a>        <span class="kw">let </span>expected = <span class="macro">format!</span>(<span class="string">"{:x}"</span>, Sha256::digest(<span class="kw-2">&amp;</span><span class="self">self</span>.payload));
<a href=#157 id=157 data-nosnippet>157</a>        <span class="self">self</span>.checksum == expected
<a href=#158 id=158 data-nosnippet>158</a>    }
<a href=#159 id=159 data-nosnippet>159</a>}</code></pre></div></section></main></body></html>