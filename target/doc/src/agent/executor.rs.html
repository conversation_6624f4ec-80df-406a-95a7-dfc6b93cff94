<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/executor.rs`."><title>executor.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>executor.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>common::{CommandResult, ProcessAction, ProcessDetails, ProcessInfo};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::process::Stdio;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::time::{Duration, Instant};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tokio::process::Command;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>tokio::time::timeout;
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub struct </span>CommandExecutor {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="comment">// 可以添加配置选项，如允许的命令列表等
<a href=#10 id=10 data-nosnippet>10</a></span>}
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">impl </span>CommandExecutor {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#14 id=14 data-nosnippet>14</a>        <span class="self">Self </span>{}
<a href=#15 id=15 data-nosnippet>15</a>    }
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub async fn </span>execute_command(
<a href=#18 id=18 data-nosnippet>18</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#19 id=19 data-nosnippet>19</a>        command: <span class="kw-2">&amp;</span>str,
<a href=#20 id=20 data-nosnippet>20</a>        args: <span class="kw-2">&amp;</span>[String],
<a href=#21 id=21 data-nosnippet>21</a>        working_dir: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>str&gt;,
<a href=#22 id=22 data-nosnippet>22</a>        timeout_secs: <span class="prelude-ty">Option</span>&lt;u64&gt;,
<a href=#23 id=23 data-nosnippet>23</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;CommandResult&gt; {
<a href=#24 id=24 data-nosnippet>24</a>        <span class="kw">let </span>start_time = Instant::now();
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a>        <span class="comment">// 安全检查：验证命令是否被允许
<a href=#27 id=27 data-nosnippet>27</a>        </span><span class="kw">if </span>!<span class="self">self</span>.is_command_allowed(command) {
<a href=#28 id=28 data-nosnippet>28</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(CommandResult {
<a href=#29 id=29 data-nosnippet>29</a>                exit_code: -<span class="number">1</span>,
<a href=#30 id=30 data-nosnippet>30</a>                stdout: String::new(),
<a href=#31 id=31 data-nosnippet>31</a>                stderr: <span class="macro">format!</span>(<span class="string">"Command not allowed: {}"</span>, command),
<a href=#32 id=32 data-nosnippet>32</a>                execution_time: <span class="number">0</span>,
<a href=#33 id=33 data-nosnippet>33</a>            });
<a href=#34 id=34 data-nosnippet>34</a>        }
<a href=#35 id=35 data-nosnippet>35</a>
<a href=#36 id=36 data-nosnippet>36</a>        <span class="kw">let </span><span class="kw-2">mut </span>cmd = Command::new(command);
<a href=#37 id=37 data-nosnippet>37</a>        cmd.args(args).stdout(Stdio::piped()).stderr(Stdio::piped());
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(dir) = working_dir {
<a href=#40 id=40 data-nosnippet>40</a>            cmd.current_dir(dir);
<a href=#41 id=41 data-nosnippet>41</a>        }
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a>        <span class="kw">let </span>timeout_duration = Duration::from_secs(timeout_secs.unwrap_or(<span class="number">30</span>));
<a href=#44 id=44 data-nosnippet>44</a>
<a href=#45 id=45 data-nosnippet>45</a>        <span class="kw">let </span>result = timeout(timeout_duration, cmd.output()).<span class="kw">await</span>;
<a href=#46 id=46 data-nosnippet>46</a>
<a href=#47 id=47 data-nosnippet>47</a>        <span class="kw">let </span>execution_time = start_time.elapsed().as_millis() <span class="kw">as </span>u64;
<a href=#48 id=48 data-nosnippet>48</a>
<a href=#49 id=49 data-nosnippet>49</a>        <span class="kw">match </span>result {
<a href=#50 id=50 data-nosnippet>50</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Ok</span>(output)) =&gt; <span class="prelude-val">Ok</span>(CommandResult {
<a href=#51 id=51 data-nosnippet>51</a>                exit_code: output.status.code().unwrap_or(-<span class="number">1</span>),
<a href=#52 id=52 data-nosnippet>52</a>                stdout: String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).to_string(),
<a href=#53 id=53 data-nosnippet>53</a>                stderr: String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stderr).to_string(),
<a href=#54 id=54 data-nosnippet>54</a>                execution_time,
<a href=#55 id=55 data-nosnippet>55</a>            }),
<a href=#56 id=56 data-nosnippet>56</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Err</span>(e)) =&gt; <span class="prelude-val">Ok</span>(CommandResult {
<a href=#57 id=57 data-nosnippet>57</a>                exit_code: -<span class="number">1</span>,
<a href=#58 id=58 data-nosnippet>58</a>                stdout: String::new(),
<a href=#59 id=59 data-nosnippet>59</a>                stderr: <span class="macro">format!</span>(<span class="string">"Failed to execute command: {}"</span>, e),
<a href=#60 id=60 data-nosnippet>60</a>                execution_time,
<a href=#61 id=61 data-nosnippet>61</a>            }),
<a href=#62 id=62 data-nosnippet>62</a>            <span class="prelude-val">Err</span>(<span class="kw">_</span>) =&gt; <span class="prelude-val">Ok</span>(CommandResult {
<a href=#63 id=63 data-nosnippet>63</a>                exit_code: -<span class="number">1</span>,
<a href=#64 id=64 data-nosnippet>64</a>                stdout: String::new(),
<a href=#65 id=65 data-nosnippet>65</a>                stderr: <span class="string">"Command execution timed out"</span>.to_string(),
<a href=#66 id=66 data-nosnippet>66</a>                execution_time,
<a href=#67 id=67 data-nosnippet>67</a>            }),
<a href=#68 id=68 data-nosnippet>68</a>        }
<a href=#69 id=69 data-nosnippet>69</a>    }
<a href=#70 id=70 data-nosnippet>70</a>
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">pub async fn </span>manage_process(<span class="kw-2">&amp;</span><span class="self">self</span>, action: ProcessAction) -&gt; <span class="prelude-ty">Result</span>&lt;ProcessInfo&gt; {
<a href=#72 id=72 data-nosnippet>72</a>        <span class="kw">match </span>action {
<a href=#73 id=73 data-nosnippet>73</a>            ProcessAction::List =&gt; <span class="self">self</span>.list_processes().<span class="kw">await</span>,
<a href=#74 id=74 data-nosnippet>74</a>            ProcessAction::Kill =&gt; {
<a href=#75 id=75 data-nosnippet>75</a>                <span class="comment">// 实现进程终止逻辑
<a href=#76 id=76 data-nosnippet>76</a>                </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Process kill not implemented"</span>))
<a href=#77 id=77 data-nosnippet>77</a>            }
<a href=#78 id=78 data-nosnippet>78</a>            ProcessAction::Start(command, args) =&gt; {
<a href=#79 id=79 data-nosnippet>79</a>                <span class="comment">// 启动新进程
<a href=#80 id=80 data-nosnippet>80</a>                </span><span class="self">self</span>.start_process(<span class="kw-2">&amp;</span>command, <span class="kw-2">&amp;</span>args).<span class="kw">await
<a href=#81 id=81 data-nosnippet>81</a>            </span>}
<a href=#82 id=82 data-nosnippet>82</a>        }
<a href=#83 id=83 data-nosnippet>83</a>    }
<a href=#84 id=84 data-nosnippet>84</a>
<a href=#85 id=85 data-nosnippet>85</a>    <span class="kw">async fn </span>list_processes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;ProcessInfo&gt; {
<a href=#86 id=86 data-nosnippet>86</a>        <span class="kw">let </span>output = Command::new(<span class="string">"ps"</span>)
<a href=#87 id=87 data-nosnippet>87</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"aux"</span>, <span class="string">"--no-headers"</span>])
<a href=#88 id=88 data-nosnippet>88</a>            .output()
<a href=#89 id=89 data-nosnippet>89</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#90 id=90 data-nosnippet>90</a>
<a href=#91 id=91 data-nosnippet>91</a>        <span class="kw">if </span>!output.status.success() {
<a href=#92 id=92 data-nosnippet>92</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Failed to list processes"</span>));
<a href=#93 id=93 data-nosnippet>93</a>        }
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a>        <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#96 id=96 data-nosnippet>96</a>        <span class="kw">let </span><span class="kw-2">mut </span>processes = Vec::new();
<a href=#97 id=97 data-nosnippet>97</a>
<a href=#98 id=98 data-nosnippet>98</a>        <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#99 id=99 data-nosnippet>99</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(process) = <span class="self">self</span>.parse_ps_line(line) {
<a href=#100 id=100 data-nosnippet>100</a>                processes.push(process);
<a href=#101 id=101 data-nosnippet>101</a>            }
<a href=#102 id=102 data-nosnippet>102</a>        }
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a>        <span class="prelude-val">Ok</span>(ProcessInfo { processes })
<a href=#105 id=105 data-nosnippet>105</a>    }
<a href=#106 id=106 data-nosnippet>106</a>
<a href=#107 id=107 data-nosnippet>107</a>    <span class="kw">fn </span>parse_ps_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;ProcessDetails&gt; {
<a href=#108 id=108 data-nosnippet>108</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">if </span>parts.len() &gt;= <span class="number">11 </span>{
<a href=#110 id=110 data-nosnippet>110</a>            <span class="comment">// ps aux格式: USER PID %CPU %MEM VSZ RSS TTY STAT START TIME COMMAND
<a href=#111 id=111 data-nosnippet>111</a>            </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(pid) = parts[<span class="number">1</span>].parse::&lt;u32&gt;() {
<a href=#112 id=112 data-nosnippet>112</a>                <span class="kw">if let </span><span class="prelude-val">Ok</span>(cpu_usage) = parts[<span class="number">2</span>].parse::&lt;f32&gt;() {
<a href=#113 id=113 data-nosnippet>113</a>                    <span class="kw">if let </span><span class="prelude-val">Ok</span>(memory_kb) = parts[<span class="number">5</span>].parse::&lt;u64&gt;() {
<a href=#114 id=114 data-nosnippet>114</a>                        <span class="kw">return </span><span class="prelude-val">Some</span>(ProcessDetails {
<a href=#115 id=115 data-nosnippet>115</a>                            pid,
<a href=#116 id=116 data-nosnippet>116</a>                            name: parts[<span class="number">10</span>..].join(<span class="string">" "</span>),
<a href=#117 id=117 data-nosnippet>117</a>                            cpu_usage,
<a href=#118 id=118 data-nosnippet>118</a>                            memory_usage: memory_kb * <span class="number">1024</span>, <span class="comment">// 转换为字节
<a href=#119 id=119 data-nosnippet>119</a>                            </span>status: parts[<span class="number">7</span>].to_string(),
<a href=#120 id=120 data-nosnippet>120</a>                        });
<a href=#121 id=121 data-nosnippet>121</a>                    }
<a href=#122 id=122 data-nosnippet>122</a>                }
<a href=#123 id=123 data-nosnippet>123</a>            }
<a href=#124 id=124 data-nosnippet>124</a>        }
<a href=#125 id=125 data-nosnippet>125</a>        <span class="prelude-val">None
<a href=#126 id=126 data-nosnippet>126</a>    </span>}
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>    <span class="kw">async fn </span>start_process(<span class="kw-2">&amp;</span><span class="self">self</span>, command: <span class="kw-2">&amp;</span>str, args: <span class="kw-2">&amp;</span>[String]) -&gt; <span class="prelude-ty">Result</span>&lt;ProcessInfo&gt; {
<a href=#129 id=129 data-nosnippet>129</a>        <span class="kw">if </span>!<span class="self">self</span>.is_command_allowed(command) {
<a href=#130 id=130 data-nosnippet>130</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Command not allowed: {}"</span>, command));
<a href=#131 id=131 data-nosnippet>131</a>        }
<a href=#132 id=132 data-nosnippet>132</a>
<a href=#133 id=133 data-nosnippet>133</a>        <span class="kw">let </span><span class="kw-2">mut </span>cmd = Command::new(command);
<a href=#134 id=134 data-nosnippet>134</a>        cmd.args(args).stdout(Stdio::null()).stderr(Stdio::null());
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>        <span class="kw">let </span>child = cmd.spawn()<span class="question-mark">?</span>;
<a href=#137 id=137 data-nosnippet>137</a>        <span class="kw">let </span>pid = child.id().unwrap_or(<span class="number">0</span>);
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>        <span class="comment">// 返回新启动的进程信息
<a href=#140 id=140 data-nosnippet>140</a>        </span><span class="kw">let </span>process = ProcessDetails {
<a href=#141 id=141 data-nosnippet>141</a>            pid,
<a href=#142 id=142 data-nosnippet>142</a>            name: <span class="macro">format!</span>(<span class="string">"{} {}"</span>, command, args.join(<span class="string">" "</span>)),
<a href=#143 id=143 data-nosnippet>143</a>            cpu_usage: <span class="number">0.0</span>,
<a href=#144 id=144 data-nosnippet>144</a>            memory_usage: <span class="number">0</span>,
<a href=#145 id=145 data-nosnippet>145</a>            status: <span class="string">"Running"</span>.to_string(),
<a href=#146 id=146 data-nosnippet>146</a>        };
<a href=#147 id=147 data-nosnippet>147</a>
<a href=#148 id=148 data-nosnippet>148</a>        <span class="prelude-val">Ok</span>(ProcessInfo {
<a href=#149 id=149 data-nosnippet>149</a>            processes: <span class="macro">vec!</span>[process],
<a href=#150 id=150 data-nosnippet>150</a>        })
<a href=#151 id=151 data-nosnippet>151</a>    }
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>    <span class="kw">fn </span>is_command_allowed(<span class="kw-2">&amp;</span><span class="self">self</span>, command: <span class="kw-2">&amp;</span>str) -&gt; bool {
<a href=#154 id=154 data-nosnippet>154</a>        <span class="comment">// 实现命令白名单或黑名单逻辑
<a href=#155 id=155 data-nosnippet>155</a>        // 这里是一个简单的实现，实际应用中应该更严格
<a href=#156 id=156 data-nosnippet>156</a>        </span><span class="kw">let </span>allowed_commands = [
<a href=#157 id=157 data-nosnippet>157</a>            <span class="string">"ls"</span>,
<a href=#158 id=158 data-nosnippet>158</a>            <span class="string">"cat"</span>,
<a href=#159 id=159 data-nosnippet>159</a>            <span class="string">"grep"</span>,
<a href=#160 id=160 data-nosnippet>160</a>            <span class="string">"find"</span>,
<a href=#161 id=161 data-nosnippet>161</a>            <span class="string">"ps"</span>,
<a href=#162 id=162 data-nosnippet>162</a>            <span class="string">"top"</span>,
<a href=#163 id=163 data-nosnippet>163</a>            <span class="string">"df"</span>,
<a href=#164 id=164 data-nosnippet>164</a>            <span class="string">"free"</span>,
<a href=#165 id=165 data-nosnippet>165</a>            <span class="string">"uname"</span>,
<a href=#166 id=166 data-nosnippet>166</a>            <span class="string">"whoami"</span>,
<a href=#167 id=167 data-nosnippet>167</a>            <span class="string">"id"</span>,
<a href=#168 id=168 data-nosnippet>168</a>            <span class="string">"pwd"</span>,
<a href=#169 id=169 data-nosnippet>169</a>            <span class="string">"date"</span>,
<a href=#170 id=170 data-nosnippet>170</a>            <span class="string">"uptime"</span>,
<a href=#171 id=171 data-nosnippet>171</a>            <span class="string">"netstat"</span>,
<a href=#172 id=172 data-nosnippet>172</a>            <span class="string">"ss"</span>,
<a href=#173 id=173 data-nosnippet>173</a>            <span class="string">"systemctl"</span>,
<a href=#174 id=174 data-nosnippet>174</a>            <span class="string">"service"</span>,
<a href=#175 id=175 data-nosnippet>175</a>            <span class="string">"journalctl"</span>,
<a href=#176 id=176 data-nosnippet>176</a>            <span class="string">"dmesg"</span>,
<a href=#177 id=177 data-nosnippet>177</a>            <span class="string">"lsof"</span>,
<a href=#178 id=178 data-nosnippet>178</a>            <span class="string">"iptables"</span>,
<a href=#179 id=179 data-nosnippet>179</a>            <span class="string">"ip"</span>,
<a href=#180 id=180 data-nosnippet>180</a>            <span class="string">"ifconfig"</span>,
<a href=#181 id=181 data-nosnippet>181</a>            <span class="string">"ping"</span>,
<a href=#182 id=182 data-nosnippet>182</a>            <span class="string">"curl"</span>,
<a href=#183 id=183 data-nosnippet>183</a>            <span class="string">"wget"</span>,
<a href=#184 id=184 data-nosnippet>184</a>        ];
<a href=#185 id=185 data-nosnippet>185</a>
<a href=#186 id=186 data-nosnippet>186</a>        <span class="kw">let </span>dangerous_commands = [
<a href=#187 id=187 data-nosnippet>187</a>            <span class="string">"rm"</span>, <span class="string">"rmdir"</span>, <span class="string">"dd"</span>, <span class="string">"mkfs"</span>, <span class="string">"fdisk"</span>, <span class="string">"parted"</span>, <span class="string">"shutdown"</span>, <span class="string">"reboot"</span>, <span class="string">"halt"</span>,
<a href=#188 id=188 data-nosnippet>188</a>            <span class="string">"poweroff"</span>, <span class="string">"passwd"</span>, <span class="string">"su"</span>, <span class="string">"sudo"</span>, <span class="string">"chmod"</span>, <span class="string">"chown"</span>,
<a href=#189 id=189 data-nosnippet>189</a>        ];
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a>        <span class="comment">// 检查是否在危险命令列表中
<a href=#192 id=192 data-nosnippet>192</a>        </span><span class="kw">if </span>dangerous_commands.contains(<span class="kw-2">&amp;</span>command) {
<a href=#193 id=193 data-nosnippet>193</a>            <span class="kw">return </span><span class="bool-val">false</span>;
<a href=#194 id=194 data-nosnippet>194</a>        }
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>        <span class="comment">// 检查是否在允许命令列表中
<a href=#197 id=197 data-nosnippet>197</a>        </span>allowed_commands.contains(<span class="kw-2">&amp;</span>command)
<a href=#198 id=198 data-nosnippet>198</a>            || command.starts_with(<span class="string">"/usr/bin/"</span>)
<a href=#199 id=199 data-nosnippet>199</a>            || command.starts_with(<span class="string">"/bin/"</span>)
<a href=#200 id=200 data-nosnippet>200</a>    }
<a href=#201 id=201 data-nosnippet>201</a>
<a href=#202 id=202 data-nosnippet>202</a>    <span class="kw">pub async fn </span>execute_shell_script(
<a href=#203 id=203 data-nosnippet>203</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#204 id=204 data-nosnippet>204</a>        script: <span class="kw-2">&amp;</span>str,
<a href=#205 id=205 data-nosnippet>205</a>        timeout_secs: <span class="prelude-ty">Option</span>&lt;u64&gt;,
<a href=#206 id=206 data-nosnippet>206</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;CommandResult&gt; {
<a href=#207 id=207 data-nosnippet>207</a>        <span class="comment">// 将脚本写入临时文件
<a href=#208 id=208 data-nosnippet>208</a>        </span><span class="kw">let </span>script_path = <span class="macro">format!</span>(<span class="string">"/tmp/script_{}.sh"</span>, uuid::Uuid::new_v4());
<a href=#209 id=209 data-nosnippet>209</a>        tokio::fs::write(<span class="kw-2">&amp;</span>script_path, script).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#210 id=210 data-nosnippet>210</a>
<a href=#211 id=211 data-nosnippet>211</a>        <span class="comment">// 设置执行权限
<a href=#212 id=212 data-nosnippet>212</a>        </span>Command::new(<span class="string">"chmod"</span>)
<a href=#213 id=213 data-nosnippet>213</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"+x"</span>, <span class="kw-2">&amp;</span>script_path])
<a href=#214 id=214 data-nosnippet>214</a>            .output()
<a href=#215 id=215 data-nosnippet>215</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#216 id=216 data-nosnippet>216</a>
<a href=#217 id=217 data-nosnippet>217</a>        <span class="comment">// 执行脚本
<a href=#218 id=218 data-nosnippet>218</a>        </span><span class="kw">let </span>result = <span class="self">self
<a href=#219 id=219 data-nosnippet>219</a>            </span>.execute_command(<span class="string">"bash"</span>, <span class="kw-2">&amp;</span>[script_path.clone()], <span class="prelude-val">None</span>, timeout_secs)
<a href=#220 id=220 data-nosnippet>220</a>            .<span class="kw">await</span>;
<a href=#221 id=221 data-nosnippet>221</a>
<a href=#222 id=222 data-nosnippet>222</a>        <span class="comment">// 清理临时文件
<a href=#223 id=223 data-nosnippet>223</a>        </span><span class="kw">let _ </span>= tokio::fs::remove_file(<span class="kw-2">&amp;</span>script_path).<span class="kw">await</span>;
<a href=#224 id=224 data-nosnippet>224</a>
<a href=#225 id=225 data-nosnippet>225</a>        result
<a href=#226 id=226 data-nosnippet>226</a>    }
<a href=#227 id=227 data-nosnippet>227</a>
<a href=#228 id=228 data-nosnippet>228</a>    <span class="kw">pub async fn </span>kill_process(<span class="kw-2">&amp;</span><span class="self">self</span>, pid: u32) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#229 id=229 data-nosnippet>229</a>        <span class="kw">let </span>output = Command::new(<span class="string">"kill"</span>)
<a href=#230 id=230 data-nosnippet>230</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"-9"</span>, <span class="kw-2">&amp;</span>pid.to_string()])
<a href=#231 id=231 data-nosnippet>231</a>            .output()
<a href=#232 id=232 data-nosnippet>232</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#233 id=233 data-nosnippet>233</a>
<a href=#234 id=234 data-nosnippet>234</a>        <span class="kw">if </span>!output.status.success() {
<a href=#235 id=235 data-nosnippet>235</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(
<a href=#236 id=236 data-nosnippet>236</a>                <span class="string">"Failed to kill process {}: {}"</span>,
<a href=#237 id=237 data-nosnippet>237</a>                pid,
<a href=#238 id=238 data-nosnippet>238</a>                String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stderr)
<a href=#239 id=239 data-nosnippet>239</a>            ));
<a href=#240 id=240 data-nosnippet>240</a>        }
<a href=#241 id=241 data-nosnippet>241</a>
<a href=#242 id=242 data-nosnippet>242</a>        <span class="prelude-val">Ok</span>(())
<a href=#243 id=243 data-nosnippet>243</a>    }
<a href=#244 id=244 data-nosnippet>244</a>
<a href=#245 id=245 data-nosnippet>245</a>    <span class="kw">pub async fn </span>get_process_info(<span class="kw-2">&amp;</span><span class="self">self</span>, pid: u32) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;ProcessDetails&gt;&gt; {
<a href=#246 id=246 data-nosnippet>246</a>        <span class="kw">let </span>processes = <span class="self">self</span>.list_processes().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#247 id=247 data-nosnippet>247</a>        <span class="prelude-val">Ok</span>(processes.processes.into_iter().find(|p| p.pid == pid))
<a href=#248 id=248 data-nosnippet>248</a>    }
<a href=#249 id=249 data-nosnippet>249</a>}
<a href=#250 id=250 data-nosnippet>250</a>
<a href=#251 id=251 data-nosnippet>251</a><span class="attr">#[cfg(test)]
<a href=#252 id=252 data-nosnippet>252</a></span><span class="kw">mod </span>tests {
<a href=#253 id=253 data-nosnippet>253</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>    <span class="attr">#[tokio::test]
<a href=#256 id=256 data-nosnippet>256</a>    </span><span class="kw">async fn </span>test_command_execution() {
<a href=#257 id=257 data-nosnippet>257</a>        <span class="kw">let </span>executor = CommandExecutor::new();
<a href=#258 id=258 data-nosnippet>258</a>        <span class="kw">let </span>result = executor
<a href=#259 id=259 data-nosnippet>259</a>            .execute_command(<span class="string">"echo"</span>, <span class="kw-2">&amp;</span>[<span class="string">"hello"</span>.to_string()], <span class="prelude-val">None</span>, <span class="prelude-val">None</span>)
<a href=#260 id=260 data-nosnippet>260</a>            .<span class="kw">await
<a href=#261 id=261 data-nosnippet>261</a>            </span>.unwrap();
<a href=#262 id=262 data-nosnippet>262</a>
<a href=#263 id=263 data-nosnippet>263</a>        <span class="macro">assert_eq!</span>(result.exit_code, <span class="number">0</span>);
<a href=#264 id=264 data-nosnippet>264</a>        <span class="macro">assert_eq!</span>(result.stdout.trim(), <span class="string">"hello"</span>);
<a href=#265 id=265 data-nosnippet>265</a>    }
<a href=#266 id=266 data-nosnippet>266</a>
<a href=#267 id=267 data-nosnippet>267</a>    <span class="attr">#[tokio::test]
<a href=#268 id=268 data-nosnippet>268</a>    </span><span class="kw">async fn </span>test_command_security() {
<a href=#269 id=269 data-nosnippet>269</a>        <span class="kw">let </span>executor = CommandExecutor::new();
<a href=#270 id=270 data-nosnippet>270</a>        <span class="kw">let </span>result = executor
<a href=#271 id=271 data-nosnippet>271</a>            .execute_command(<span class="string">"rm"</span>, <span class="kw-2">&amp;</span>[<span class="string">"-rf"</span>.to_string(), <span class="string">"/"</span>.to_string()], <span class="prelude-val">None</span>, <span class="prelude-val">None</span>)
<a href=#272 id=272 data-nosnippet>272</a>            .<span class="kw">await
<a href=#273 id=273 data-nosnippet>273</a>            </span>.unwrap();
<a href=#274 id=274 data-nosnippet>274</a>
<a href=#275 id=275 data-nosnippet>275</a>        <span class="macro">assert_eq!</span>(result.exit_code, -<span class="number">1</span>);
<a href=#276 id=276 data-nosnippet>276</a>        <span class="macro">assert!</span>(result.stderr.contains(<span class="string">"not allowed"</span>));
<a href=#277 id=277 data-nosnippet>277</a>    }
<a href=#278 id=278 data-nosnippet>278</a>
<a href=#279 id=279 data-nosnippet>279</a>    <span class="attr">#[test]
<a href=#280 id=280 data-nosnippet>280</a>    </span><span class="kw">fn </span>test_ps_line_parsing() {
<a href=#281 id=281 data-nosnippet>281</a>        <span class="kw">let </span>executor = CommandExecutor::new();
<a href=#282 id=282 data-nosnippet>282</a>        <span class="kw">let </span>line = <span class="string">"root      1234  0.1  0.5  12345  6789 ?        S    10:00   0:01 /usr/bin/test"</span>;
<a href=#283 id=283 data-nosnippet>283</a>        <span class="kw">let </span>process = executor.parse_ps_line(line).unwrap();
<a href=#284 id=284 data-nosnippet>284</a>
<a href=#285 id=285 data-nosnippet>285</a>        <span class="macro">assert_eq!</span>(process.pid, <span class="number">1234</span>);
<a href=#286 id=286 data-nosnippet>286</a>        <span class="macro">assert_eq!</span>(process.cpu_usage, <span class="number">0.1</span>);
<a href=#287 id=287 data-nosnippet>287</a>        <span class="macro">assert_eq!</span>(process.memory_usage, <span class="number">6789 </span>* <span class="number">1024</span>);
<a href=#288 id=288 data-nosnippet>288</a>    }
<a href=#289 id=289 data-nosnippet>289</a>}</code></pre></div></section></main></body></html>