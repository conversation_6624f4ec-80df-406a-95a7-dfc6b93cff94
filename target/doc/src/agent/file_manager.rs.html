<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/file_manager.rs`."><title>file_manager.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>file_manager.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>common::FileData;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::collections::HashMap;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::path::{Path, PathBuf};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tokio::fs::{File, OpenOptions};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>tokio::io::{AsyncReadExt, AsyncSeekExt, AsyncWriteExt, SeekFrom};
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub struct </span>FileManager {
<a href=#9 id=9 data-nosnippet>9</a>    temp_files: HashMap&lt;String, TempFileInfo&gt;,
<a href=#10 id=10 data-nosnippet>10</a>}
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a><span class="attr">#[derive(Debug)]
<a href=#13 id=13 data-nosnippet>13</a></span><span class="kw">struct </span>TempFileInfo {
<a href=#14 id=14 data-nosnippet>14</a>    path: PathBuf,
<a href=#15 id=15 data-nosnippet>15</a>    total_chunks: u32,
<a href=#16 id=16 data-nosnippet>16</a>    received_chunks: Vec&lt;bool&gt;,
<a href=#17 id=17 data-nosnippet>17</a>    file_size: u64,
<a href=#18 id=18 data-nosnippet>18</a>}
<a href=#19 id=19 data-nosnippet>19</a>
<a href=#20 id=20 data-nosnippet>20</a><span class="kw">impl </span>FileManager {
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#22 id=22 data-nosnippet>22</a>        <span class="self">Self </span>{
<a href=#23 id=23 data-nosnippet>23</a>            temp_files: HashMap::new(),
<a href=#24 id=24 data-nosnippet>24</a>        }
<a href=#25 id=25 data-nosnippet>25</a>    }
<a href=#26 id=26 data-nosnippet>26</a>
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub async fn </span>receive_file_chunk(
<a href=#28 id=28 data-nosnippet>28</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#29 id=29 data-nosnippet>29</a>        file_path: <span class="kw-2">&amp;</span>str,
<a href=#30 id=30 data-nosnippet>30</a>        chunk_index: u32,
<a href=#31 id=31 data-nosnippet>31</a>        total_chunks: u32,
<a href=#32 id=32 data-nosnippet>32</a>        data: <span class="kw-2">&amp;</span>[u8],
<a href=#33 id=33 data-nosnippet>33</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#34 id=34 data-nosnippet>34</a>        <span class="comment">// 创建或获取临时文件信息
<a href=#35 id=35 data-nosnippet>35</a>        </span><span class="kw">let </span>temp_info = <span class="self">self
<a href=#36 id=36 data-nosnippet>36</a>            </span>.temp_files
<a href=#37 id=37 data-nosnippet>37</a>            .entry(file_path.to_string())
<a href=#38 id=38 data-nosnippet>38</a>            .or_insert_with(|| TempFileInfo {
<a href=#39 id=39 data-nosnippet>39</a>                path: PathBuf::from(<span class="macro">format!</span>(<span class="string">"/tmp/upload_{}"</span>, uuid::Uuid::new_v4())),
<a href=#40 id=40 data-nosnippet>40</a>                total_chunks,
<a href=#41 id=41 data-nosnippet>41</a>                received_chunks: <span class="macro">vec!</span>[<span class="bool-val">false</span>; total_chunks <span class="kw">as </span>usize],
<a href=#42 id=42 data-nosnippet>42</a>                file_size: <span class="number">0</span>,
<a href=#43 id=43 data-nosnippet>43</a>            });
<a href=#44 id=44 data-nosnippet>44</a>
<a href=#45 id=45 data-nosnippet>45</a>        <span class="comment">// 验证chunk索引
<a href=#46 id=46 data-nosnippet>46</a>        </span><span class="kw">if </span>chunk_index &gt;= total_chunks {
<a href=#47 id=47 data-nosnippet>47</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(
<a href=#48 id=48 data-nosnippet>48</a>                <span class="string">"Invalid chunk index: {} &gt;= {}"</span>,
<a href=#49 id=49 data-nosnippet>49</a>                chunk_index,
<a href=#50 id=50 data-nosnippet>50</a>                total_chunks
<a href=#51 id=51 data-nosnippet>51</a>            ));
<a href=#52 id=52 data-nosnippet>52</a>        }
<a href=#53 id=53 data-nosnippet>53</a>
<a href=#54 id=54 data-nosnippet>54</a>        <span class="comment">// 写入chunk数据
<a href=#55 id=55 data-nosnippet>55</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>file = OpenOptions::new()
<a href=#56 id=56 data-nosnippet>56</a>            .create(<span class="bool-val">true</span>)
<a href=#57 id=57 data-nosnippet>57</a>            .write(<span class="bool-val">true</span>)
<a href=#58 id=58 data-nosnippet>58</a>            .open(<span class="kw-2">&amp;</span>temp_info.path)
<a href=#59 id=59 data-nosnippet>59</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#60 id=60 data-nosnippet>60</a>
<a href=#61 id=61 data-nosnippet>61</a>        <span class="comment">// 计算chunk在文件中的位置
<a href=#62 id=62 data-nosnippet>62</a>        </span><span class="kw">let </span>chunk_size = <span class="number">64 </span>* <span class="number">1024</span>; <span class="comment">// 64KB
<a href=#63 id=63 data-nosnippet>63</a>        </span><span class="kw">let </span>offset = chunk_index <span class="kw">as </span>u64 * chunk_size;
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a>        file.seek(SeekFrom::Start(offset)).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#66 id=66 data-nosnippet>66</a>        file.write_all(data).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#67 id=67 data-nosnippet>67</a>        file.flush().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a>        <span class="comment">// 标记chunk为已接收
<a href=#70 id=70 data-nosnippet>70</a>        </span>temp_info.received_chunks[chunk_index <span class="kw">as </span>usize] = <span class="bool-val">true</span>;
<a href=#71 id=71 data-nosnippet>71</a>        temp_info.file_size += data.len() <span class="kw">as </span>u64;
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>        <span class="comment">// 检查是否所有chunk都已接收
<a href=#74 id=74 data-nosnippet>74</a>        </span><span class="kw">if </span>temp_info.received_chunks.iter().all(|<span class="kw-2">&amp;</span>received| received) {
<a href=#75 id=75 data-nosnippet>75</a>            <span class="self">self</span>.finalize_file_upload(file_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#76 id=76 data-nosnippet>76</a>        }
<a href=#77 id=77 data-nosnippet>77</a>
<a href=#78 id=78 data-nosnippet>78</a>        <span class="prelude-val">Ok</span>(())
<a href=#79 id=79 data-nosnippet>79</a>    }
<a href=#80 id=80 data-nosnippet>80</a>
<a href=#81 id=81 data-nosnippet>81</a>    <span class="kw">async fn </span>finalize_file_upload(<span class="kw-2">&amp;mut </span><span class="self">self</span>, file_path: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#82 id=82 data-nosnippet>82</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(temp_info) = <span class="self">self</span>.temp_files.remove(file_path) {
<a href=#83 id=83 data-nosnippet>83</a>            <span class="comment">// 确保目标目录存在
<a href=#84 id=84 data-nosnippet>84</a>            </span><span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = Path::new(file_path).parent() {
<a href=#85 id=85 data-nosnippet>85</a>                tokio::fs::create_dir_all(parent).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#86 id=86 data-nosnippet>86</a>            }
<a href=#87 id=87 data-nosnippet>87</a>
<a href=#88 id=88 data-nosnippet>88</a>            <span class="comment">// 移动临时文件到最终位置
<a href=#89 id=89 data-nosnippet>89</a>            </span>tokio::fs::rename(<span class="kw-2">&amp;</span>temp_info.path, file_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#90 id=90 data-nosnippet>90</a>
<a href=#91 id=91 data-nosnippet>91</a>            <span class="macro">log::info!</span>(<span class="string">"File upload completed: {}"</span>, file_path);
<a href=#92 id=92 data-nosnippet>92</a>        }
<a href=#93 id=93 data-nosnippet>93</a>
<a href=#94 id=94 data-nosnippet>94</a>        <span class="prelude-val">Ok</span>(())
<a href=#95 id=95 data-nosnippet>95</a>    }
<a href=#96 id=96 data-nosnippet>96</a>
<a href=#97 id=97 data-nosnippet>97</a>    <span class="kw">pub async fn </span>send_file(<span class="kw-2">&amp;</span><span class="self">self</span>, file_path: <span class="kw-2">&amp;</span>str, chunk_size: u32) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;FileData&gt;&gt; {
<a href=#98 id=98 data-nosnippet>98</a>        <span class="kw">let </span>path = Path::new(file_path);
<a href=#99 id=99 data-nosnippet>99</a>        <span class="kw">if </span>!path.exists() {
<a href=#100 id=100 data-nosnippet>100</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"File not found: {}"</span>, file_path));
<a href=#101 id=101 data-nosnippet>101</a>        }
<a href=#102 id=102 data-nosnippet>102</a>
<a href=#103 id=103 data-nosnippet>103</a>        <span class="kw">let </span><span class="kw-2">mut </span>file = File::open(path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#104 id=104 data-nosnippet>104</a>        <span class="kw">let </span>file_size = file.metadata().<span class="kw">await</span><span class="question-mark">?</span>.len();
<a href=#105 id=105 data-nosnippet>105</a>
<a href=#106 id=106 data-nosnippet>106</a>        <span class="kw">let </span>total_chunks = ((file_size + chunk_size <span class="kw">as </span>u64 - <span class="number">1</span>) / chunk_size <span class="kw">as </span>u64) <span class="kw">as </span>u32;
<a href=#107 id=107 data-nosnippet>107</a>        <span class="kw">let </span><span class="kw-2">mut </span>chunks = Vec::new();
<a href=#108 id=108 data-nosnippet>108</a>
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">for </span>chunk_index <span class="kw">in </span><span class="number">0</span>..total_chunks {
<a href=#110 id=110 data-nosnippet>110</a>            <span class="kw">let </span><span class="kw-2">mut </span>buffer = <span class="macro">vec!</span>[<span class="number">0u8</span>; chunk_size <span class="kw">as </span>usize];
<a href=#111 id=111 data-nosnippet>111</a>            <span class="kw">let </span>bytes_read = file.read(<span class="kw-2">&amp;mut </span>buffer).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#112 id=112 data-nosnippet>112</a>            buffer.truncate(bytes_read);
<a href=#113 id=113 data-nosnippet>113</a>
<a href=#114 id=114 data-nosnippet>114</a>            <span class="kw">let </span>is_complete = chunk_index == total_chunks - <span class="number">1</span>;
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>            chunks.push(FileData {
<a href=#117 id=117 data-nosnippet>117</a>                file_path: file_path.to_string(),
<a href=#118 id=118 data-nosnippet>118</a>                chunk_index,
<a href=#119 id=119 data-nosnippet>119</a>                total_chunks,
<a href=#120 id=120 data-nosnippet>120</a>                data: buffer,
<a href=#121 id=121 data-nosnippet>121</a>                is_complete,
<a href=#122 id=122 data-nosnippet>122</a>            });
<a href=#123 id=123 data-nosnippet>123</a>        }
<a href=#124 id=124 data-nosnippet>124</a>
<a href=#125 id=125 data-nosnippet>125</a>        <span class="prelude-val">Ok</span>(chunks)
<a href=#126 id=126 data-nosnippet>126</a>    }
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>    <span class="kw">pub async fn </span>delete_file(<span class="kw-2">&amp;</span><span class="self">self</span>, file_path: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#129 id=129 data-nosnippet>129</a>        <span class="kw">let </span>path = Path::new(file_path);
<a href=#130 id=130 data-nosnippet>130</a>        <span class="kw">if </span>path.exists() {
<a href=#131 id=131 data-nosnippet>131</a>            <span class="kw">if </span>path.is_file() {
<a href=#132 id=132 data-nosnippet>132</a>                tokio::fs::remove_file(path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#133 id=133 data-nosnippet>133</a>            } <span class="kw">else if </span>path.is_dir() {
<a href=#134 id=134 data-nosnippet>134</a>                tokio::fs::remove_dir_all(path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#135 id=135 data-nosnippet>135</a>            }
<a href=#136 id=136 data-nosnippet>136</a>            <span class="macro">log::info!</span>(<span class="string">"File deleted: {}"</span>, file_path);
<a href=#137 id=137 data-nosnippet>137</a>        }
<a href=#138 id=138 data-nosnippet>138</a>        <span class="prelude-val">Ok</span>(())
<a href=#139 id=139 data-nosnippet>139</a>    }
<a href=#140 id=140 data-nosnippet>140</a>
<a href=#141 id=141 data-nosnippet>141</a>    <span class="kw">pub async fn </span>create_directory(<span class="kw-2">&amp;</span><span class="self">self</span>, dir_path: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#142 id=142 data-nosnippet>142</a>        tokio::fs::create_dir_all(dir_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#143 id=143 data-nosnippet>143</a>        <span class="macro">log::info!</span>(<span class="string">"Directory created: {}"</span>, dir_path);
<a href=#144 id=144 data-nosnippet>144</a>        <span class="prelude-val">Ok</span>(())
<a href=#145 id=145 data-nosnippet>145</a>    }
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>    <span class="kw">pub async fn </span>list_directory(<span class="kw-2">&amp;</span><span class="self">self</span>, dir_path: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;FileInfo&gt;&gt; {
<a href=#148 id=148 data-nosnippet>148</a>        <span class="kw">let </span><span class="kw-2">mut </span>entries = tokio::fs::read_dir(dir_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#149 id=149 data-nosnippet>149</a>        <span class="kw">let </span><span class="kw-2">mut </span>files = Vec::new();
<a href=#150 id=150 data-nosnippet>150</a>
<a href=#151 id=151 data-nosnippet>151</a>        <span class="kw">while let </span><span class="prelude-val">Some</span>(entry) = entries.next_entry().<span class="kw">await</span><span class="question-mark">? </span>{
<a href=#152 id=152 data-nosnippet>152</a>            <span class="kw">let </span>metadata = entry.metadata().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#153 id=153 data-nosnippet>153</a>            <span class="kw">let </span>file_type = <span class="kw">if </span>metadata.is_file() {
<a href=#154 id=154 data-nosnippet>154</a>                FileType::File
<a href=#155 id=155 data-nosnippet>155</a>            } <span class="kw">else if </span>metadata.is_dir() {
<a href=#156 id=156 data-nosnippet>156</a>                FileType::Directory
<a href=#157 id=157 data-nosnippet>157</a>            } <span class="kw">else </span>{
<a href=#158 id=158 data-nosnippet>158</a>                FileType::Other
<a href=#159 id=159 data-nosnippet>159</a>            };
<a href=#160 id=160 data-nosnippet>160</a>
<a href=#161 id=161 data-nosnippet>161</a>            files.push(FileInfo {
<a href=#162 id=162 data-nosnippet>162</a>                name: entry.file_name().to_string_lossy().to_string(),
<a href=#163 id=163 data-nosnippet>163</a>                path: entry.path().to_string_lossy().to_string(),
<a href=#164 id=164 data-nosnippet>164</a>                size: metadata.len(),
<a href=#165 id=165 data-nosnippet>165</a>                file_type,
<a href=#166 id=166 data-nosnippet>166</a>                modified: metadata
<a href=#167 id=167 data-nosnippet>167</a>                    .modified()
<a href=#168 id=168 data-nosnippet>168</a>                    .ok()
<a href=#169 id=169 data-nosnippet>169</a>                    .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
<a href=#170 id=170 data-nosnippet>170</a>                    .map(|d| d.as_secs()),
<a href=#171 id=171 data-nosnippet>171</a>                permissions: <span class="self">self</span>.get_permissions(<span class="kw-2">&amp;</span>metadata),
<a href=#172 id=172 data-nosnippet>172</a>            });
<a href=#173 id=173 data-nosnippet>173</a>        }
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a>        <span class="prelude-val">Ok</span>(files)
<a href=#176 id=176 data-nosnippet>176</a>    }
<a href=#177 id=177 data-nosnippet>177</a>
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">fn </span>get_permissions(<span class="kw-2">&amp;</span><span class="self">self</span>, metadata: <span class="kw-2">&amp;</span>std::fs::Metadata) -&gt; String {
<a href=#179 id=179 data-nosnippet>179</a>        <span class="attr">#[cfg(unix)]
<a href=#180 id=180 data-nosnippet>180</a>        </span>{
<a href=#181 id=181 data-nosnippet>181</a>            <span class="kw">use </span>std::os::unix::fs::PermissionsExt;
<a href=#182 id=182 data-nosnippet>182</a>            <span class="kw">let </span>mode = metadata.permissions().mode();
<a href=#183 id=183 data-nosnippet>183</a>            <span class="macro">format!</span>(<span class="string">"{:o}"</span>, mode &amp; <span class="number">0o777</span>)
<a href=#184 id=184 data-nosnippet>184</a>        }
<a href=#185 id=185 data-nosnippet>185</a>        <span class="attr">#[cfg(not(unix))]
<a href=#186 id=186 data-nosnippet>186</a>        </span>{
<a href=#187 id=187 data-nosnippet>187</a>            <span class="string">"unknown"</span>.to_string()
<a href=#188 id=188 data-nosnippet>188</a>        }
<a href=#189 id=189 data-nosnippet>189</a>    }
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a>    <span class="kw">pub async fn </span>get_file_info(<span class="kw-2">&amp;</span><span class="self">self</span>, file_path: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;FileInfo&gt; {
<a href=#192 id=192 data-nosnippet>192</a>        <span class="kw">let </span>path = Path::new(file_path);
<a href=#193 id=193 data-nosnippet>193</a>        <span class="kw">let </span>metadata = tokio::fs::metadata(path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#194 id=194 data-nosnippet>194</a>
<a href=#195 id=195 data-nosnippet>195</a>        <span class="kw">let </span>file_type = <span class="kw">if </span>metadata.is_file() {
<a href=#196 id=196 data-nosnippet>196</a>            FileType::File
<a href=#197 id=197 data-nosnippet>197</a>        } <span class="kw">else if </span>metadata.is_dir() {
<a href=#198 id=198 data-nosnippet>198</a>            FileType::Directory
<a href=#199 id=199 data-nosnippet>199</a>        } <span class="kw">else </span>{
<a href=#200 id=200 data-nosnippet>200</a>            FileType::Other
<a href=#201 id=201 data-nosnippet>201</a>        };
<a href=#202 id=202 data-nosnippet>202</a>
<a href=#203 id=203 data-nosnippet>203</a>        <span class="prelude-val">Ok</span>(FileInfo {
<a href=#204 id=204 data-nosnippet>204</a>            name: path
<a href=#205 id=205 data-nosnippet>205</a>                .file_name()
<a href=#206 id=206 data-nosnippet>206</a>                .unwrap_or_default()
<a href=#207 id=207 data-nosnippet>207</a>                .to_string_lossy()
<a href=#208 id=208 data-nosnippet>208</a>                .to_string(),
<a href=#209 id=209 data-nosnippet>209</a>            path: file_path.to_string(),
<a href=#210 id=210 data-nosnippet>210</a>            size: metadata.len(),
<a href=#211 id=211 data-nosnippet>211</a>            file_type,
<a href=#212 id=212 data-nosnippet>212</a>            modified: metadata
<a href=#213 id=213 data-nosnippet>213</a>                .modified()
<a href=#214 id=214 data-nosnippet>214</a>                .ok()
<a href=#215 id=215 data-nosnippet>215</a>                .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
<a href=#216 id=216 data-nosnippet>216</a>                .map(|d| d.as_secs()),
<a href=#217 id=217 data-nosnippet>217</a>            permissions: <span class="self">self</span>.get_permissions(<span class="kw-2">&amp;</span>metadata),
<a href=#218 id=218 data-nosnippet>218</a>        })
<a href=#219 id=219 data-nosnippet>219</a>    }
<a href=#220 id=220 data-nosnippet>220</a>
<a href=#221 id=221 data-nosnippet>221</a>    <span class="kw">pub async fn </span>copy_file(<span class="kw-2">&amp;</span><span class="self">self</span>, source: <span class="kw-2">&amp;</span>str, destination: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#222 id=222 data-nosnippet>222</a>        <span class="comment">// 确保目标目录存在
<a href=#223 id=223 data-nosnippet>223</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = Path::new(destination).parent() {
<a href=#224 id=224 data-nosnippet>224</a>            tokio::fs::create_dir_all(parent).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#225 id=225 data-nosnippet>225</a>        }
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>        tokio::fs::copy(source, destination).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#228 id=228 data-nosnippet>228</a>        <span class="macro">log::info!</span>(<span class="string">"File copied: {} -&gt; {}"</span>, source, destination);
<a href=#229 id=229 data-nosnippet>229</a>        <span class="prelude-val">Ok</span>(())
<a href=#230 id=230 data-nosnippet>230</a>    }
<a href=#231 id=231 data-nosnippet>231</a>
<a href=#232 id=232 data-nosnippet>232</a>    <span class="kw">pub async fn </span>move_file(<span class="kw-2">&amp;</span><span class="self">self</span>, source: <span class="kw-2">&amp;</span>str, destination: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#233 id=233 data-nosnippet>233</a>        <span class="comment">// 确保目标目录存在
<a href=#234 id=234 data-nosnippet>234</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = Path::new(destination).parent() {
<a href=#235 id=235 data-nosnippet>235</a>            tokio::fs::create_dir_all(parent).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#236 id=236 data-nosnippet>236</a>        }
<a href=#237 id=237 data-nosnippet>237</a>
<a href=#238 id=238 data-nosnippet>238</a>        tokio::fs::rename(source, destination).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#239 id=239 data-nosnippet>239</a>        <span class="macro">log::info!</span>(<span class="string">"File moved: {} -&gt; {}"</span>, source, destination);
<a href=#240 id=240 data-nosnippet>240</a>        <span class="prelude-val">Ok</span>(())
<a href=#241 id=241 data-nosnippet>241</a>    }
<a href=#242 id=242 data-nosnippet>242</a>
<a href=#243 id=243 data-nosnippet>243</a>    <span class="kw">pub async fn </span>read_file_content(
<a href=#244 id=244 data-nosnippet>244</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#245 id=245 data-nosnippet>245</a>        file_path: <span class="kw-2">&amp;</span>str,
<a href=#246 id=246 data-nosnippet>246</a>        max_size: <span class="prelude-ty">Option</span>&lt;usize&gt;,
<a href=#247 id=247 data-nosnippet>247</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;u8&gt;&gt; {
<a href=#248 id=248 data-nosnippet>248</a>        <span class="kw">let </span><span class="kw-2">mut </span>file = File::open(file_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#249 id=249 data-nosnippet>249</a>        <span class="kw">let </span>file_size = file.metadata().<span class="kw">await</span><span class="question-mark">?</span>.len() <span class="kw">as </span>usize;
<a href=#250 id=250 data-nosnippet>250</a>
<a href=#251 id=251 data-nosnippet>251</a>        <span class="kw">let </span>read_size = max_size.unwrap_or(file_size).min(file_size);
<a href=#252 id=252 data-nosnippet>252</a>        <span class="kw">let </span><span class="kw-2">mut </span>buffer = <span class="macro">vec!</span>[<span class="number">0u8</span>; read_size];
<a href=#253 id=253 data-nosnippet>253</a>
<a href=#254 id=254 data-nosnippet>254</a>        file.read_exact(<span class="kw-2">&amp;mut </span>buffer).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#255 id=255 data-nosnippet>255</a>        <span class="prelude-val">Ok</span>(buffer)
<a href=#256 id=256 data-nosnippet>256</a>    }
<a href=#257 id=257 data-nosnippet>257</a>
<a href=#258 id=258 data-nosnippet>258</a>    <span class="kw">pub async fn </span>write_file_content(<span class="kw-2">&amp;</span><span class="self">self</span>, file_path: <span class="kw-2">&amp;</span>str, content: <span class="kw-2">&amp;</span>[u8]) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#259 id=259 data-nosnippet>259</a>        <span class="comment">// 确保目标目录存在
<a href=#260 id=260 data-nosnippet>260</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = Path::new(file_path).parent() {
<a href=#261 id=261 data-nosnippet>261</a>            tokio::fs::create_dir_all(parent).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#262 id=262 data-nosnippet>262</a>        }
<a href=#263 id=263 data-nosnippet>263</a>
<a href=#264 id=264 data-nosnippet>264</a>        <span class="kw">let </span><span class="kw-2">mut </span>file = File::create(file_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#265 id=265 data-nosnippet>265</a>        file.write_all(content).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#266 id=266 data-nosnippet>266</a>        file.flush().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#267 id=267 data-nosnippet>267</a>
<a href=#268 id=268 data-nosnippet>268</a>        <span class="macro">log::info!</span>(<span class="string">"File written: {} ({} bytes)"</span>, file_path, content.len());
<a href=#269 id=269 data-nosnippet>269</a>        <span class="prelude-val">Ok</span>(())
<a href=#270 id=270 data-nosnippet>270</a>    }
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a>    <span class="kw">pub async fn </span>cleanup_temp_files(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#273 id=273 data-nosnippet>273</a>        <span class="kw">for </span>(<span class="kw">_</span>, temp_info) <span class="kw">in </span><span class="self">self</span>.temp_files.drain() {
<a href=#274 id=274 data-nosnippet>274</a>            <span class="kw">if </span>temp_info.path.exists() {
<a href=#275 id=275 data-nosnippet>275</a>                <span class="kw">let _ </span>= tokio::fs::remove_file(<span class="kw-2">&amp;</span>temp_info.path).<span class="kw">await</span>;
<a href=#276 id=276 data-nosnippet>276</a>            }
<a href=#277 id=277 data-nosnippet>277</a>        }
<a href=#278 id=278 data-nosnippet>278</a>        <span class="prelude-val">Ok</span>(())
<a href=#279 id=279 data-nosnippet>279</a>    }
<a href=#280 id=280 data-nosnippet>280</a>}
<a href=#281 id=281 data-nosnippet>281</a>
<a href=#282 id=282 data-nosnippet>282</a><span class="attr">#[derive(Debug, Clone)]
<a href=#283 id=283 data-nosnippet>283</a></span><span class="kw">pub struct </span>FileInfo {
<a href=#284 id=284 data-nosnippet>284</a>    <span class="kw">pub </span>name: String,
<a href=#285 id=285 data-nosnippet>285</a>    <span class="kw">pub </span>path: String,
<a href=#286 id=286 data-nosnippet>286</a>    <span class="kw">pub </span>size: u64,
<a href=#287 id=287 data-nosnippet>287</a>    <span class="kw">pub </span>file_type: FileType,
<a href=#288 id=288 data-nosnippet>288</a>    <span class="kw">pub </span>modified: <span class="prelude-ty">Option</span>&lt;u64&gt;,
<a href=#289 id=289 data-nosnippet>289</a>    <span class="kw">pub </span>permissions: String,
<a href=#290 id=290 data-nosnippet>290</a>}
<a href=#291 id=291 data-nosnippet>291</a>
<a href=#292 id=292 data-nosnippet>292</a><span class="attr">#[derive(Debug, Clone)]
<a href=#293 id=293 data-nosnippet>293</a></span><span class="kw">pub enum </span>FileType {
<a href=#294 id=294 data-nosnippet>294</a>    File,
<a href=#295 id=295 data-nosnippet>295</a>    Directory,
<a href=#296 id=296 data-nosnippet>296</a>    Other,
<a href=#297 id=297 data-nosnippet>297</a>}
<a href=#298 id=298 data-nosnippet>298</a>
<a href=#299 id=299 data-nosnippet>299</a><span class="attr">#[cfg(test)]
<a href=#300 id=300 data-nosnippet>300</a></span><span class="kw">mod </span>tests {
<a href=#301 id=301 data-nosnippet>301</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#302 id=302 data-nosnippet>302</a>    <span class="kw">use </span>tempfile::TempDir;
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>    <span class="attr">#[tokio::test]
<a href=#305 id=305 data-nosnippet>305</a>    </span><span class="kw">async fn </span>test_file_operations() {
<a href=#306 id=306 data-nosnippet>306</a>        <span class="kw">let </span>temp_dir = TempDir::new().unwrap();
<a href=#307 id=307 data-nosnippet>307</a>        <span class="kw">let </span>file_manager = FileManager::new();
<a href=#308 id=308 data-nosnippet>308</a>
<a href=#309 id=309 data-nosnippet>309</a>        <span class="kw">let </span>test_file = temp_dir.path().join(<span class="string">"test.txt"</span>);
<a href=#310 id=310 data-nosnippet>310</a>        <span class="kw">let </span>content = <span class="string">b"Hello, World!"</span>;
<a href=#311 id=311 data-nosnippet>311</a>
<a href=#312 id=312 data-nosnippet>312</a>        <span class="comment">// 写入文件
<a href=#313 id=313 data-nosnippet>313</a>        </span>file_manager
<a href=#314 id=314 data-nosnippet>314</a>            .write_file_content(test_file.to_str().unwrap(), content)
<a href=#315 id=315 data-nosnippet>315</a>            .<span class="kw">await
<a href=#316 id=316 data-nosnippet>316</a>            </span>.unwrap();
<a href=#317 id=317 data-nosnippet>317</a>
<a href=#318 id=318 data-nosnippet>318</a>        <span class="comment">// 读取文件
<a href=#319 id=319 data-nosnippet>319</a>        </span><span class="kw">let </span>read_content = file_manager
<a href=#320 id=320 data-nosnippet>320</a>            .read_file_content(test_file.to_str().unwrap(), <span class="prelude-val">None</span>)
<a href=#321 id=321 data-nosnippet>321</a>            .<span class="kw">await
<a href=#322 id=322 data-nosnippet>322</a>            </span>.unwrap();
<a href=#323 id=323 data-nosnippet>323</a>
<a href=#324 id=324 data-nosnippet>324</a>        <span class="macro">assert_eq!</span>(content, read_content.as_slice());
<a href=#325 id=325 data-nosnippet>325</a>
<a href=#326 id=326 data-nosnippet>326</a>        <span class="comment">// 获取文件信息
<a href=#327 id=327 data-nosnippet>327</a>        </span><span class="kw">let </span>file_info = file_manager
<a href=#328 id=328 data-nosnippet>328</a>            .get_file_info(test_file.to_str().unwrap())
<a href=#329 id=329 data-nosnippet>329</a>            .<span class="kw">await
<a href=#330 id=330 data-nosnippet>330</a>            </span>.unwrap();
<a href=#331 id=331 data-nosnippet>331</a>
<a href=#332 id=332 data-nosnippet>332</a>        <span class="macro">assert_eq!</span>(file_info.size, content.len() <span class="kw">as </span>u64);
<a href=#333 id=333 data-nosnippet>333</a>        <span class="macro">assert!</span>(<span class="macro">matches!</span>(file_info.file_type, FileType::File));
<a href=#334 id=334 data-nosnippet>334</a>    }
<a href=#335 id=335 data-nosnippet>335</a>
<a href=#336 id=336 data-nosnippet>336</a>    <span class="attr">#[tokio::test]
<a href=#337 id=337 data-nosnippet>337</a>    </span><span class="kw">async fn </span>test_directory_operations() {
<a href=#338 id=338 data-nosnippet>338</a>        <span class="kw">let </span>temp_dir = TempDir::new().unwrap();
<a href=#339 id=339 data-nosnippet>339</a>        <span class="kw">let </span>file_manager = FileManager::new();
<a href=#340 id=340 data-nosnippet>340</a>
<a href=#341 id=341 data-nosnippet>341</a>        <span class="kw">let </span>test_dir = temp_dir.path().join(<span class="string">"test_dir"</span>);
<a href=#342 id=342 data-nosnippet>342</a>
<a href=#343 id=343 data-nosnippet>343</a>        <span class="comment">// 创建目录
<a href=#344 id=344 data-nosnippet>344</a>        </span>file_manager
<a href=#345 id=345 data-nosnippet>345</a>            .create_directory(test_dir.to_str().unwrap())
<a href=#346 id=346 data-nosnippet>346</a>            .<span class="kw">await
<a href=#347 id=347 data-nosnippet>347</a>            </span>.unwrap();
<a href=#348 id=348 data-nosnippet>348</a>
<a href=#349 id=349 data-nosnippet>349</a>        <span class="macro">assert!</span>(test_dir.exists());
<a href=#350 id=350 data-nosnippet>350</a>        <span class="macro">assert!</span>(test_dir.is_dir());
<a href=#351 id=351 data-nosnippet>351</a>
<a href=#352 id=352 data-nosnippet>352</a>        <span class="comment">// 列出目录内容
<a href=#353 id=353 data-nosnippet>353</a>        </span><span class="kw">let </span>entries = file_manager
<a href=#354 id=354 data-nosnippet>354</a>            .list_directory(temp_dir.path().to_str().unwrap())
<a href=#355 id=355 data-nosnippet>355</a>            .<span class="kw">await
<a href=#356 id=356 data-nosnippet>356</a>            </span>.unwrap();
<a href=#357 id=357 data-nosnippet>357</a>
<a href=#358 id=358 data-nosnippet>358</a>        <span class="macro">assert_eq!</span>(entries.len(), <span class="number">1</span>);
<a href=#359 id=359 data-nosnippet>359</a>        <span class="macro">assert_eq!</span>(entries[<span class="number">0</span>].name, <span class="string">"test_dir"</span>);
<a href=#360 id=360 data-nosnippet>360</a>    }
<a href=#361 id=361 data-nosnippet>361</a>}</code></pre></div></section></main></body></html>