<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/main.rs`."><title>main.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>main.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">mod </span>agent;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">mod </span>elf_loader;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">mod </span>executor;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">mod </span>file_manager;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">mod </span>system_info;
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="attr">#[cfg(test)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">mod </span>tests;
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">use </span>agent::Agent;
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">use </span>anyhow::Result;
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">use </span>clap::{Parser, Subcommand};
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">use </span>common::{CryptoManager, Logger};
<a href=#14 id=14 data-nosnippet>14</a><span class="kw">use </span>std::path::PathBuf;
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a><span class="attr">#[derive(Parser)]
<a href=#17 id=17 data-nosnippet>17</a>#[command(name = <span class="string">"agent"</span>)]
<a href=#18 id=18 data-nosnippet>18</a>#[command(about = <span class="string">"Ubuntu Remote Control - Agent"</span>)]
<a href=#19 id=19 data-nosnippet>19</a></span><span class="kw">struct </span>Cli {
<a href=#20 id=20 data-nosnippet>20</a>    <span class="attr">#[command(subcommand)]
<a href=#21 id=21 data-nosnippet>21</a>    </span>command: <span class="prelude-ty">Option</span>&lt;Commands&gt;,
<a href=#22 id=22 data-nosnippet>22</a>
<a href=#23 id=23 data-nosnippet>23</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"agent.log"</span>)]
<a href=#24 id=24 data-nosnippet>24</a>    </span>log_file: PathBuf,
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"http://localhost:8080"</span>)]
<a href=#27 id=27 data-nosnippet>27</a>    </span>server_url: String,
<a href=#28 id=28 data-nosnippet>28</a>
<a href=#29 id=29 data-nosnippet>29</a>    <span class="attr">#[arg(short, long)]
<a href=#30 id=30 data-nosnippet>30</a>    </span>crypto_key: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"30"</span>)]
<a href=#33 id=33 data-nosnippet>33</a>    </span>heartbeat_interval: u64,
<a href=#34 id=34 data-nosnippet>34</a>
<a href=#35 id=35 data-nosnippet>35</a>    <span class="attr">#[arg(short, long)]
<a href=#36 id=36 data-nosnippet>36</a>    </span>daemon: bool,
<a href=#37 id=37 data-nosnippet>37</a>}
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a><span class="attr">#[derive(Subcommand, Clone)]
<a href=#40 id=40 data-nosnippet>40</a></span><span class="kw">enum </span>Commands {
<a href=#41 id=41 data-nosnippet>41</a>    <span class="doccomment">/// Start agent in foreground
<a href=#42 id=42 data-nosnippet>42</a>    </span>Start,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="doccomment">/// Install agent as system service
<a href=#44 id=44 data-nosnippet>44</a>    </span>Install,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="doccomment">/// Uninstall agent service
<a href=#46 id=46 data-nosnippet>46</a>    </span>Uninstall,
<a href=#47 id=47 data-nosnippet>47</a>    <span class="doccomment">/// Check agent status
<a href=#48 id=48 data-nosnippet>48</a>    </span>Status,
<a href=#49 id=49 data-nosnippet>49</a>}
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a><span class="attr">#[tokio::main]
<a href=#52 id=52 data-nosnippet>52</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#53 id=53 data-nosnippet>53</a>    <span class="kw">let </span>cli = Cli::parse();
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a>    <span class="comment">// 初始化日志
<a href=#56 id=56 data-nosnippet>56</a>    </span>env_logger::init();
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">let </span>logger = Logger::new(cli.log_file.to_string_lossy().to_string());
<a href=#58 id=58 data-nosnippet>58</a>    logger.ensure_log_directory()<span class="question-mark">?</span>;
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a>    <span class="comment">// 初始化加密密钥
<a href=#61 id=61 data-nosnippet>61</a>    </span><span class="kw">let </span>crypto_key = <span class="kw">if let </span><span class="prelude-val">Some</span>(<span class="kw-2">ref </span>key) = cli.crypto_key {
<a href=#62 id=62 data-nosnippet>62</a>        key.clone().into_bytes()
<a href=#63 id=63 data-nosnippet>63</a>    } <span class="kw">else </span>{
<a href=#64 id=64 data-nosnippet>64</a>        <span class="comment">// 在生产环境中，这应该从安全的地方获取
<a href=#65 id=65 data-nosnippet>65</a>        </span>CryptoManager::generate_key()
<a href=#66 id=66 data-nosnippet>66</a>    };
<a href=#67 id=67 data-nosnippet>67</a>
<a href=#68 id=68 data-nosnippet>68</a>    <span class="comment">// 如果以daemon模式运行，则fork到后台
<a href=#69 id=69 data-nosnippet>69</a>    </span><span class="kw">if </span>cli.daemon {
<a href=#70 id=70 data-nosnippet>70</a>        daemonize()<span class="question-mark">?</span>;
<a href=#71 id=71 data-nosnippet>71</a>    }
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">match </span>cli.command.clone().unwrap_or(Commands::Start) {
<a href=#74 id=74 data-nosnippet>74</a>        Commands::Start =&gt; {
<a href=#75 id=75 data-nosnippet>75</a>            <span class="macro">println!</span>(<span class="string">"Starting agent..."</span>);
<a href=#76 id=76 data-nosnippet>76</a>            <span class="kw">let </span><span class="kw-2">mut </span>agent =
<a href=#77 id=77 data-nosnippet>77</a>                Agent::new(cli.server_url, <span class="kw-2">&amp;</span>crypto_key, logger, cli.heartbeat_interval).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#78 id=78 data-nosnippet>78</a>            agent.start().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#79 id=79 data-nosnippet>79</a>        }
<a href=#80 id=80 data-nosnippet>80</a>        Commands::Install =&gt; {
<a href=#81 id=81 data-nosnippet>81</a>            <span class="macro">println!</span>(<span class="string">"Installing agent as system service..."</span>);
<a href=#82 id=82 data-nosnippet>82</a>            install_service(<span class="kw-2">&amp;</span>cli)<span class="question-mark">?</span>;
<a href=#83 id=83 data-nosnippet>83</a>        }
<a href=#84 id=84 data-nosnippet>84</a>        Commands::Uninstall =&gt; {
<a href=#85 id=85 data-nosnippet>85</a>            <span class="macro">println!</span>(<span class="string">"Uninstalling agent service..."</span>);
<a href=#86 id=86 data-nosnippet>86</a>            uninstall_service()<span class="question-mark">?</span>;
<a href=#87 id=87 data-nosnippet>87</a>        }
<a href=#88 id=88 data-nosnippet>88</a>        Commands::Status =&gt; {
<a href=#89 id=89 data-nosnippet>89</a>            <span class="macro">println!</span>(<span class="string">"Checking agent status..."</span>);
<a href=#90 id=90 data-nosnippet>90</a>            check_status()<span class="question-mark">?</span>;
<a href=#91 id=91 data-nosnippet>91</a>        }
<a href=#92 id=92 data-nosnippet>92</a>    }
<a href=#93 id=93 data-nosnippet>93</a>
<a href=#94 id=94 data-nosnippet>94</a>    <span class="prelude-val">Ok</span>(())
<a href=#95 id=95 data-nosnippet>95</a>}
<a href=#96 id=96 data-nosnippet>96</a>
<a href=#97 id=97 data-nosnippet>97</a><span class="kw">fn </span>daemonize() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#98 id=98 data-nosnippet>98</a>    <span class="kw">use </span>std::process;
<a href=#99 id=99 data-nosnippet>99</a>
<a href=#100 id=100 data-nosnippet>100</a>    <span class="comment">// 简单的daemon化实现
<a href=#101 id=101 data-nosnippet>101</a>    </span><span class="kw">match unsafe </span>{ libc::fork() } {
<a href=#102 id=102 data-nosnippet>102</a>        -<span class="number">1 </span>=&gt; <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow::anyhow!</span>(<span class="string">"Fork failed"</span>)),
<a href=#103 id=103 data-nosnippet>103</a>        <span class="number">0 </span>=&gt; {
<a href=#104 id=104 data-nosnippet>104</a>            <span class="comment">// 子进程
<a href=#105 id=105 data-nosnippet>105</a>            </span><span class="kw">unsafe </span>{
<a href=#106 id=106 data-nosnippet>106</a>                libc::setsid();
<a href=#107 id=107 data-nosnippet>107</a>            }
<a href=#108 id=108 data-nosnippet>108</a>        }
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">_ </span>=&gt; {
<a href=#110 id=110 data-nosnippet>110</a>            <span class="comment">// 父进程退出
<a href=#111 id=111 data-nosnippet>111</a>            </span>process::exit(<span class="number">0</span>);
<a href=#112 id=112 data-nosnippet>112</a>        }
<a href=#113 id=113 data-nosnippet>113</a>    }
<a href=#114 id=114 data-nosnippet>114</a>
<a href=#115 id=115 data-nosnippet>115</a>    <span class="prelude-val">Ok</span>(())
<a href=#116 id=116 data-nosnippet>116</a>}
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a><span class="kw">fn </span>install_service(cli: <span class="kw-2">&amp;</span>Cli) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">let </span>current_exe = std::env::current_exe()<span class="question-mark">?</span>;
<a href=#120 id=120 data-nosnippet>120</a>    <span class="kw">let </span>service_content = <span class="macro">format!</span>(
<a href=#121 id=121 data-nosnippet>121</a>        <span class="string">r#"[Unit]
<a href=#122 id=122 data-nosnippet>122</a>Description=Ubuntu Remote Control Agent
<a href=#123 id=123 data-nosnippet>123</a>After=network.target
<a href=#124 id=124 data-nosnippet>124</a>
<a href=#125 id=125 data-nosnippet>125</a>[Service]
<a href=#126 id=126 data-nosnippet>126</a>Type=simple
<a href=#127 id=127 data-nosnippet>127</a>User=root
<a href=#128 id=128 data-nosnippet>128</a>ExecStart={} --daemon --server-url {} --log-file {}
<a href=#129 id=129 data-nosnippet>129</a>Restart=always
<a href=#130 id=130 data-nosnippet>130</a>RestartSec=5
<a href=#131 id=131 data-nosnippet>131</a>StandardOutput=journal
<a href=#132 id=132 data-nosnippet>132</a>StandardError=journal
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a>[Install]
<a href=#135 id=135 data-nosnippet>135</a>WantedBy=multi-user.target"#</span>,
<a href=#136 id=136 data-nosnippet>136</a>        current_exe.display(),
<a href=#137 id=137 data-nosnippet>137</a>        cli.server_url,
<a href=#138 id=138 data-nosnippet>138</a>        cli.log_file.display()
<a href=#139 id=139 data-nosnippet>139</a>    );
<a href=#140 id=140 data-nosnippet>140</a>
<a href=#141 id=141 data-nosnippet>141</a>    std::fs::write(<span class="string">"/etc/systemd/system/remote-agent.service"</span>, service_content)<span class="question-mark">?</span>;
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>    <span class="comment">// 启用并启动服务
<a href=#144 id=144 data-nosnippet>144</a>    </span>std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#145 id=145 data-nosnippet>145</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"daemon-reload"</span>])
<a href=#146 id=146 data-nosnippet>146</a>        .status()<span class="question-mark">?</span>;
<a href=#147 id=147 data-nosnippet>147</a>
<a href=#148 id=148 data-nosnippet>148</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#149 id=149 data-nosnippet>149</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"enable"</span>, <span class="string">"remote-agent"</span>])
<a href=#150 id=150 data-nosnippet>150</a>        .status()<span class="question-mark">?</span>;
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#153 id=153 data-nosnippet>153</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"start"</span>, <span class="string">"remote-agent"</span>])
<a href=#154 id=154 data-nosnippet>154</a>        .status()<span class="question-mark">?</span>;
<a href=#155 id=155 data-nosnippet>155</a>
<a href=#156 id=156 data-nosnippet>156</a>    <span class="macro">println!</span>(<span class="string">"Agent service installed and started successfully"</span>);
<a href=#157 id=157 data-nosnippet>157</a>    <span class="prelude-val">Ok</span>(())
<a href=#158 id=158 data-nosnippet>158</a>}
<a href=#159 id=159 data-nosnippet>159</a>
<a href=#160 id=160 data-nosnippet>160</a><span class="kw">fn </span>uninstall_service() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#161 id=161 data-nosnippet>161</a>    <span class="comment">// 停止并禁用服务
<a href=#162 id=162 data-nosnippet>162</a>    </span>std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#163 id=163 data-nosnippet>163</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"stop"</span>, <span class="string">"remote-agent"</span>])
<a href=#164 id=164 data-nosnippet>164</a>        .status()<span class="question-mark">?</span>;
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#167 id=167 data-nosnippet>167</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"disable"</span>, <span class="string">"remote-agent"</span>])
<a href=#168 id=168 data-nosnippet>168</a>        .status()<span class="question-mark">?</span>;
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>    <span class="comment">// 删除服务文件
<a href=#171 id=171 data-nosnippet>171</a>    </span><span class="kw">if </span>std::path::Path::new(<span class="string">"/etc/systemd/system/remote-agent.service"</span>).exists() {
<a href=#172 id=172 data-nosnippet>172</a>        std::fs::remove_file(<span class="string">"/etc/systemd/system/remote-agent.service"</span>)<span class="question-mark">?</span>;
<a href=#173 id=173 data-nosnippet>173</a>    }
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#176 id=176 data-nosnippet>176</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"daemon-reload"</span>])
<a href=#177 id=177 data-nosnippet>177</a>        .status()<span class="question-mark">?</span>;
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a>    <span class="macro">println!</span>(<span class="string">"Agent service uninstalled successfully"</span>);
<a href=#180 id=180 data-nosnippet>180</a>    <span class="prelude-val">Ok</span>(())
<a href=#181 id=181 data-nosnippet>181</a>}
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a><span class="kw">fn </span>check_status() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#184 id=184 data-nosnippet>184</a>    <span class="kw">let </span>output = std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#185 id=185 data-nosnippet>185</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"status"</span>, <span class="string">"remote-agent"</span>])
<a href=#186 id=186 data-nosnippet>186</a>        .output()<span class="question-mark">?</span>;
<a href=#187 id=187 data-nosnippet>187</a>
<a href=#188 id=188 data-nosnippet>188</a>    <span class="macro">println!</span>(<span class="string">"{}"</span>, String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout));
<a href=#189 id=189 data-nosnippet>189</a>
<a href=#190 id=190 data-nosnippet>190</a>    <span class="kw">if </span>!output.stderr.is_empty() {
<a href=#191 id=191 data-nosnippet>191</a>        <span class="macro">eprintln!</span>(<span class="string">"{}"</span>, String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stderr));
<a href=#192 id=192 data-nosnippet>192</a>    }
<a href=#193 id=193 data-nosnippet>193</a>
<a href=#194 id=194 data-nosnippet>194</a>    <span class="prelude-val">Ok</span>(())
<a href=#195 id=195 data-nosnippet>195</a>}</code></pre></div></section></main></body></html>