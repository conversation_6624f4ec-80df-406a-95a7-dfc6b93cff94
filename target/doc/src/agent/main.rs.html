<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/main.rs`."><title>main.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>main.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">mod </span>agent;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">mod </span>elf_loader;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">mod </span>executor;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">mod </span>file_manager;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">mod </span>system_info;
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="attr">#[cfg(test)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">mod </span>tests;
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">use </span>agent::Agent;
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">use </span>anyhow::Result;
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">use </span>clap::{Parser, Subcommand};
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">use </span>common::{CryptoManager, Logger};
<a href=#14 id=14 data-nosnippet>14</a><span class="kw">use </span>std::path::PathBuf;
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a><span class="attr">#[derive(Parser)]
<a href=#17 id=17 data-nosnippet>17</a>#[command(name = <span class="string">"agent"</span>)]
<a href=#18 id=18 data-nosnippet>18</a>#[command(about = <span class="string">"Ubuntu Remote Control - Agent"</span>)]
<a href=#19 id=19 data-nosnippet>19</a></span><span class="kw">struct </span>Cli {
<a href=#20 id=20 data-nosnippet>20</a>    <span class="attr">#[command(subcommand)]
<a href=#21 id=21 data-nosnippet>21</a>    </span>command: <span class="prelude-ty">Option</span>&lt;Commands&gt;,
<a href=#22 id=22 data-nosnippet>22</a>
<a href=#23 id=23 data-nosnippet>23</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"agent.log"</span>)]
<a href=#24 id=24 data-nosnippet>24</a>    </span>log_file: PathBuf,
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"http://localhost:8080"</span>)]
<a href=#27 id=27 data-nosnippet>27</a>    </span>server_url: String,
<a href=#28 id=28 data-nosnippet>28</a>
<a href=#29 id=29 data-nosnippet>29</a>    <span class="attr">#[arg(short, long)]
<a href=#30 id=30 data-nosnippet>30</a>    </span>crypto_key: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"30"</span>)]
<a href=#33 id=33 data-nosnippet>33</a>    </span>heartbeat_interval: u64,
<a href=#34 id=34 data-nosnippet>34</a>
<a href=#35 id=35 data-nosnippet>35</a>    <span class="attr">#[arg(short, long)]
<a href=#36 id=36 data-nosnippet>36</a>    </span>daemon: bool,
<a href=#37 id=37 data-nosnippet>37</a>}
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a><span class="attr">#[derive(Subcommand, Clone)]
<a href=#40 id=40 data-nosnippet>40</a></span><span class="kw">enum </span>Commands {
<a href=#41 id=41 data-nosnippet>41</a>    <span class="doccomment">/// Start agent in foreground
<a href=#42 id=42 data-nosnippet>42</a>    </span>Start,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="doccomment">/// Install agent as system service
<a href=#44 id=44 data-nosnippet>44</a>    </span>Install,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="doccomment">/// Uninstall agent service
<a href=#46 id=46 data-nosnippet>46</a>    </span>Uninstall,
<a href=#47 id=47 data-nosnippet>47</a>    <span class="doccomment">/// Check agent status
<a href=#48 id=48 data-nosnippet>48</a>    </span>Status,
<a href=#49 id=49 data-nosnippet>49</a>}
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a><span class="attr">#[tokio::main]
<a href=#52 id=52 data-nosnippet>52</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#53 id=53 data-nosnippet>53</a>    <span class="kw">let </span>cli = Cli::parse();
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a>    <span class="comment">// 初始化日志
<a href=#56 id=56 data-nosnippet>56</a>    </span>env_logger::init();
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">let </span>logger = Logger::new(cli.log_file.to_string_lossy().to_string());
<a href=#58 id=58 data-nosnippet>58</a>    logger.ensure_log_directory()<span class="question-mark">?</span>;
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a>    <span class="comment">// 初始化加密密钥
<a href=#61 id=61 data-nosnippet>61</a>    </span><span class="kw">let </span>crypto_key = <span class="kw">if let </span><span class="prelude-val">Some</span>(<span class="kw-2">ref </span>key) = cli.crypto_key {
<a href=#62 id=62 data-nosnippet>62</a>        key.clone().into_bytes()
<a href=#63 id=63 data-nosnippet>63</a>    } <span class="kw">else </span>{
<a href=#64 id=64 data-nosnippet>64</a>        <span class="comment">// 在生产环境中，这应该从安全的地方获取
<a href=#65 id=65 data-nosnippet>65</a>        </span>CryptoManager::generate_key()
<a href=#66 id=66 data-nosnippet>66</a>    };
<a href=#67 id=67 data-nosnippet>67</a>
<a href=#68 id=68 data-nosnippet>68</a>    <span class="comment">// 如果以daemon模式运行，则fork到后台
<a href=#69 id=69 data-nosnippet>69</a>    </span><span class="kw">if </span>cli.daemon {
<a href=#70 id=70 data-nosnippet>70</a>        daemonize()<span class="question-mark">?</span>;
<a href=#71 id=71 data-nosnippet>71</a>    }
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">match </span>cli.command.clone().unwrap_or(Commands::Start) {
<a href=#74 id=74 data-nosnippet>74</a>        Commands::Start =&gt; {
<a href=#75 id=75 data-nosnippet>75</a>            <span class="macro">println!</span>(<span class="string">"Starting agent..."</span>);
<a href=#76 id=76 data-nosnippet>76</a>            <span class="kw">let </span><span class="kw-2">mut </span>agent =
<a href=#77 id=77 data-nosnippet>77</a>                Agent::new(cli.server_url, <span class="kw-2">&amp;</span>crypto_key, logger, cli.heartbeat_interval).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#78 id=78 data-nosnippet>78</a>            agent.start().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#79 id=79 data-nosnippet>79</a>        }
<a href=#80 id=80 data-nosnippet>80</a>        Commands::Install =&gt; {
<a href=#81 id=81 data-nosnippet>81</a>            <span class="macro">println!</span>(<span class="string">"Installing agent as system service..."</span>);
<a href=#82 id=82 data-nosnippet>82</a>            install_service(<span class="kw-2">&amp;</span>cli)<span class="question-mark">?</span>;
<a href=#83 id=83 data-nosnippet>83</a>        }
<a href=#84 id=84 data-nosnippet>84</a>        Commands::Uninstall =&gt; {
<a href=#85 id=85 data-nosnippet>85</a>            <span class="macro">println!</span>(<span class="string">"Uninstalling agent service..."</span>);
<a href=#86 id=86 data-nosnippet>86</a>            uninstall_service()<span class="question-mark">?</span>;
<a href=#87 id=87 data-nosnippet>87</a>        }
<a href=#88 id=88 data-nosnippet>88</a>        Commands::Status =&gt; {
<a href=#89 id=89 data-nosnippet>89</a>            <span class="macro">println!</span>(<span class="string">"Checking agent status..."</span>);
<a href=#90 id=90 data-nosnippet>90</a>            check_status()<span class="question-mark">?</span>;
<a href=#91 id=91 data-nosnippet>91</a>        }
<a href=#92 id=92 data-nosnippet>92</a>    }
<a href=#93 id=93 data-nosnippet>93</a>
<a href=#94 id=94 data-nosnippet>94</a>    <span class="prelude-val">Ok</span>(())
<a href=#95 id=95 data-nosnippet>95</a>}
<a href=#96 id=96 data-nosnippet>96</a>
<a href=#97 id=97 data-nosnippet>97</a><span class="kw">fn </span>daemonize() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#98 id=98 data-nosnippet>98</a>    <span class="attr">#[cfg(unix)]
<a href=#99 id=99 data-nosnippet>99</a>    </span>{
<a href=#100 id=100 data-nosnippet>100</a>        <span class="kw">use </span>std::process;
<a href=#101 id=101 data-nosnippet>101</a>
<a href=#102 id=102 data-nosnippet>102</a>        <span class="comment">// 简单的daemon化实现
<a href=#103 id=103 data-nosnippet>103</a>        </span><span class="kw">match unsafe </span>{ libc::fork() } {
<a href=#104 id=104 data-nosnippet>104</a>            -<span class="number">1 </span>=&gt; <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow::anyhow!</span>(<span class="string">"Fork failed"</span>)),
<a href=#105 id=105 data-nosnippet>105</a>            <span class="number">0 </span>=&gt; {
<a href=#106 id=106 data-nosnippet>106</a>                <span class="comment">// 子进程
<a href=#107 id=107 data-nosnippet>107</a>                </span><span class="kw">unsafe </span>{
<a href=#108 id=108 data-nosnippet>108</a>                    libc::setsid();
<a href=#109 id=109 data-nosnippet>109</a>                }
<a href=#110 id=110 data-nosnippet>110</a>            }
<a href=#111 id=111 data-nosnippet>111</a>            <span class="kw">_ </span>=&gt; {
<a href=#112 id=112 data-nosnippet>112</a>                <span class="comment">// 父进程退出
<a href=#113 id=113 data-nosnippet>113</a>                </span>process::exit(<span class="number">0</span>);
<a href=#114 id=114 data-nosnippet>114</a>            }
<a href=#115 id=115 data-nosnippet>115</a>        }
<a href=#116 id=116 data-nosnippet>116</a>    }
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a>    <span class="attr">#[cfg(not(unix))]
<a href=#119 id=119 data-nosnippet>119</a>    </span>{
<a href=#120 id=120 data-nosnippet>120</a>        <span class="macro">println!</span>(<span class="string">"Daemon mode not supported on this platform, running in foreground"</span>);
<a href=#121 id=121 data-nosnippet>121</a>    }
<a href=#122 id=122 data-nosnippet>122</a>
<a href=#123 id=123 data-nosnippet>123</a>    <span class="prelude-val">Ok</span>(())
<a href=#124 id=124 data-nosnippet>124</a>}
<a href=#125 id=125 data-nosnippet>125</a>
<a href=#126 id=126 data-nosnippet>126</a><span class="kw">fn </span>install_service(cli: <span class="kw-2">&amp;</span>Cli) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#127 id=127 data-nosnippet>127</a>    <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#128 id=128 data-nosnippet>128</a>    </span>{
<a href=#129 id=129 data-nosnippet>129</a>        install_systemd_service(cli)
<a href=#130 id=130 data-nosnippet>130</a>    }
<a href=#131 id=131 data-nosnippet>131</a>
<a href=#132 id=132 data-nosnippet>132</a>    <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#133 id=133 data-nosnippet>133</a>    </span>{
<a href=#134 id=134 data-nosnippet>134</a>        install_launchd_service(cli)
<a href=#135 id=135 data-nosnippet>135</a>    }
<a href=#136 id=136 data-nosnippet>136</a>
<a href=#137 id=137 data-nosnippet>137</a>    <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#138 id=138 data-nosnippet>138</a>    </span>{
<a href=#139 id=139 data-nosnippet>139</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow::anyhow!</span>(
<a href=#140 id=140 data-nosnippet>140</a>            <span class="string">"Service installation not supported on this platform"
<a href=#141 id=141 data-nosnippet>141</a>        </span>))
<a href=#142 id=142 data-nosnippet>142</a>    }
<a href=#143 id=143 data-nosnippet>143</a>}
<a href=#144 id=144 data-nosnippet>144</a>
<a href=#145 id=145 data-nosnippet>145</a><span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#146 id=146 data-nosnippet>146</a></span><span class="kw">fn </span>install_systemd_service(cli: <span class="kw-2">&amp;</span>Cli) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#147 id=147 data-nosnippet>147</a>    <span class="kw">let </span>current_exe = std::env::current_exe()<span class="question-mark">?</span>;
<a href=#148 id=148 data-nosnippet>148</a>    <span class="kw">let </span>service_content = <span class="macro">format!</span>(
<a href=#149 id=149 data-nosnippet>149</a>        <span class="string">r#"[Unit]
<a href=#150 id=150 data-nosnippet>150</a>Description=Ubuntu Remote Control Agent
<a href=#151 id=151 data-nosnippet>151</a>After=network.target
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>[Service]
<a href=#154 id=154 data-nosnippet>154</a>Type=simple
<a href=#155 id=155 data-nosnippet>155</a>User=root
<a href=#156 id=156 data-nosnippet>156</a>ExecStart={} --daemon --server-url {} --log-file {}
<a href=#157 id=157 data-nosnippet>157</a>Restart=always
<a href=#158 id=158 data-nosnippet>158</a>RestartSec=5
<a href=#159 id=159 data-nosnippet>159</a>StandardOutput=journal
<a href=#160 id=160 data-nosnippet>160</a>StandardError=journal
<a href=#161 id=161 data-nosnippet>161</a>
<a href=#162 id=162 data-nosnippet>162</a>[Install]
<a href=#163 id=163 data-nosnippet>163</a>WantedBy=multi-user.target"#</span>,
<a href=#164 id=164 data-nosnippet>164</a>        current_exe.display(),
<a href=#165 id=165 data-nosnippet>165</a>        cli.server_url,
<a href=#166 id=166 data-nosnippet>166</a>        cli.log_file.display()
<a href=#167 id=167 data-nosnippet>167</a>    );
<a href=#168 id=168 data-nosnippet>168</a>
<a href=#169 id=169 data-nosnippet>169</a>    std::fs::write(<span class="string">"/etc/systemd/system/remote-agent.service"</span>, service_content)<span class="question-mark">?</span>;
<a href=#170 id=170 data-nosnippet>170</a>
<a href=#171 id=171 data-nosnippet>171</a>    <span class="comment">// 启用并启动服务
<a href=#172 id=172 data-nosnippet>172</a>    </span>std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#173 id=173 data-nosnippet>173</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"daemon-reload"</span>])
<a href=#174 id=174 data-nosnippet>174</a>        .status()<span class="question-mark">?</span>;
<a href=#175 id=175 data-nosnippet>175</a>
<a href=#176 id=176 data-nosnippet>176</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#177 id=177 data-nosnippet>177</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"enable"</span>, <span class="string">"remote-agent"</span>])
<a href=#178 id=178 data-nosnippet>178</a>        .status()<span class="question-mark">?</span>;
<a href=#179 id=179 data-nosnippet>179</a>
<a href=#180 id=180 data-nosnippet>180</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#181 id=181 data-nosnippet>181</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"start"</span>, <span class="string">"remote-agent"</span>])
<a href=#182 id=182 data-nosnippet>182</a>        .status()<span class="question-mark">?</span>;
<a href=#183 id=183 data-nosnippet>183</a>
<a href=#184 id=184 data-nosnippet>184</a>    <span class="macro">println!</span>(<span class="string">"Agent service installed and started successfully (systemd)"</span>);
<a href=#185 id=185 data-nosnippet>185</a>    <span class="prelude-val">Ok</span>(())
<a href=#186 id=186 data-nosnippet>186</a>}
<a href=#187 id=187 data-nosnippet>187</a>
<a href=#188 id=188 data-nosnippet>188</a><span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#189 id=189 data-nosnippet>189</a></span><span class="kw">fn </span>install_launchd_service(cli: <span class="kw-2">&amp;</span>Cli) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#190 id=190 data-nosnippet>190</a>    <span class="kw">let </span>current_exe = std::env::current_exe()<span class="question-mark">?</span>;
<a href=#191 id=191 data-nosnippet>191</a>    <span class="kw">let </span>home_dir = std::env::var(<span class="string">"HOME"</span>)<span class="question-mark">?</span>;
<a href=#192 id=192 data-nosnippet>192</a>    <span class="kw">let </span>plist_path = <span class="macro">format!</span>(
<a href=#193 id=193 data-nosnippet>193</a>        <span class="string">"{}/Library/LaunchAgents/com.remotecontrol.agent.plist"</span>,
<a href=#194 id=194 data-nosnippet>194</a>        home_dir
<a href=#195 id=195 data-nosnippet>195</a>    );
<a href=#196 id=196 data-nosnippet>196</a>
<a href=#197 id=197 data-nosnippet>197</a>    <span class="kw">let </span>plist_content = <span class="macro">format!</span>(
<a href=#198 id=198 data-nosnippet>198</a>        <span class="string">r#"&lt;?xml version="1.0" encoding="UTF-8"?&gt;
<a href=#199 id=199 data-nosnippet>199</a>&lt;!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd"&gt;
<a href=#200 id=200 data-nosnippet>200</a>&lt;plist version="1.0"&gt;
<a href=#201 id=201 data-nosnippet>201</a>&lt;dict&gt;
<a href=#202 id=202 data-nosnippet>202</a>    &lt;key&gt;Label&lt;/key&gt;
<a href=#203 id=203 data-nosnippet>203</a>    &lt;string&gt;com.remotecontrol.agent&lt;/string&gt;
<a href=#204 id=204 data-nosnippet>204</a>    &lt;key&gt;ProgramArguments&lt;/key&gt;
<a href=#205 id=205 data-nosnippet>205</a>    &lt;array&gt;
<a href=#206 id=206 data-nosnippet>206</a>        &lt;string&gt;{}&lt;/string&gt;
<a href=#207 id=207 data-nosnippet>207</a>        &lt;string&gt;start&lt;/string&gt;
<a href=#208 id=208 data-nosnippet>208</a>        &lt;string&gt;--server-url&lt;/string&gt;
<a href=#209 id=209 data-nosnippet>209</a>        &lt;string&gt;{}&lt;/string&gt;
<a href=#210 id=210 data-nosnippet>210</a>        &lt;string&gt;--log-file&lt;/string&gt;
<a href=#211 id=211 data-nosnippet>211</a>        &lt;string&gt;{}&lt;/string&gt;
<a href=#212 id=212 data-nosnippet>212</a>    &lt;/array&gt;
<a href=#213 id=213 data-nosnippet>213</a>    &lt;key&gt;RunAtLoad&lt;/key&gt;
<a href=#214 id=214 data-nosnippet>214</a>    &lt;true/&gt;
<a href=#215 id=215 data-nosnippet>215</a>    &lt;key&gt;KeepAlive&lt;/key&gt;
<a href=#216 id=216 data-nosnippet>216</a>    &lt;true/&gt;
<a href=#217 id=217 data-nosnippet>217</a>    &lt;key&gt;StandardOutPath&lt;/key&gt;
<a href=#218 id=218 data-nosnippet>218</a>    &lt;string&gt;{}/Library/Logs/remote-agent.log&lt;/string&gt;
<a href=#219 id=219 data-nosnippet>219</a>    &lt;key&gt;StandardErrorPath&lt;/key&gt;
<a href=#220 id=220 data-nosnippet>220</a>    &lt;string&gt;{}/Library/Logs/remote-agent.error.log&lt;/string&gt;
<a href=#221 id=221 data-nosnippet>221</a>&lt;/dict&gt;
<a href=#222 id=222 data-nosnippet>222</a>&lt;/plist&gt;"#</span>,
<a href=#223 id=223 data-nosnippet>223</a>        current_exe.display(),
<a href=#224 id=224 data-nosnippet>224</a>        cli.server_url,
<a href=#225 id=225 data-nosnippet>225</a>        cli.log_file.display(),
<a href=#226 id=226 data-nosnippet>226</a>        home_dir,
<a href=#227 id=227 data-nosnippet>227</a>        home_dir
<a href=#228 id=228 data-nosnippet>228</a>    );
<a href=#229 id=229 data-nosnippet>229</a>
<a href=#230 id=230 data-nosnippet>230</a>    <span class="comment">// 创建目录
<a href=#231 id=231 data-nosnippet>231</a>    </span>std::fs::create_dir_all(<span class="macro">format!</span>(<span class="string">"{}/Library/LaunchAgents"</span>, home_dir))<span class="question-mark">?</span>;
<a href=#232 id=232 data-nosnippet>232</a>    std::fs::create_dir_all(<span class="macro">format!</span>(<span class="string">"{}/Library/Logs"</span>, home_dir))<span class="question-mark">?</span>;
<a href=#233 id=233 data-nosnippet>233</a>
<a href=#234 id=234 data-nosnippet>234</a>    <span class="comment">// 写入 plist 文件
<a href=#235 id=235 data-nosnippet>235</a>    </span>std::fs::write(<span class="kw-2">&amp;</span>plist_path, plist_content)<span class="question-mark">?</span>;
<a href=#236 id=236 data-nosnippet>236</a>
<a href=#237 id=237 data-nosnippet>237</a>    <span class="comment">// 加载服务
<a href=#238 id=238 data-nosnippet>238</a>    </span>std::process::Command::new(<span class="string">"launchctl"</span>)
<a href=#239 id=239 data-nosnippet>239</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"load"</span>, <span class="kw-2">&amp;</span>plist_path])
<a href=#240 id=240 data-nosnippet>240</a>        .status()<span class="question-mark">?</span>;
<a href=#241 id=241 data-nosnippet>241</a>
<a href=#242 id=242 data-nosnippet>242</a>    <span class="macro">println!</span>(<span class="string">"Agent service installed and started successfully (launchd)"</span>);
<a href=#243 id=243 data-nosnippet>243</a>    <span class="macro">println!</span>(<span class="string">"Service file: {}"</span>, plist_path);
<a href=#244 id=244 data-nosnippet>244</a>    <span class="prelude-val">Ok</span>(())
<a href=#245 id=245 data-nosnippet>245</a>}
<a href=#246 id=246 data-nosnippet>246</a>
<a href=#247 id=247 data-nosnippet>247</a><span class="kw">fn </span>uninstall_service() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#248 id=248 data-nosnippet>248</a>    <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#249 id=249 data-nosnippet>249</a>    </span>{
<a href=#250 id=250 data-nosnippet>250</a>        uninstall_systemd_service()
<a href=#251 id=251 data-nosnippet>251</a>    }
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>    <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#254 id=254 data-nosnippet>254</a>    </span>{
<a href=#255 id=255 data-nosnippet>255</a>        uninstall_launchd_service()
<a href=#256 id=256 data-nosnippet>256</a>    }
<a href=#257 id=257 data-nosnippet>257</a>
<a href=#258 id=258 data-nosnippet>258</a>    <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#259 id=259 data-nosnippet>259</a>    </span>{
<a href=#260 id=260 data-nosnippet>260</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow::anyhow!</span>(
<a href=#261 id=261 data-nosnippet>261</a>            <span class="string">"Service uninstallation not supported on this platform"
<a href=#262 id=262 data-nosnippet>262</a>        </span>))
<a href=#263 id=263 data-nosnippet>263</a>    }
<a href=#264 id=264 data-nosnippet>264</a>}
<a href=#265 id=265 data-nosnippet>265</a>
<a href=#266 id=266 data-nosnippet>266</a><span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#267 id=267 data-nosnippet>267</a></span><span class="kw">fn </span>uninstall_systemd_service() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#268 id=268 data-nosnippet>268</a>    <span class="comment">// 停止并禁用服务
<a href=#269 id=269 data-nosnippet>269</a>    </span>std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#270 id=270 data-nosnippet>270</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"stop"</span>, <span class="string">"remote-agent"</span>])
<a href=#271 id=271 data-nosnippet>271</a>        .status()<span class="question-mark">?</span>;
<a href=#272 id=272 data-nosnippet>272</a>
<a href=#273 id=273 data-nosnippet>273</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#274 id=274 data-nosnippet>274</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"disable"</span>, <span class="string">"remote-agent"</span>])
<a href=#275 id=275 data-nosnippet>275</a>        .status()<span class="question-mark">?</span>;
<a href=#276 id=276 data-nosnippet>276</a>
<a href=#277 id=277 data-nosnippet>277</a>    <span class="comment">// 删除服务文件
<a href=#278 id=278 data-nosnippet>278</a>    </span><span class="kw">if </span>std::path::Path::new(<span class="string">"/etc/systemd/system/remote-agent.service"</span>).exists() {
<a href=#279 id=279 data-nosnippet>279</a>        std::fs::remove_file(<span class="string">"/etc/systemd/system/remote-agent.service"</span>)<span class="question-mark">?</span>;
<a href=#280 id=280 data-nosnippet>280</a>    }
<a href=#281 id=281 data-nosnippet>281</a>
<a href=#282 id=282 data-nosnippet>282</a>    std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#283 id=283 data-nosnippet>283</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"daemon-reload"</span>])
<a href=#284 id=284 data-nosnippet>284</a>        .status()<span class="question-mark">?</span>;
<a href=#285 id=285 data-nosnippet>285</a>
<a href=#286 id=286 data-nosnippet>286</a>    <span class="macro">println!</span>(<span class="string">"Agent service uninstalled successfully (systemd)"</span>);
<a href=#287 id=287 data-nosnippet>287</a>    <span class="prelude-val">Ok</span>(())
<a href=#288 id=288 data-nosnippet>288</a>}
<a href=#289 id=289 data-nosnippet>289</a>
<a href=#290 id=290 data-nosnippet>290</a><span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#291 id=291 data-nosnippet>291</a></span><span class="kw">fn </span>uninstall_launchd_service() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#292 id=292 data-nosnippet>292</a>    <span class="kw">let </span>home_dir = std::env::var(<span class="string">"HOME"</span>)<span class="question-mark">?</span>;
<a href=#293 id=293 data-nosnippet>293</a>    <span class="kw">let </span>plist_path = <span class="macro">format!</span>(
<a href=#294 id=294 data-nosnippet>294</a>        <span class="string">"{}/Library/LaunchAgents/com.remotecontrol.agent.plist"</span>,
<a href=#295 id=295 data-nosnippet>295</a>        home_dir
<a href=#296 id=296 data-nosnippet>296</a>    );
<a href=#297 id=297 data-nosnippet>297</a>
<a href=#298 id=298 data-nosnippet>298</a>    <span class="comment">// 卸载服务
<a href=#299 id=299 data-nosnippet>299</a>    </span><span class="kw">if </span>std::path::Path::new(<span class="kw-2">&amp;</span>plist_path).exists() {
<a href=#300 id=300 data-nosnippet>300</a>        std::process::Command::new(<span class="string">"launchctl"</span>)
<a href=#301 id=301 data-nosnippet>301</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"unload"</span>, <span class="kw-2">&amp;</span>plist_path])
<a href=#302 id=302 data-nosnippet>302</a>            .status()<span class="question-mark">?</span>;
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>        std::fs::remove_file(<span class="kw-2">&amp;</span>plist_path)<span class="question-mark">?</span>;
<a href=#305 id=305 data-nosnippet>305</a>    }
<a href=#306 id=306 data-nosnippet>306</a>
<a href=#307 id=307 data-nosnippet>307</a>    <span class="macro">println!</span>(<span class="string">"Agent service uninstalled successfully (launchd)"</span>);
<a href=#308 id=308 data-nosnippet>308</a>    <span class="prelude-val">Ok</span>(())
<a href=#309 id=309 data-nosnippet>309</a>}
<a href=#310 id=310 data-nosnippet>310</a>
<a href=#311 id=311 data-nosnippet>311</a><span class="kw">fn </span>check_status() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#312 id=312 data-nosnippet>312</a>    <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#313 id=313 data-nosnippet>313</a>    </span>{
<a href=#314 id=314 data-nosnippet>314</a>        check_systemd_status()
<a href=#315 id=315 data-nosnippet>315</a>    }
<a href=#316 id=316 data-nosnippet>316</a>
<a href=#317 id=317 data-nosnippet>317</a>    <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#318 id=318 data-nosnippet>318</a>    </span>{
<a href=#319 id=319 data-nosnippet>319</a>        check_launchd_status()
<a href=#320 id=320 data-nosnippet>320</a>    }
<a href=#321 id=321 data-nosnippet>321</a>
<a href=#322 id=322 data-nosnippet>322</a>    <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#323 id=323 data-nosnippet>323</a>    </span>{
<a href=#324 id=324 data-nosnippet>324</a>        <span class="macro">println!</span>(<span class="string">"Status check not supported on this platform"</span>);
<a href=#325 id=325 data-nosnippet>325</a>        <span class="prelude-val">Ok</span>(())
<a href=#326 id=326 data-nosnippet>326</a>    }
<a href=#327 id=327 data-nosnippet>327</a>}
<a href=#328 id=328 data-nosnippet>328</a>
<a href=#329 id=329 data-nosnippet>329</a><span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#330 id=330 data-nosnippet>330</a></span><span class="kw">fn </span>check_systemd_status() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#331 id=331 data-nosnippet>331</a>    <span class="kw">let </span>output = std::process::Command::new(<span class="string">"systemctl"</span>)
<a href=#332 id=332 data-nosnippet>332</a>        .args(<span class="kw-2">&amp;</span>[<span class="string">"status"</span>, <span class="string">"remote-agent"</span>])
<a href=#333 id=333 data-nosnippet>333</a>        .output()<span class="question-mark">?</span>;
<a href=#334 id=334 data-nosnippet>334</a>
<a href=#335 id=335 data-nosnippet>335</a>    <span class="macro">println!</span>(<span class="string">"{}"</span>, String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout));
<a href=#336 id=336 data-nosnippet>336</a>
<a href=#337 id=337 data-nosnippet>337</a>    <span class="kw">if </span>!output.stderr.is_empty() {
<a href=#338 id=338 data-nosnippet>338</a>        <span class="macro">eprintln!</span>(<span class="string">"{}"</span>, String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stderr));
<a href=#339 id=339 data-nosnippet>339</a>    }
<a href=#340 id=340 data-nosnippet>340</a>
<a href=#341 id=341 data-nosnippet>341</a>    <span class="prelude-val">Ok</span>(())
<a href=#342 id=342 data-nosnippet>342</a>}
<a href=#343 id=343 data-nosnippet>343</a>
<a href=#344 id=344 data-nosnippet>344</a><span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#345 id=345 data-nosnippet>345</a></span><span class="kw">fn </span>check_launchd_status() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#346 id=346 data-nosnippet>346</a>    <span class="kw">let </span>home_dir = std::env::var(<span class="string">"HOME"</span>)<span class="question-mark">?</span>;
<a href=#347 id=347 data-nosnippet>347</a>    <span class="kw">let </span>plist_path = <span class="macro">format!</span>(
<a href=#348 id=348 data-nosnippet>348</a>        <span class="string">"{}/Library/LaunchAgents/com.remotecontrol.agent.plist"</span>,
<a href=#349 id=349 data-nosnippet>349</a>        home_dir
<a href=#350 id=350 data-nosnippet>350</a>    );
<a href=#351 id=351 data-nosnippet>351</a>
<a href=#352 id=352 data-nosnippet>352</a>    <span class="kw">if </span>std::path::Path::new(<span class="kw-2">&amp;</span>plist_path).exists() {
<a href=#353 id=353 data-nosnippet>353</a>        <span class="kw">let </span>output = std::process::Command::new(<span class="string">"launchctl"</span>)
<a href=#354 id=354 data-nosnippet>354</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"list"</span>, <span class="string">"com.remotecontrol.agent"</span>])
<a href=#355 id=355 data-nosnippet>355</a>            .output()<span class="question-mark">?</span>;
<a href=#356 id=356 data-nosnippet>356</a>
<a href=#357 id=357 data-nosnippet>357</a>        <span class="kw">if </span>output.status.success() {
<a href=#358 id=358 data-nosnippet>358</a>            <span class="macro">println!</span>(<span class="string">"Agent service is running"</span>);
<a href=#359 id=359 data-nosnippet>359</a>            <span class="macro">println!</span>(<span class="string">"{}"</span>, String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout));
<a href=#360 id=360 data-nosnippet>360</a>        } <span class="kw">else </span>{
<a href=#361 id=361 data-nosnippet>361</a>            <span class="macro">println!</span>(<span class="string">"Agent service is not running"</span>);
<a href=#362 id=362 data-nosnippet>362</a>            <span class="kw">if </span>!output.stderr.is_empty() {
<a href=#363 id=363 data-nosnippet>363</a>                <span class="macro">eprintln!</span>(<span class="string">"{}"</span>, String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stderr));
<a href=#364 id=364 data-nosnippet>364</a>            }
<a href=#365 id=365 data-nosnippet>365</a>        }
<a href=#366 id=366 data-nosnippet>366</a>    } <span class="kw">else </span>{
<a href=#367 id=367 data-nosnippet>367</a>        <span class="macro">println!</span>(<span class="string">"Agent service is not installed"</span>);
<a href=#368 id=368 data-nosnippet>368</a>    }
<a href=#369 id=369 data-nosnippet>369</a>
<a href=#370 id=370 data-nosnippet>370</a>    <span class="prelude-val">Ok</span>(())
<a href=#371 id=371 data-nosnippet>371</a>}</code></pre></div></section></main></body></html>