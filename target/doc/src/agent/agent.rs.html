<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/agent.rs`."><title>agent.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>agent.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::elf_loader::ElfLoader;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::executor::CommandExecutor;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span><span class="kw">crate</span>::file_manager::FileManager;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span><span class="kw">crate</span>::system_info::SystemInfoCollector;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>common::{
<a href=#7 id=7 data-nosnippet>7</a>    CommandResult, FileData, HttpCommunicator, LogCategory, LogEntry, LogLevel, Logger, Message,
<a href=#8 id=8 data-nosnippet>8</a>    MessageType, ProcessInfo, SystemInfoResult,
<a href=#9 id=9 data-nosnippet>9</a>};
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">use </span>std::time::Duration;
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">use </span>tokio::time::{interval, sleep};
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">use </span>uuid::Uuid;
<a href=#13 id=13 data-nosnippet>13</a>
<a href=#14 id=14 data-nosnippet>14</a><span class="kw">pub struct </span>Agent {
<a href=#15 id=15 data-nosnippet>15</a>    id: Uuid,
<a href=#16 id=16 data-nosnippet>16</a>    communicator: HttpCommunicator,
<a href=#17 id=17 data-nosnippet>17</a>    logger: Logger,
<a href=#18 id=18 data-nosnippet>18</a>    heartbeat_interval: Duration,
<a href=#19 id=19 data-nosnippet>19</a>    executor: CommandExecutor,
<a href=#20 id=20 data-nosnippet>20</a>    system_info: SystemInfoCollector,
<a href=#21 id=21 data-nosnippet>21</a>    file_manager: FileManager,
<a href=#22 id=22 data-nosnippet>22</a>    elf_loader: ElfLoader,
<a href=#23 id=23 data-nosnippet>23</a>    running: bool,
<a href=#24 id=24 data-nosnippet>24</a>}
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a><span class="kw">impl </span>Agent {
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub async fn </span>new(
<a href=#28 id=28 data-nosnippet>28</a>        server_url: String,
<a href=#29 id=29 data-nosnippet>29</a>        crypto_key: <span class="kw-2">&amp;</span>[u8],
<a href=#30 id=30 data-nosnippet>30</a>        logger: Logger,
<a href=#31 id=31 data-nosnippet>31</a>        heartbeat_interval: u64,
<a href=#32 id=32 data-nosnippet>32</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#33 id=33 data-nosnippet>33</a>        <span class="kw">let </span>communicator = HttpCommunicator::new(server_url, crypto_key)<span class="question-mark">?</span>;
<a href=#34 id=34 data-nosnippet>34</a>        <span class="kw">let </span>id = Uuid::new_v4();
<a href=#35 id=35 data-nosnippet>35</a>
<a href=#36 id=36 data-nosnippet>36</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#37 id=37 data-nosnippet>37</a>            id,
<a href=#38 id=38 data-nosnippet>38</a>            communicator,
<a href=#39 id=39 data-nosnippet>39</a>            logger,
<a href=#40 id=40 data-nosnippet>40</a>            heartbeat_interval: Duration::from_secs(heartbeat_interval),
<a href=#41 id=41 data-nosnippet>41</a>            executor: CommandExecutor::new(),
<a href=#42 id=42 data-nosnippet>42</a>            system_info: SystemInfoCollector::new(),
<a href=#43 id=43 data-nosnippet>43</a>            file_manager: FileManager::new(),
<a href=#44 id=44 data-nosnippet>44</a>            elf_loader: ElfLoader::new(),
<a href=#45 id=45 data-nosnippet>45</a>            running: <span class="bool-val">false</span>,
<a href=#46 id=46 data-nosnippet>46</a>        })
<a href=#47 id=47 data-nosnippet>47</a>    }
<a href=#48 id=48 data-nosnippet>48</a>
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub async fn </span>start(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#50 id=50 data-nosnippet>50</a>        <span class="self">self</span>.running = <span class="bool-val">true</span>;
<a href=#51 id=51 data-nosnippet>51</a>        <span class="self">self</span>.log_info(<span class="string">"Agent starting up"</span>).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#52 id=52 data-nosnippet>52</a>
<a href=#53 id=53 data-nosnippet>53</a>        <span class="comment">// 发送初始系统信息
<a href=#54 id=54 data-nosnippet>54</a>        </span><span class="self">self</span>.send_initial_info().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#55 id=55 data-nosnippet>55</a>
<a href=#56 id=56 data-nosnippet>56</a>        <span class="comment">// 启动心跳任务
<a href=#57 id=57 data-nosnippet>57</a>        </span><span class="kw">let </span>communicator_clone = <span class="self">self</span>.communicator.clone();
<a href=#58 id=58 data-nosnippet>58</a>        <span class="kw">let </span>heartbeat_interval = <span class="self">self</span>.heartbeat_interval;
<a href=#59 id=59 data-nosnippet>59</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#60 id=60 data-nosnippet>60</a>            <span class="kw">let </span><span class="kw-2">mut </span>interval = interval(heartbeat_interval);
<a href=#61 id=61 data-nosnippet>61</a>            <span class="kw">loop </span>{
<a href=#62 id=62 data-nosnippet>62</a>                interval.tick().<span class="kw">await</span>;
<a href=#63 id=63 data-nosnippet>63</a>                <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = communicator_clone.send_heartbeat().<span class="kw">await </span>{
<a href=#64 id=64 data-nosnippet>64</a>                    <span class="macro">log::error!</span>(<span class="string">"Failed to send heartbeat: {}"</span>, e);
<a href=#65 id=65 data-nosnippet>65</a>                }
<a href=#66 id=66 data-nosnippet>66</a>            }
<a href=#67 id=67 data-nosnippet>67</a>        });
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a>        <span class="comment">// 主消息处理循环
<a href=#70 id=70 data-nosnippet>70</a>        </span><span class="kw">while </span><span class="self">self</span>.running {
<a href=#71 id=71 data-nosnippet>71</a>            <span class="kw">match </span><span class="self">self</span>.communicator.receive_message().<span class="kw">await </span>{
<a href=#72 id=72 data-nosnippet>72</a>                <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(message)) =&gt; {
<a href=#73 id=73 data-nosnippet>73</a>                    <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = <span class="self">self</span>.handle_message(message).<span class="kw">await </span>{
<a href=#74 id=74 data-nosnippet>74</a>                        <span class="self">self</span>.log_error(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Error handling message: {}"</span>, e))
<a href=#75 id=75 data-nosnippet>75</a>                            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#76 id=76 data-nosnippet>76</a>                    }
<a href=#77 id=77 data-nosnippet>77</a>                }
<a href=#78 id=78 data-nosnippet>78</a>                <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>) =&gt; {
<a href=#79 id=79 data-nosnippet>79</a>                    <span class="comment">// 没有消息，短暂休眠
<a href=#80 id=80 data-nosnippet>80</a>                    </span>sleep(Duration::from_millis(<span class="number">100</span>)).<span class="kw">await</span>;
<a href=#81 id=81 data-nosnippet>81</a>                }
<a href=#82 id=82 data-nosnippet>82</a>                <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#83 id=83 data-nosnippet>83</a>                    <span class="self">self</span>.log_error(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Communication error: {}"</span>, e))
<a href=#84 id=84 data-nosnippet>84</a>                        .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#85 id=85 data-nosnippet>85</a>                    sleep(Duration::from_secs(<span class="number">5</span>)).<span class="kw">await</span>;
<a href=#86 id=86 data-nosnippet>86</a>                }
<a href=#87 id=87 data-nosnippet>87</a>            }
<a href=#88 id=88 data-nosnippet>88</a>        }
<a href=#89 id=89 data-nosnippet>89</a>
<a href=#90 id=90 data-nosnippet>90</a>        <span class="self">self</span>.log_info(<span class="string">"Agent shutting down"</span>).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#91 id=91 data-nosnippet>91</a>        <span class="prelude-val">Ok</span>(())
<a href=#92 id=92 data-nosnippet>92</a>    }
<a href=#93 id=93 data-nosnippet>93</a>
<a href=#94 id=94 data-nosnippet>94</a>    <span class="kw">pub fn </span>stop(<span class="kw-2">&amp;mut </span><span class="self">self</span>) {
<a href=#95 id=95 data-nosnippet>95</a>        <span class="self">self</span>.running = <span class="bool-val">false</span>;
<a href=#96 id=96 data-nosnippet>96</a>    }
<a href=#97 id=97 data-nosnippet>97</a>
<a href=#98 id=98 data-nosnippet>98</a>    <span class="kw">async fn </span>send_initial_info(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#99 id=99 data-nosnippet>99</a>        <span class="kw">let </span>system_info = <span class="self">self</span>.system_info.collect().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#100 id=100 data-nosnippet>100</a>        <span class="kw">let </span>message = Message::new(MessageType::SystemInfoResult(system_info));
<a href=#101 id=101 data-nosnippet>101</a>        <span class="self">self</span>.communicator.send_message(<span class="kw-2">&amp;</span>message).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#102 id=102 data-nosnippet>102</a>        <span class="prelude-val">Ok</span>(())
<a href=#103 id=103 data-nosnippet>103</a>    }
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>    <span class="kw">async fn </span>handle_message(<span class="kw-2">&amp;mut </span><span class="self">self</span>, message: Message) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#106 id=106 data-nosnippet>106</a>        <span class="comment">// 验证消息完整性
<a href=#107 id=107 data-nosnippet>107</a>        </span><span class="kw">if </span>!message.verify_checksum() {
<a href=#108 id=108 data-nosnippet>108</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Message checksum verification failed"</span>));
<a href=#109 id=109 data-nosnippet>109</a>        }
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>        <span class="kw">match </span>message.message_type {
<a href=#112 id=112 data-nosnippet>112</a>            MessageType::Command(cmd_msg) =&gt; {
<a href=#113 id=113 data-nosnippet>113</a>                <span class="self">self</span>.log_info(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Executing command: {}"</span>, cmd_msg.command))
<a href=#114 id=114 data-nosnippet>114</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>                <span class="kw">let </span>result = <span class="self">self
<a href=#117 id=117 data-nosnippet>117</a>                    </span>.executor
<a href=#118 id=118 data-nosnippet>118</a>                    .execute_command(
<a href=#119 id=119 data-nosnippet>119</a>                        <span class="kw-2">&amp;</span>cmd_msg.command,
<a href=#120 id=120 data-nosnippet>120</a>                        <span class="kw-2">&amp;</span>cmd_msg.args,
<a href=#121 id=121 data-nosnippet>121</a>                        cmd_msg.working_dir.as_deref(),
<a href=#122 id=122 data-nosnippet>122</a>                        cmd_msg.timeout,
<a href=#123 id=123 data-nosnippet>123</a>                    )
<a href=#124 id=124 data-nosnippet>124</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#125 id=125 data-nosnippet>125</a>
<a href=#126 id=126 data-nosnippet>126</a>                <span class="kw">let </span>response = Message::new(MessageType::CommandResult(result));
<a href=#127 id=127 data-nosnippet>127</a>                <span class="self">self</span>.communicator.send_message(<span class="kw-2">&amp;</span>response).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#128 id=128 data-nosnippet>128</a>            }
<a href=#129 id=129 data-nosnippet>129</a>
<a href=#130 id=130 data-nosnippet>130</a>            MessageType::FileUpload(upload_msg) =&gt; {
<a href=#131 id=131 data-nosnippet>131</a>                <span class="self">self</span>.log_info(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Receiving file: {}"</span>, upload_msg.file_path))
<a href=#132 id=132 data-nosnippet>132</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a>                <span class="self">self</span>.file_manager
<a href=#135 id=135 data-nosnippet>135</a>                    .receive_file_chunk(
<a href=#136 id=136 data-nosnippet>136</a>                        <span class="kw-2">&amp;</span>upload_msg.file_path,
<a href=#137 id=137 data-nosnippet>137</a>                        upload_msg.chunk_index,
<a href=#138 id=138 data-nosnippet>138</a>                        upload_msg.total_chunks,
<a href=#139 id=139 data-nosnippet>139</a>                        <span class="kw-2">&amp;</span>upload_msg.data,
<a href=#140 id=140 data-nosnippet>140</a>                    )
<a href=#141 id=141 data-nosnippet>141</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>                <span class="comment">// 发送确认
<a href=#144 id=144 data-nosnippet>144</a>                </span><span class="kw">let </span>ack = Message::new(MessageType::Ack);
<a href=#145 id=145 data-nosnippet>145</a>                <span class="self">self</span>.communicator.send_message(<span class="kw-2">&amp;</span>ack).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#146 id=146 data-nosnippet>146</a>            }
<a href=#147 id=147 data-nosnippet>147</a>
<a href=#148 id=148 data-nosnippet>148</a>            MessageType::FileDownload(download_msg) =&gt; {
<a href=#149 id=149 data-nosnippet>149</a>                <span class="self">self</span>.log_info(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Sending file: {}"</span>, download_msg.file_path))
<a href=#150 id=150 data-nosnippet>150</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>                <span class="kw">let </span>file_data = <span class="self">self
<a href=#153 id=153 data-nosnippet>153</a>                    </span>.file_manager
<a href=#154 id=154 data-nosnippet>154</a>                    .send_file(
<a href=#155 id=155 data-nosnippet>155</a>                        <span class="kw-2">&amp;</span>download_msg.file_path,
<a href=#156 id=156 data-nosnippet>156</a>                        download_msg.chunk_size.unwrap_or(<span class="number">64 </span>* <span class="number">1024</span>),
<a href=#157 id=157 data-nosnippet>157</a>                    )
<a href=#158 id=158 data-nosnippet>158</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#159 id=159 data-nosnippet>159</a>
<a href=#160 id=160 data-nosnippet>160</a>                <span class="kw">for </span>chunk <span class="kw">in </span>file_data {
<a href=#161 id=161 data-nosnippet>161</a>                    <span class="kw">let </span>response = Message::new(MessageType::FileData(chunk));
<a href=#162 id=162 data-nosnippet>162</a>                    <span class="self">self</span>.communicator.send_message(<span class="kw-2">&amp;</span>response).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#163 id=163 data-nosnippet>163</a>                }
<a href=#164 id=164 data-nosnippet>164</a>            }
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>            MessageType::ProcessManagement(process_msg) =&gt; {
<a href=#167 id=167 data-nosnippet>167</a>                <span class="self">self</span>.log_info(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Process management: {:?}"</span>, process_msg.action))
<a href=#168 id=168 data-nosnippet>168</a>                    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>                <span class="kw">let </span>process_info = <span class="self">self</span>.executor.manage_process(process_msg.action).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#171 id=171 data-nosnippet>171</a>                <span class="kw">let </span>response = Message::new(MessageType::ProcessInfo(process_info));
<a href=#172 id=172 data-nosnippet>172</a>                <span class="self">self</span>.communicator.send_message(<span class="kw-2">&amp;</span>response).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#173 id=173 data-nosnippet>173</a>            }
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a>            MessageType::SystemInfo =&gt; {
<a href=#176 id=176 data-nosnippet>176</a>                <span class="self">self</span>.log_info(<span class="string">"Collecting system information"</span>).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#177 id=177 data-nosnippet>177</a>
<a href=#178 id=178 data-nosnippet>178</a>                <span class="kw">let </span>system_info = <span class="self">self</span>.system_info.collect().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#179 id=179 data-nosnippet>179</a>                <span class="kw">let </span>response = Message::new(MessageType::SystemInfoResult(system_info));
<a href=#180 id=180 data-nosnippet>180</a>                <span class="self">self</span>.communicator.send_message(<span class="kw-2">&amp;</span>response).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#181 id=181 data-nosnippet>181</a>            }
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a>            <span class="kw">_ </span>=&gt; {
<a href=#184 id=184 data-nosnippet>184</a>                <span class="self">self</span>.log_info(<span class="kw-2">&amp;</span><span class="macro">format!</span>(
<a href=#185 id=185 data-nosnippet>185</a>                    <span class="string">"Received unsupported message type: {:?}"</span>,
<a href=#186 id=186 data-nosnippet>186</a>                    message.message_type
<a href=#187 id=187 data-nosnippet>187</a>                ))
<a href=#188 id=188 data-nosnippet>188</a>                .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#189 id=189 data-nosnippet>189</a>            }
<a href=#190 id=190 data-nosnippet>190</a>        }
<a href=#191 id=191 data-nosnippet>191</a>
<a href=#192 id=192 data-nosnippet>192</a>        <span class="prelude-val">Ok</span>(())
<a href=#193 id=193 data-nosnippet>193</a>    }
<a href=#194 id=194 data-nosnippet>194</a>
<a href=#195 id=195 data-nosnippet>195</a>    <span class="kw">async fn </span>log_info(<span class="kw-2">&amp;</span><span class="self">self</span>, message: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#196 id=196 data-nosnippet>196</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#197 id=197 data-nosnippet>197</a>            id: Uuid::new_v4(),
<a href=#198 id=198 data-nosnippet>198</a>            timestamp: chrono::Utc::now(),
<a href=#199 id=199 data-nosnippet>199</a>            level: LogLevel::Info,
<a href=#200 id=200 data-nosnippet>200</a>            category: LogCategory::Communication,
<a href=#201 id=201 data-nosnippet>201</a>            user_id: <span class="prelude-val">Some</span>(<span class="string">"agent"</span>.to_string()),
<a href=#202 id=202 data-nosnippet>202</a>            session_id: <span class="prelude-val">Some</span>(<span class="self">self</span>.id),
<a href=#203 id=203 data-nosnippet>203</a>            message: message.to_string(),
<a href=#204 id=204 data-nosnippet>204</a>            details: <span class="prelude-val">None</span>,
<a href=#205 id=205 data-nosnippet>205</a>        };
<a href=#206 id=206 data-nosnippet>206</a>        <span class="self">self</span>.logger.log(entry)
<a href=#207 id=207 data-nosnippet>207</a>    }
<a href=#208 id=208 data-nosnippet>208</a>
<a href=#209 id=209 data-nosnippet>209</a>    <span class="kw">async fn </span>log_error(<span class="kw-2">&amp;</span><span class="self">self</span>, message: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#210 id=210 data-nosnippet>210</a>        <span class="kw">let </span>entry = LogEntry {
<a href=#211 id=211 data-nosnippet>211</a>            id: Uuid::new_v4(),
<a href=#212 id=212 data-nosnippet>212</a>            timestamp: chrono::Utc::now(),
<a href=#213 id=213 data-nosnippet>213</a>            level: LogLevel::Error,
<a href=#214 id=214 data-nosnippet>214</a>            category: LogCategory::Error,
<a href=#215 id=215 data-nosnippet>215</a>            user_id: <span class="prelude-val">Some</span>(<span class="string">"agent"</span>.to_string()),
<a href=#216 id=216 data-nosnippet>216</a>            session_id: <span class="prelude-val">Some</span>(<span class="self">self</span>.id),
<a href=#217 id=217 data-nosnippet>217</a>            message: message.to_string(),
<a href=#218 id=218 data-nosnippet>218</a>            details: <span class="prelude-val">None</span>,
<a href=#219 id=219 data-nosnippet>219</a>        };
<a href=#220 id=220 data-nosnippet>220</a>        <span class="self">self</span>.logger.log(entry)
<a href=#221 id=221 data-nosnippet>221</a>    }
<a href=#222 id=222 data-nosnippet>222</a>
<a href=#223 id=223 data-nosnippet>223</a>    <span class="kw">pub fn </span>get_id(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Uuid {
<a href=#224 id=224 data-nosnippet>224</a>        <span class="self">self</span>.id
<a href=#225 id=225 data-nosnippet>225</a>    }
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>    <span class="kw">pub async fn </span>load_and_execute_elf(<span class="kw-2">&amp;mut </span><span class="self">self</span>, elf_data: <span class="kw-2">&amp;</span>[u8], args: <span class="kw-2">&amp;</span>[String]) -&gt; <span class="prelude-ty">Result</span>&lt;i32&gt; {
<a href=#228 id=228 data-nosnippet>228</a>        <span class="self">self</span>.log_info(<span class="string">"Loading and executing ELF file"</span>).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#229 id=229 data-nosnippet>229</a>        <span class="self">self</span>.elf_loader.load_and_execute(elf_data, args).<span class="kw">await
<a href=#230 id=230 data-nosnippet>230</a>    </span>}
<a href=#231 id=231 data-nosnippet>231</a>}</code></pre></div></section></main></body></html>