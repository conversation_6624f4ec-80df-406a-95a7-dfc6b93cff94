<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/system_info.rs`."><title>system_info.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>system_info.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>common::{DiskInfo, NetworkInterface, SystemInfoResult};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::collections::HashMap;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::fs;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tokio::process::Command;
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">pub struct </span>SystemInfoCollector;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">impl </span>SystemInfoCollector {
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#11 id=11 data-nosnippet>11</a>        <span class="self">Self
<a href=#12 id=12 data-nosnippet>12</a>    </span>}
<a href=#13 id=13 data-nosnippet>13</a>
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub async fn </span>collect(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;SystemInfoResult&gt; {
<a href=#15 id=15 data-nosnippet>15</a>        <span class="prelude-val">Ok</span>(SystemInfoResult {
<a href=#16 id=16 data-nosnippet>16</a>            hostname: <span class="self">self</span>.get_hostname().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#17 id=17 data-nosnippet>17</a>            os_version: <span class="self">self</span>.get_os_version().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#18 id=18 data-nosnippet>18</a>            kernel_version: <span class="self">self</span>.get_kernel_version().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#19 id=19 data-nosnippet>19</a>            architecture: <span class="self">self</span>.get_architecture().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#20 id=20 data-nosnippet>20</a>            cpu_info: <span class="self">self</span>.get_cpu_info().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#21 id=21 data-nosnippet>21</a>            memory_total: <span class="self">self</span>.get_memory_total().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#22 id=22 data-nosnippet>22</a>            memory_available: <span class="self">self</span>.get_memory_available().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#23 id=23 data-nosnippet>23</a>            disk_info: <span class="self">self</span>.get_disk_info().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#24 id=24 data-nosnippet>24</a>            network_interfaces: <span class="self">self</span>.get_network_interfaces().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#25 id=25 data-nosnippet>25</a>        })
<a href=#26 id=26 data-nosnippet>26</a>    }
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">async fn </span>get_hostname(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#29 id=29 data-nosnippet>29</a>        <span class="kw">let </span>output = Command::new(<span class="string">"hostname"</span>).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#30 id=30 data-nosnippet>30</a>        <span class="kw">if </span>output.status.success() {
<a href=#31 id=31 data-nosnippet>31</a>            <span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string())
<a href=#32 id=32 data-nosnippet>32</a>        } <span class="kw">else </span>{
<a href=#33 id=33 data-nosnippet>33</a>            <span class="comment">// 备用方法：读取 /etc/hostname
<a href=#34 id=34 data-nosnippet>34</a>            </span><span class="kw">match </span>fs::read_to_string(<span class="string">"/etc/hostname"</span>) {
<a href=#35 id=35 data-nosnippet>35</a>                <span class="prelude-val">Ok</span>(hostname) =&gt; <span class="prelude-val">Ok</span>(hostname.trim().to_string()),
<a href=#36 id=36 data-nosnippet>36</a>                <span class="prelude-val">Err</span>(<span class="kw">_</span>) =&gt; <span class="prelude-val">Ok</span>(<span class="string">"unknown"</span>.to_string()),
<a href=#37 id=37 data-nosnippet>37</a>            }
<a href=#38 id=38 data-nosnippet>38</a>        }
<a href=#39 id=39 data-nosnippet>39</a>    }
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">async fn </span>get_os_version(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#42 id=42 data-nosnippet>42</a>        <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#43 id=43 data-nosnippet>43</a>        </span>{
<a href=#44 id=44 data-nosnippet>44</a>            <span class="comment">// 尝试读取 /etc/os-release
<a href=#45 id=45 data-nosnippet>45</a>            </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(content) = fs::read_to_string(<span class="string">"/etc/os-release"</span>) {
<a href=#46 id=46 data-nosnippet>46</a>                <span class="kw">let </span><span class="kw-2">mut </span>name = String::new();
<a href=#47 id=47 data-nosnippet>47</a>                <span class="kw">let </span><span class="kw-2">mut </span>version = String::new();
<a href=#48 id=48 data-nosnippet>48</a>
<a href=#49 id=49 data-nosnippet>49</a>                <span class="kw">for </span>line <span class="kw">in </span>content.lines() {
<a href=#50 id=50 data-nosnippet>50</a>                    <span class="kw">if </span>line.starts_with(<span class="string">"NAME="</span>) {
<a href=#51 id=51 data-nosnippet>51</a>                        name = line.replace(<span class="string">"NAME="</span>, <span class="string">""</span>).trim_matches(<span class="string">'"'</span>).to_string();
<a href=#52 id=52 data-nosnippet>52</a>                    } <span class="kw">else if </span>line.starts_with(<span class="string">"VERSION="</span>) {
<a href=#53 id=53 data-nosnippet>53</a>                        version = line.replace(<span class="string">"VERSION="</span>, <span class="string">""</span>).trim_matches(<span class="string">'"'</span>).to_string();
<a href=#54 id=54 data-nosnippet>54</a>                    }
<a href=#55 id=55 data-nosnippet>55</a>                }
<a href=#56 id=56 data-nosnippet>56</a>
<a href=#57 id=57 data-nosnippet>57</a>                <span class="kw">if </span>!name.is_empty() {
<a href=#58 id=58 data-nosnippet>58</a>                    <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="macro">format!</span>(<span class="string">"{} {}"</span>, name, version));
<a href=#59 id=59 data-nosnippet>59</a>                }
<a href=#60 id=60 data-nosnippet>60</a>            }
<a href=#61 id=61 data-nosnippet>61</a>
<a href=#62 id=62 data-nosnippet>62</a>            <span class="comment">// 备用方法：使用 lsb_release
<a href=#63 id=63 data-nosnippet>63</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"lsb_release"</span>)
<a href=#64 id=64 data-nosnippet>64</a>                .args(<span class="kw-2">&amp;</span>[<span class="string">"-d"</span>, <span class="string">"-s"</span>])
<a href=#65 id=65 data-nosnippet>65</a>                .output()
<a href=#66 id=66 data-nosnippet>66</a>                .<span class="kw">await</span>;
<a href=#67 id=67 data-nosnippet>67</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(output) = output {
<a href=#68 id=68 data-nosnippet>68</a>                <span class="kw">if </span>output.status.success() {
<a href=#69 id=69 data-nosnippet>69</a>                    <span class="kw">return </span><span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string());
<a href=#70 id=70 data-nosnippet>70</a>                }
<a href=#71 id=71 data-nosnippet>71</a>            }
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>            <span class="prelude-val">Ok</span>(<span class="string">"Unknown Linux"</span>.to_string())
<a href=#74 id=74 data-nosnippet>74</a>        }
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>        <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#77 id=77 data-nosnippet>77</a>        </span>{
<a href=#78 id=78 data-nosnippet>78</a>            <span class="comment">// 使用 sw_vers 获取 macOS 版本信息
<a href=#79 id=79 data-nosnippet>79</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"sw_vers"</span>).output().<span class="kw">await</span>;
<a href=#80 id=80 data-nosnippet>80</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(output) = output {
<a href=#81 id=81 data-nosnippet>81</a>                <span class="kw">if </span>output.status.success() {
<a href=#82 id=82 data-nosnippet>82</a>                    <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#83 id=83 data-nosnippet>83</a>                    <span class="kw">let </span><span class="kw-2">mut </span>product_name = String::new();
<a href=#84 id=84 data-nosnippet>84</a>                    <span class="kw">let </span><span class="kw-2">mut </span>product_version = String::new();
<a href=#85 id=85 data-nosnippet>85</a>
<a href=#86 id=86 data-nosnippet>86</a>                    <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#87 id=87 data-nosnippet>87</a>                        <span class="kw">if </span>line.starts_with(<span class="string">"ProductName:"</span>) {
<a href=#88 id=88 data-nosnippet>88</a>                            product_name = line.replace(<span class="string">"ProductName:"</span>, <span class="string">""</span>).trim().to_string();
<a href=#89 id=89 data-nosnippet>89</a>                        } <span class="kw">else if </span>line.starts_with(<span class="string">"ProductVersion:"</span>) {
<a href=#90 id=90 data-nosnippet>90</a>                            product_version =
<a href=#91 id=91 data-nosnippet>91</a>                                line.replace(<span class="string">"ProductVersion:"</span>, <span class="string">""</span>).trim().to_string();
<a href=#92 id=92 data-nosnippet>92</a>                        }
<a href=#93 id=93 data-nosnippet>93</a>                    }
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a>                    <span class="kw">if </span>!product_name.is_empty() &amp;&amp; !product_version.is_empty() {
<a href=#96 id=96 data-nosnippet>96</a>                        <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="macro">format!</span>(<span class="string">"{} {}"</span>, product_name, product_version));
<a href=#97 id=97 data-nosnippet>97</a>                    }
<a href=#98 id=98 data-nosnippet>98</a>                }
<a href=#99 id=99 data-nosnippet>99</a>            }
<a href=#100 id=100 data-nosnippet>100</a>
<a href=#101 id=101 data-nosnippet>101</a>            <span class="prelude-val">Ok</span>(<span class="string">"macOS"</span>.to_string())
<a href=#102 id=102 data-nosnippet>102</a>        }
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a>        <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#105 id=105 data-nosnippet>105</a>        </span>{
<a href=#106 id=106 data-nosnippet>106</a>            <span class="prelude-val">Ok</span>(<span class="string">"Unknown OS"</span>.to_string())
<a href=#107 id=107 data-nosnippet>107</a>        }
<a href=#108 id=108 data-nosnippet>108</a>    }
<a href=#109 id=109 data-nosnippet>109</a>
<a href=#110 id=110 data-nosnippet>110</a>    <span class="kw">async fn </span>get_kernel_version(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#111 id=111 data-nosnippet>111</a>        <span class="kw">let </span>output = Command::new(<span class="string">"uname"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-r"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#112 id=112 data-nosnippet>112</a>        <span class="kw">if </span>output.status.success() {
<a href=#113 id=113 data-nosnippet>113</a>            <span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string())
<a href=#114 id=114 data-nosnippet>114</a>        } <span class="kw">else </span>{
<a href=#115 id=115 data-nosnippet>115</a>            <span class="prelude-val">Ok</span>(<span class="string">"unknown"</span>.to_string())
<a href=#116 id=116 data-nosnippet>116</a>        }
<a href=#117 id=117 data-nosnippet>117</a>    }
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">async fn </span>get_architecture(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#120 id=120 data-nosnippet>120</a>        <span class="kw">let </span>output = Command::new(<span class="string">"uname"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-m"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#121 id=121 data-nosnippet>121</a>        <span class="kw">if </span>output.status.success() {
<a href=#122 id=122 data-nosnippet>122</a>            <span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string())
<a href=#123 id=123 data-nosnippet>123</a>        } <span class="kw">else </span>{
<a href=#124 id=124 data-nosnippet>124</a>            <span class="prelude-val">Ok</span>(<span class="string">"unknown"</span>.to_string())
<a href=#125 id=125 data-nosnippet>125</a>        }
<a href=#126 id=126 data-nosnippet>126</a>    }
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>    <span class="kw">async fn </span>get_cpu_info(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#129 id=129 data-nosnippet>129</a>        <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#130 id=130 data-nosnippet>130</a>        </span>{
<a href=#131 id=131 data-nosnippet>131</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(content) = fs::read_to_string(<span class="string">"/proc/cpuinfo"</span>) {
<a href=#132 id=132 data-nosnippet>132</a>                <span class="kw">for </span>line <span class="kw">in </span>content.lines() {
<a href=#133 id=133 data-nosnippet>133</a>                    <span class="kw">if </span>line.starts_with(<span class="string">"model name"</span>) {
<a href=#134 id=134 data-nosnippet>134</a>                        <span class="kw">if let </span><span class="prelude-val">Some</span>(cpu_name) = line.split(<span class="string">':'</span>).nth(<span class="number">1</span>) {
<a href=#135 id=135 data-nosnippet>135</a>                            <span class="kw">return </span><span class="prelude-val">Ok</span>(cpu_name.trim().to_string());
<a href=#136 id=136 data-nosnippet>136</a>                        }
<a href=#137 id=137 data-nosnippet>137</a>                    }
<a href=#138 id=138 data-nosnippet>138</a>                }
<a href=#139 id=139 data-nosnippet>139</a>            }
<a href=#140 id=140 data-nosnippet>140</a>        }
<a href=#141 id=141 data-nosnippet>141</a>
<a href=#142 id=142 data-nosnippet>142</a>        <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#143 id=143 data-nosnippet>143</a>        </span>{
<a href=#144 id=144 data-nosnippet>144</a>            <span class="comment">// 使用 sysctl 获取 CPU 信息
<a href=#145 id=145 data-nosnippet>145</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"sysctl"</span>)
<a href=#146 id=146 data-nosnippet>146</a>                .args(<span class="kw-2">&amp;</span>[<span class="string">"-n"</span>, <span class="string">"machdep.cpu.brand_string"</span>])
<a href=#147 id=147 data-nosnippet>147</a>                .output()
<a href=#148 id=148 data-nosnippet>148</a>                .<span class="kw">await</span>;
<a href=#149 id=149 data-nosnippet>149</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(output) = output {
<a href=#150 id=150 data-nosnippet>150</a>                <span class="kw">if </span>output.status.success() {
<a href=#151 id=151 data-nosnippet>151</a>                    <span class="kw">return </span><span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string());
<a href=#152 id=152 data-nosnippet>152</a>                }
<a href=#153 id=153 data-nosnippet>153</a>            }
<a href=#154 id=154 data-nosnippet>154</a>        }
<a href=#155 id=155 data-nosnippet>155</a>
<a href=#156 id=156 data-nosnippet>156</a>        <span class="prelude-val">Ok</span>(<span class="string">"Unknown CPU"</span>.to_string())
<a href=#157 id=157 data-nosnippet>157</a>    }
<a href=#158 id=158 data-nosnippet>158</a>
<a href=#159 id=159 data-nosnippet>159</a>    <span class="kw">async fn </span>get_memory_total(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#160 id=160 data-nosnippet>160</a>        <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#161 id=161 data-nosnippet>161</a>        </span>{
<a href=#162 id=162 data-nosnippet>162</a>            <span class="self">self</span>.parse_meminfo(<span class="string">"MemTotal"</span>).<span class="kw">await
<a href=#163 id=163 data-nosnippet>163</a>        </span>}
<a href=#164 id=164 data-nosnippet>164</a>
<a href=#165 id=165 data-nosnippet>165</a>        <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#166 id=166 data-nosnippet>166</a>        </span>{
<a href=#167 id=167 data-nosnippet>167</a>            <span class="comment">// 使用 sysctl 获取内存信息
<a href=#168 id=168 data-nosnippet>168</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"sysctl"</span>)
<a href=#169 id=169 data-nosnippet>169</a>                .args(<span class="kw-2">&amp;</span>[<span class="string">"-n"</span>, <span class="string">"hw.memsize"</span>])
<a href=#170 id=170 data-nosnippet>170</a>                .output()
<a href=#171 id=171 data-nosnippet>171</a>                .<span class="kw">await</span>;
<a href=#172 id=172 data-nosnippet>172</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(output) = output {
<a href=#173 id=173 data-nosnippet>173</a>                <span class="kw">if </span>output.status.success() {
<a href=#174 id=174 data-nosnippet>174</a>                    <span class="kw">let </span>mem_str = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#175 id=175 data-nosnippet>175</a>                    <span class="kw">let </span>mem_str = mem_str.trim();
<a href=#176 id=176 data-nosnippet>176</a>                    <span class="kw">if let </span><span class="prelude-val">Ok</span>(mem_bytes) = mem_str.parse::&lt;u64&gt;() {
<a href=#177 id=177 data-nosnippet>177</a>                        <span class="kw">return </span><span class="prelude-val">Ok</span>(mem_bytes);
<a href=#178 id=178 data-nosnippet>178</a>                    }
<a href=#179 id=179 data-nosnippet>179</a>                }
<a href=#180 id=180 data-nosnippet>180</a>            }
<a href=#181 id=181 data-nosnippet>181</a>            <span class="prelude-val">Ok</span>(<span class="number">0</span>)
<a href=#182 id=182 data-nosnippet>182</a>        }
<a href=#183 id=183 data-nosnippet>183</a>
<a href=#184 id=184 data-nosnippet>184</a>        <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#185 id=185 data-nosnippet>185</a>        </span>{
<a href=#186 id=186 data-nosnippet>186</a>            <span class="prelude-val">Ok</span>(<span class="number">0</span>)
<a href=#187 id=187 data-nosnippet>187</a>        }
<a href=#188 id=188 data-nosnippet>188</a>    }
<a href=#189 id=189 data-nosnippet>189</a>
<a href=#190 id=190 data-nosnippet>190</a>    <span class="kw">async fn </span>get_memory_available(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#191 id=191 data-nosnippet>191</a>        <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#192 id=192 data-nosnippet>192</a>        </span>{
<a href=#193 id=193 data-nosnippet>193</a>            <span class="comment">// 尝试获取 MemAvailable，如果不存在则计算 MemFree + Buffers + Cached
<a href=#194 id=194 data-nosnippet>194</a>            </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(available) = <span class="self">self</span>.parse_meminfo(<span class="string">"MemAvailable"</span>).<span class="kw">await </span>{
<a href=#195 id=195 data-nosnippet>195</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(available);
<a href=#196 id=196 data-nosnippet>196</a>            }
<a href=#197 id=197 data-nosnippet>197</a>
<a href=#198 id=198 data-nosnippet>198</a>            <span class="kw">let </span>free = <span class="self">self</span>.parse_meminfo(<span class="string">"MemFree"</span>).<span class="kw">await</span>.unwrap_or(<span class="number">0</span>);
<a href=#199 id=199 data-nosnippet>199</a>            <span class="kw">let </span>buffers = <span class="self">self</span>.parse_meminfo(<span class="string">"Buffers"</span>).<span class="kw">await</span>.unwrap_or(<span class="number">0</span>);
<a href=#200 id=200 data-nosnippet>200</a>            <span class="kw">let </span>cached = <span class="self">self</span>.parse_meminfo(<span class="string">"Cached"</span>).<span class="kw">await</span>.unwrap_or(<span class="number">0</span>);
<a href=#201 id=201 data-nosnippet>201</a>
<a href=#202 id=202 data-nosnippet>202</a>            <span class="prelude-val">Ok</span>(free + buffers + cached)
<a href=#203 id=203 data-nosnippet>203</a>        }
<a href=#204 id=204 data-nosnippet>204</a>
<a href=#205 id=205 data-nosnippet>205</a>        <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#206 id=206 data-nosnippet>206</a>        </span>{
<a href=#207 id=207 data-nosnippet>207</a>            <span class="comment">// 使用 vm_stat 获取可用内存
<a href=#208 id=208 data-nosnippet>208</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"vm_stat"</span>).output().<span class="kw">await</span>;
<a href=#209 id=209 data-nosnippet>209</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(output) = output {
<a href=#210 id=210 data-nosnippet>210</a>                <span class="kw">if </span>output.status.success() {
<a href=#211 id=211 data-nosnippet>211</a>                    <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#212 id=212 data-nosnippet>212</a>                    <span class="kw">let </span><span class="kw-2">mut </span>free_pages = <span class="number">0u64</span>;
<a href=#213 id=213 data-nosnippet>213</a>                    <span class="kw">let </span><span class="kw-2">mut </span>inactive_pages = <span class="number">0u64</span>;
<a href=#214 id=214 data-nosnippet>214</a>
<a href=#215 id=215 data-nosnippet>215</a>                    <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#216 id=216 data-nosnippet>216</a>                        <span class="kw">if </span>line.starts_with(<span class="string">"Pages free:"</span>) {
<a href=#217 id=217 data-nosnippet>217</a>                            <span class="kw">if let </span><span class="prelude-val">Some</span>(pages_str) = line.split_whitespace().nth(<span class="number">2</span>) {
<a href=#218 id=218 data-nosnippet>218</a>                                free_pages = pages_str.trim_end_matches(<span class="string">'.'</span>).parse().unwrap_or(<span class="number">0</span>);
<a href=#219 id=219 data-nosnippet>219</a>                            }
<a href=#220 id=220 data-nosnippet>220</a>                        } <span class="kw">else if </span>line.starts_with(<span class="string">"Pages inactive:"</span>) {
<a href=#221 id=221 data-nosnippet>221</a>                            <span class="kw">if let </span><span class="prelude-val">Some</span>(pages_str) = line.split_whitespace().nth(<span class="number">2</span>) {
<a href=#222 id=222 data-nosnippet>222</a>                                inactive_pages =
<a href=#223 id=223 data-nosnippet>223</a>                                    pages_str.trim_end_matches(<span class="string">'.'</span>).parse().unwrap_or(<span class="number">0</span>);
<a href=#224 id=224 data-nosnippet>224</a>                            }
<a href=#225 id=225 data-nosnippet>225</a>                        }
<a href=#226 id=226 data-nosnippet>226</a>                    }
<a href=#227 id=227 data-nosnippet>227</a>
<a href=#228 id=228 data-nosnippet>228</a>                    <span class="comment">// macOS 页面大小通常是 4KB
<a href=#229 id=229 data-nosnippet>229</a>                    </span><span class="kw">return </span><span class="prelude-val">Ok</span>((free_pages + inactive_pages) * <span class="number">4096</span>);
<a href=#230 id=230 data-nosnippet>230</a>                }
<a href=#231 id=231 data-nosnippet>231</a>            }
<a href=#232 id=232 data-nosnippet>232</a>            <span class="prelude-val">Ok</span>(<span class="number">0</span>)
<a href=#233 id=233 data-nosnippet>233</a>        }
<a href=#234 id=234 data-nosnippet>234</a>
<a href=#235 id=235 data-nosnippet>235</a>        <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#236 id=236 data-nosnippet>236</a>        </span>{
<a href=#237 id=237 data-nosnippet>237</a>            <span class="prelude-val">Ok</span>(<span class="number">0</span>)
<a href=#238 id=238 data-nosnippet>238</a>        }
<a href=#239 id=239 data-nosnippet>239</a>    }
<a href=#240 id=240 data-nosnippet>240</a>
<a href=#241 id=241 data-nosnippet>241</a>    <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#242 id=242 data-nosnippet>242</a>    </span><span class="kw">async fn </span>parse_meminfo(<span class="kw-2">&amp;</span><span class="self">self</span>, field: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#243 id=243 data-nosnippet>243</a>        <span class="kw">let </span>content = fs::read_to_string(<span class="string">"/proc/meminfo"</span>)<span class="question-mark">?</span>;
<a href=#244 id=244 data-nosnippet>244</a>        <span class="kw">for </span>line <span class="kw">in </span>content.lines() {
<a href=#245 id=245 data-nosnippet>245</a>            <span class="kw">if </span>line.starts_with(field) {
<a href=#246 id=246 data-nosnippet>246</a>                <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#247 id=247 data-nosnippet>247</a>                <span class="kw">if </span>parts.len() &gt;= <span class="number">2 </span>{
<a href=#248 id=248 data-nosnippet>248</a>                    <span class="kw">if let </span><span class="prelude-val">Ok</span>(kb) = parts[<span class="number">1</span>].parse::&lt;u64&gt;() {
<a href=#249 id=249 data-nosnippet>249</a>                        <span class="kw">return </span><span class="prelude-val">Ok</span>(kb * <span class="number">1024</span>); <span class="comment">// 转换为字节
<a href=#250 id=250 data-nosnippet>250</a>                    </span>}
<a href=#251 id=251 data-nosnippet>251</a>                }
<a href=#252 id=252 data-nosnippet>252</a>            }
<a href=#253 id=253 data-nosnippet>253</a>        }
<a href=#254 id=254 data-nosnippet>254</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Field {} not found in /proc/meminfo"</span>, field))
<a href=#255 id=255 data-nosnippet>255</a>    }
<a href=#256 id=256 data-nosnippet>256</a>
<a href=#257 id=257 data-nosnippet>257</a>    <span class="kw">async fn </span>get_disk_info(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;DiskInfo&gt;&gt; {
<a href=#258 id=258 data-nosnippet>258</a>        <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#259 id=259 data-nosnippet>259</a>        </span>{
<a href=#260 id=260 data-nosnippet>260</a>            <span class="kw">let </span>output = Command::new(<span class="string">"df"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-h"</span>, <span class="string">"-T"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#261 id=261 data-nosnippet>261</a>            <span class="kw">if </span>!output.status.success() {
<a href=#262 id=262 data-nosnippet>262</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(Vec::new());
<a href=#263 id=263 data-nosnippet>263</a>            }
<a href=#264 id=264 data-nosnippet>264</a>
<a href=#265 id=265 data-nosnippet>265</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#266 id=266 data-nosnippet>266</a>            <span class="kw">let </span><span class="kw-2">mut </span>disks = Vec::new();
<a href=#267 id=267 data-nosnippet>267</a>
<a href=#268 id=268 data-nosnippet>268</a>            <span class="kw">for </span>line <span class="kw">in </span>stdout.lines().skip(<span class="number">1</span>) {
<a href=#269 id=269 data-nosnippet>269</a>                <span class="comment">// 跳过标题行
<a href=#270 id=270 data-nosnippet>270</a>                </span><span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#271 id=271 data-nosnippet>271</a>                <span class="kw">if </span>parts.len() &gt;= <span class="number">7 </span>{
<a href=#272 id=272 data-nosnippet>272</a>                    <span class="kw">let </span>device = parts[<span class="number">0</span>].to_string();
<a href=#273 id=273 data-nosnippet>273</a>                    <span class="kw">let </span>filesystem = parts[<span class="number">1</span>].to_string();
<a href=#274 id=274 data-nosnippet>274</a>                    <span class="kw">let </span>mount_point = parts[<span class="number">6</span>].to_string();
<a href=#275 id=275 data-nosnippet>275</a>
<a href=#276 id=276 data-nosnippet>276</a>                    <span class="comment">// 解析大小（需要处理K, M, G, T后缀）
<a href=#277 id=277 data-nosnippet>277</a>                    </span><span class="kw">let </span>total_space = <span class="self">self</span>.parse_size(parts[<span class="number">2</span>]).unwrap_or(<span class="number">0</span>);
<a href=#278 id=278 data-nosnippet>278</a>                    <span class="kw">let </span>used_space = <span class="self">self</span>.parse_size(parts[<span class="number">3</span>]).unwrap_or(<span class="number">0</span>);
<a href=#279 id=279 data-nosnippet>279</a>                    <span class="kw">let </span>available_space = total_space.saturating_sub(used_space);
<a href=#280 id=280 data-nosnippet>280</a>
<a href=#281 id=281 data-nosnippet>281</a>                    disks.push(DiskInfo {
<a href=#282 id=282 data-nosnippet>282</a>                        device,
<a href=#283 id=283 data-nosnippet>283</a>                        mount_point,
<a href=#284 id=284 data-nosnippet>284</a>                        total_space,
<a href=#285 id=285 data-nosnippet>285</a>                        available_space,
<a href=#286 id=286 data-nosnippet>286</a>                        filesystem,
<a href=#287 id=287 data-nosnippet>287</a>                    });
<a href=#288 id=288 data-nosnippet>288</a>                }
<a href=#289 id=289 data-nosnippet>289</a>            }
<a href=#290 id=290 data-nosnippet>290</a>
<a href=#291 id=291 data-nosnippet>291</a>            <span class="prelude-val">Ok</span>(disks)
<a href=#292 id=292 data-nosnippet>292</a>        }
<a href=#293 id=293 data-nosnippet>293</a>
<a href=#294 id=294 data-nosnippet>294</a>        <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#295 id=295 data-nosnippet>295</a>        </span>{
<a href=#296 id=296 data-nosnippet>296</a>            <span class="kw">let </span>output = Command::new(<span class="string">"df"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-h"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#297 id=297 data-nosnippet>297</a>            <span class="kw">if </span>!output.status.success() {
<a href=#298 id=298 data-nosnippet>298</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(Vec::new());
<a href=#299 id=299 data-nosnippet>299</a>            }
<a href=#300 id=300 data-nosnippet>300</a>
<a href=#301 id=301 data-nosnippet>301</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#302 id=302 data-nosnippet>302</a>            <span class="kw">let </span><span class="kw-2">mut </span>disks = Vec::new();
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>            <span class="kw">for </span>line <span class="kw">in </span>stdout.lines().skip(<span class="number">1</span>) {
<a href=#305 id=305 data-nosnippet>305</a>                <span class="comment">// 跳过标题行
<a href=#306 id=306 data-nosnippet>306</a>                </span><span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#307 id=307 data-nosnippet>307</a>                <span class="kw">if </span>parts.len() &gt;= <span class="number">6 </span>{
<a href=#308 id=308 data-nosnippet>308</a>                    <span class="kw">let </span>device = parts[<span class="number">0</span>].to_string();
<a href=#309 id=309 data-nosnippet>309</a>                    <span class="kw">let </span>mount_point = parts[<span class="number">5</span>].to_string();
<a href=#310 id=310 data-nosnippet>310</a>
<a href=#311 id=311 data-nosnippet>311</a>                    <span class="comment">// 解析大小（需要处理K, M, G, T后缀）
<a href=#312 id=312 data-nosnippet>312</a>                    </span><span class="kw">let </span>total_space = <span class="self">self</span>.parse_size(parts[<span class="number">1</span>]).unwrap_or(<span class="number">0</span>);
<a href=#313 id=313 data-nosnippet>313</a>                    <span class="kw">let </span>used_space = <span class="self">self</span>.parse_size(parts[<span class="number">2</span>]).unwrap_or(<span class="number">0</span>);
<a href=#314 id=314 data-nosnippet>314</a>                    <span class="kw">let </span>available_space = <span class="self">self</span>.parse_size(parts[<span class="number">3</span>]).unwrap_or(<span class="number">0</span>);
<a href=#315 id=315 data-nosnippet>315</a>
<a href=#316 id=316 data-nosnippet>316</a>                    disks.push(DiskInfo {
<a href=#317 id=317 data-nosnippet>317</a>                        device,
<a href=#318 id=318 data-nosnippet>318</a>                        mount_point,
<a href=#319 id=319 data-nosnippet>319</a>                        total_space,
<a href=#320 id=320 data-nosnippet>320</a>                        available_space,
<a href=#321 id=321 data-nosnippet>321</a>                        filesystem: <span class="string">"Unknown"</span>.to_string(), <span class="comment">// macOS df 不显示文件系统类型
<a href=#322 id=322 data-nosnippet>322</a>                    </span>});
<a href=#323 id=323 data-nosnippet>323</a>                }
<a href=#324 id=324 data-nosnippet>324</a>            }
<a href=#325 id=325 data-nosnippet>325</a>
<a href=#326 id=326 data-nosnippet>326</a>            <span class="prelude-val">Ok</span>(disks)
<a href=#327 id=327 data-nosnippet>327</a>        }
<a href=#328 id=328 data-nosnippet>328</a>
<a href=#329 id=329 data-nosnippet>329</a>        <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#330 id=330 data-nosnippet>330</a>        </span>{
<a href=#331 id=331 data-nosnippet>331</a>            <span class="prelude-val">Ok</span>(Vec::new())
<a href=#332 id=332 data-nosnippet>332</a>        }
<a href=#333 id=333 data-nosnippet>333</a>    }
<a href=#334 id=334 data-nosnippet>334</a>
<a href=#335 id=335 data-nosnippet>335</a>    <span class="kw">fn </span>parse_size(<span class="kw-2">&amp;</span><span class="self">self</span>, size_str: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;u64&gt; {
<a href=#336 id=336 data-nosnippet>336</a>        <span class="kw">if </span>size_str.is_empty() || size_str == <span class="string">"-" </span>{
<a href=#337 id=337 data-nosnippet>337</a>            <span class="kw">return </span><span class="prelude-val">None</span>;
<a href=#338 id=338 data-nosnippet>338</a>        }
<a href=#339 id=339 data-nosnippet>339</a>
<a href=#340 id=340 data-nosnippet>340</a>        <span class="kw">let </span>size_str = size_str.to_uppercase();
<a href=#341 id=341 data-nosnippet>341</a>        <span class="kw">let </span>(number_part, suffix) = <span class="kw">if </span>size_str.ends_with(<span class="string">'K'</span>) {
<a href=#342 id=342 data-nosnippet>342</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024</span>)
<a href=#343 id=343 data-nosnippet>343</a>        } <span class="kw">else if </span>size_str.ends_with(<span class="string">'M'</span>) {
<a href=#344 id=344 data-nosnippet>344</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024 </span>* <span class="number">1024</span>)
<a href=#345 id=345 data-nosnippet>345</a>        } <span class="kw">else if </span>size_str.ends_with(<span class="string">'G'</span>) {
<a href=#346 id=346 data-nosnippet>346</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024 </span>* <span class="number">1024 </span>* <span class="number">1024</span>)
<a href=#347 id=347 data-nosnippet>347</a>        } <span class="kw">else if </span>size_str.ends_with(<span class="string">'T'</span>) {
<a href=#348 id=348 data-nosnippet>348</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024_u64</span>.pow(<span class="number">4</span>))
<a href=#349 id=349 data-nosnippet>349</a>        } <span class="kw">else </span>{
<a href=#350 id=350 data-nosnippet>350</a>            (size_str.as_str(), <span class="number">1</span>)
<a href=#351 id=351 data-nosnippet>351</a>        };
<a href=#352 id=352 data-nosnippet>352</a>
<a href=#353 id=353 data-nosnippet>353</a>        number_part
<a href=#354 id=354 data-nosnippet>354</a>            .parse::&lt;f64&gt;()
<a href=#355 id=355 data-nosnippet>355</a>            .ok()
<a href=#356 id=356 data-nosnippet>356</a>            .map(|n| (n * suffix <span class="kw">as </span>f64) <span class="kw">as </span>u64)
<a href=#357 id=357 data-nosnippet>357</a>    }
<a href=#358 id=358 data-nosnippet>358</a>
<a href=#359 id=359 data-nosnippet>359</a>    <span class="kw">async fn </span>get_network_interfaces(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;NetworkInterface&gt;&gt; {
<a href=#360 id=360 data-nosnippet>360</a>        <span class="attr">#[cfg(target_os = <span class="string">"linux"</span>)]
<a href=#361 id=361 data-nosnippet>361</a>        </span>{
<a href=#362 id=362 data-nosnippet>362</a>            <span class="kw">let </span><span class="kw-2">mut </span>interfaces = Vec::new();
<a href=#363 id=363 data-nosnippet>363</a>
<a href=#364 id=364 data-nosnippet>364</a>            <span class="comment">// 获取接口列表
<a href=#365 id=365 data-nosnippet>365</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"ip"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"link"</span>, <span class="string">"show"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#366 id=366 data-nosnippet>366</a>            <span class="kw">if </span>!output.status.success() {
<a href=#367 id=367 data-nosnippet>367</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(interfaces);
<a href=#368 id=368 data-nosnippet>368</a>            }
<a href=#369 id=369 data-nosnippet>369</a>
<a href=#370 id=370 data-nosnippet>370</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#371 id=371 data-nosnippet>371</a>            <span class="kw">let </span><span class="kw-2">mut </span>current_interface: <span class="prelude-ty">Option</span>&lt;String&gt; = <span class="prelude-val">None</span>;
<a href=#372 id=372 data-nosnippet>372</a>
<a href=#373 id=373 data-nosnippet>373</a>            <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#374 id=374 data-nosnippet>374</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(interface_name) = <span class="self">self</span>.parse_interface_line(line) {
<a href=#375 id=375 data-nosnippet>375</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(name) = current_interface.take() {
<a href=#376 id=376 data-nosnippet>376</a>                        <span class="comment">// 处理前一个接口
<a href=#377 id=377 data-nosnippet>377</a>                        </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(interface) = <span class="self">self</span>.get_interface_details(<span class="kw-2">&amp;</span>name).<span class="kw">await </span>{
<a href=#378 id=378 data-nosnippet>378</a>                            interfaces.push(interface);
<a href=#379 id=379 data-nosnippet>379</a>                        }
<a href=#380 id=380 data-nosnippet>380</a>                    }
<a href=#381 id=381 data-nosnippet>381</a>                    current_interface = <span class="prelude-val">Some</span>(interface_name);
<a href=#382 id=382 data-nosnippet>382</a>                }
<a href=#383 id=383 data-nosnippet>383</a>            }
<a href=#384 id=384 data-nosnippet>384</a>
<a href=#385 id=385 data-nosnippet>385</a>            <span class="comment">// 处理最后一个接口
<a href=#386 id=386 data-nosnippet>386</a>            </span><span class="kw">if let </span><span class="prelude-val">Some</span>(name) = current_interface {
<a href=#387 id=387 data-nosnippet>387</a>                <span class="kw">if let </span><span class="prelude-val">Ok</span>(interface) = <span class="self">self</span>.get_interface_details(<span class="kw-2">&amp;</span>name).<span class="kw">await </span>{
<a href=#388 id=388 data-nosnippet>388</a>                    interfaces.push(interface);
<a href=#389 id=389 data-nosnippet>389</a>                }
<a href=#390 id=390 data-nosnippet>390</a>            }
<a href=#391 id=391 data-nosnippet>391</a>
<a href=#392 id=392 data-nosnippet>392</a>            <span class="prelude-val">Ok</span>(interfaces)
<a href=#393 id=393 data-nosnippet>393</a>        }
<a href=#394 id=394 data-nosnippet>394</a>
<a href=#395 id=395 data-nosnippet>395</a>        <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#396 id=396 data-nosnippet>396</a>        </span>{
<a href=#397 id=397 data-nosnippet>397</a>            <span class="kw">let </span><span class="kw-2">mut </span>interfaces = Vec::new();
<a href=#398 id=398 data-nosnippet>398</a>
<a href=#399 id=399 data-nosnippet>399</a>            <span class="comment">// 使用 ifconfig 获取接口信息
<a href=#400 id=400 data-nosnippet>400</a>            </span><span class="kw">let </span>output = Command::new(<span class="string">"ifconfig"</span>).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#401 id=401 data-nosnippet>401</a>            <span class="kw">if </span>!output.status.success() {
<a href=#402 id=402 data-nosnippet>402</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(interfaces);
<a href=#403 id=403 data-nosnippet>403</a>            }
<a href=#404 id=404 data-nosnippet>404</a>
<a href=#405 id=405 data-nosnippet>405</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#406 id=406 data-nosnippet>406</a>            <span class="kw">let </span><span class="kw-2">mut </span>current_interface: <span class="prelude-ty">Option</span>&lt;String&gt; = <span class="prelude-val">None</span>;
<a href=#407 id=407 data-nosnippet>407</a>            <span class="kw">let </span><span class="kw-2">mut </span>current_ip_addresses = Vec::new();
<a href=#408 id=408 data-nosnippet>408</a>            <span class="kw">let </span><span class="kw-2">mut </span>current_mac_address = String::new();
<a href=#409 id=409 data-nosnippet>409</a>            <span class="kw">let </span><span class="kw-2">mut </span>current_is_up = <span class="bool-val">false</span>;
<a href=#410 id=410 data-nosnippet>410</a>
<a href=#411 id=411 data-nosnippet>411</a>            <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#412 id=412 data-nosnippet>412</a>                <span class="kw">if </span>!line.starts_with(<span class="string">'\t'</span>) &amp;&amp; !line.starts_with(<span class="string">' '</span>) &amp;&amp; line.contains(<span class="string">':'</span>) {
<a href=#413 id=413 data-nosnippet>413</a>                    <span class="comment">// 新接口开始
<a href=#414 id=414 data-nosnippet>414</a>                    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(name) = current_interface.take() {
<a href=#415 id=415 data-nosnippet>415</a>                        <span class="kw">if </span>name != <span class="string">"lo0" </span>{
<a href=#416 id=416 data-nosnippet>416</a>                            <span class="comment">// 跳过回环接口
<a href=#417 id=417 data-nosnippet>417</a>                            </span>interfaces.push(NetworkInterface {
<a href=#418 id=418 data-nosnippet>418</a>                                name,
<a href=#419 id=419 data-nosnippet>419</a>                                ip_addresses: current_ip_addresses.clone(),
<a href=#420 id=420 data-nosnippet>420</a>                                mac_address: current_mac_address.clone(),
<a href=#421 id=421 data-nosnippet>421</a>                                is_up: current_is_up,
<a href=#422 id=422 data-nosnippet>422</a>                            });
<a href=#423 id=423 data-nosnippet>423</a>                        }
<a href=#424 id=424 data-nosnippet>424</a>                    }
<a href=#425 id=425 data-nosnippet>425</a>
<a href=#426 id=426 data-nosnippet>426</a>                    <span class="comment">// 解析接口名称
<a href=#427 id=427 data-nosnippet>427</a>                    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(interface_name) = line.split(<span class="string">':'</span>).next() {
<a href=#428 id=428 data-nosnippet>428</a>                        current_interface = <span class="prelude-val">Some</span>(interface_name.to_string());
<a href=#429 id=429 data-nosnippet>429</a>                        current_ip_addresses.clear();
<a href=#430 id=430 data-nosnippet>430</a>                        current_mac_address.clear();
<a href=#431 id=431 data-nosnippet>431</a>                        current_is_up = line.contains(<span class="string">"UP"</span>);
<a href=#432 id=432 data-nosnippet>432</a>                    }
<a href=#433 id=433 data-nosnippet>433</a>                } <span class="kw">else if </span>line.contains(<span class="string">"inet "</span>) &amp;&amp; !line.contains(<span class="string">"127.0.0.1"</span>) {
<a href=#434 id=434 data-nosnippet>434</a>                    <span class="comment">// 解析 IP 地址
<a href=#435 id=435 data-nosnippet>435</a>                    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(ip) = <span class="self">self</span>.extract_ip_from_ifconfig_line(line) {
<a href=#436 id=436 data-nosnippet>436</a>                        current_ip_addresses.push(ip);
<a href=#437 id=437 data-nosnippet>437</a>                    }
<a href=#438 id=438 data-nosnippet>438</a>                } <span class="kw">else if </span>line.contains(<span class="string">"ether "</span>) {
<a href=#439 id=439 data-nosnippet>439</a>                    <span class="comment">// 解析 MAC 地址
<a href=#440 id=440 data-nosnippet>440</a>                    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(mac) = <span class="self">self</span>.extract_mac_from_ifconfig_line(line) {
<a href=#441 id=441 data-nosnippet>441</a>                        current_mac_address = mac;
<a href=#442 id=442 data-nosnippet>442</a>                    }
<a href=#443 id=443 data-nosnippet>443</a>                }
<a href=#444 id=444 data-nosnippet>444</a>            }
<a href=#445 id=445 data-nosnippet>445</a>
<a href=#446 id=446 data-nosnippet>446</a>            <span class="comment">// 处理最后一个接口
<a href=#447 id=447 data-nosnippet>447</a>            </span><span class="kw">if let </span><span class="prelude-val">Some</span>(name) = current_interface {
<a href=#448 id=448 data-nosnippet>448</a>                <span class="kw">if </span>name != <span class="string">"lo0" </span>{
<a href=#449 id=449 data-nosnippet>449</a>                    interfaces.push(NetworkInterface {
<a href=#450 id=450 data-nosnippet>450</a>                        name,
<a href=#451 id=451 data-nosnippet>451</a>                        ip_addresses: current_ip_addresses,
<a href=#452 id=452 data-nosnippet>452</a>                        mac_address: current_mac_address,
<a href=#453 id=453 data-nosnippet>453</a>                        is_up: current_is_up,
<a href=#454 id=454 data-nosnippet>454</a>                    });
<a href=#455 id=455 data-nosnippet>455</a>                }
<a href=#456 id=456 data-nosnippet>456</a>            }
<a href=#457 id=457 data-nosnippet>457</a>
<a href=#458 id=458 data-nosnippet>458</a>            <span class="prelude-val">Ok</span>(interfaces)
<a href=#459 id=459 data-nosnippet>459</a>        }
<a href=#460 id=460 data-nosnippet>460</a>
<a href=#461 id=461 data-nosnippet>461</a>        <span class="attr">#[cfg(not(any(target_os = <span class="string">"linux"</span>, target_os = <span class="string">"macos"</span>)))]
<a href=#462 id=462 data-nosnippet>462</a>        </span>{
<a href=#463 id=463 data-nosnippet>463</a>            <span class="prelude-val">Ok</span>(Vec::new())
<a href=#464 id=464 data-nosnippet>464</a>        }
<a href=#465 id=465 data-nosnippet>465</a>    }
<a href=#466 id=466 data-nosnippet>466</a>
<a href=#467 id=467 data-nosnippet>467</a>    <span class="kw">fn </span>parse_interface_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#468 id=468 data-nosnippet>468</a>        <span class="kw">if </span>line.starts_with(char::is_numeric) {
<a href=#469 id=469 data-nosnippet>469</a>            <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#470 id=470 data-nosnippet>470</a>            <span class="kw">if </span>parts.len() &gt;= <span class="number">2 </span>{
<a href=#471 id=471 data-nosnippet>471</a>                <span class="kw">let </span>name = parts[<span class="number">1</span>].trim_end_matches(<span class="string">':'</span>);
<a href=#472 id=472 data-nosnippet>472</a>                <span class="kw">if </span>name != <span class="string">"lo" </span>{
<a href=#473 id=473 data-nosnippet>473</a>                    <span class="comment">// 跳过回环接口
<a href=#474 id=474 data-nosnippet>474</a>                    </span><span class="kw">return </span><span class="prelude-val">Some</span>(name.to_string());
<a href=#475 id=475 data-nosnippet>475</a>                }
<a href=#476 id=476 data-nosnippet>476</a>            }
<a href=#477 id=477 data-nosnippet>477</a>        }
<a href=#478 id=478 data-nosnippet>478</a>        <span class="prelude-val">None
<a href=#479 id=479 data-nosnippet>479</a>    </span>}
<a href=#480 id=480 data-nosnippet>480</a>
<a href=#481 id=481 data-nosnippet>481</a>    <span class="kw">async fn </span>get_interface_details(<span class="kw-2">&amp;</span><span class="self">self</span>, interface_name: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;NetworkInterface&gt; {
<a href=#482 id=482 data-nosnippet>482</a>        <span class="kw">let </span><span class="kw-2">mut </span>ip_addresses = Vec::new();
<a href=#483 id=483 data-nosnippet>483</a>        <span class="kw">let </span><span class="kw-2">mut </span>mac_address = String::new();
<a href=#484 id=484 data-nosnippet>484</a>        <span class="kw">let </span><span class="kw-2">mut </span>is_up = <span class="bool-val">false</span>;
<a href=#485 id=485 data-nosnippet>485</a>
<a href=#486 id=486 data-nosnippet>486</a>        <span class="comment">// 获取IP地址
<a href=#487 id=487 data-nosnippet>487</a>        </span><span class="kw">let </span>output = Command::new(<span class="string">"ip"</span>)
<a href=#488 id=488 data-nosnippet>488</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"addr"</span>, <span class="string">"show"</span>, interface_name])
<a href=#489 id=489 data-nosnippet>489</a>            .output()
<a href=#490 id=490 data-nosnippet>490</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#491 id=491 data-nosnippet>491</a>
<a href=#492 id=492 data-nosnippet>492</a>        <span class="kw">if </span>output.status.success() {
<a href=#493 id=493 data-nosnippet>493</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#494 id=494 data-nosnippet>494</a>            <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#495 id=495 data-nosnippet>495</a>                <span class="kw">if </span>line.contains(<span class="string">"inet "</span>) &amp;&amp; !line.contains(<span class="string">"127.0.0.1"</span>) {
<a href=#496 id=496 data-nosnippet>496</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(ip) = <span class="self">self</span>.extract_ip_from_line(line) {
<a href=#497 id=497 data-nosnippet>497</a>                        ip_addresses.push(ip);
<a href=#498 id=498 data-nosnippet>498</a>                    }
<a href=#499 id=499 data-nosnippet>499</a>                } <span class="kw">else if </span>line.contains(<span class="string">"link/ether"</span>) {
<a href=#500 id=500 data-nosnippet>500</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(mac) = <span class="self">self</span>.extract_mac_from_line(line) {
<a href=#501 id=501 data-nosnippet>501</a>                        mac_address = mac;
<a href=#502 id=502 data-nosnippet>502</a>                    }
<a href=#503 id=503 data-nosnippet>503</a>                } <span class="kw">else if </span>line.contains(<span class="string">"state UP"</span>) {
<a href=#504 id=504 data-nosnippet>504</a>                    is_up = <span class="bool-val">true</span>;
<a href=#505 id=505 data-nosnippet>505</a>                }
<a href=#506 id=506 data-nosnippet>506</a>            }
<a href=#507 id=507 data-nosnippet>507</a>        }
<a href=#508 id=508 data-nosnippet>508</a>
<a href=#509 id=509 data-nosnippet>509</a>        <span class="prelude-val">Ok</span>(NetworkInterface {
<a href=#510 id=510 data-nosnippet>510</a>            name: interface_name.to_string(),
<a href=#511 id=511 data-nosnippet>511</a>            ip_addresses,
<a href=#512 id=512 data-nosnippet>512</a>            mac_address,
<a href=#513 id=513 data-nosnippet>513</a>            is_up,
<a href=#514 id=514 data-nosnippet>514</a>        })
<a href=#515 id=515 data-nosnippet>515</a>    }
<a href=#516 id=516 data-nosnippet>516</a>
<a href=#517 id=517 data-nosnippet>517</a>    <span class="kw">fn </span>extract_ip_from_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#518 id=518 data-nosnippet>518</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#519 id=519 data-nosnippet>519</a>        <span class="kw">for </span>part <span class="kw">in </span>parts {
<a href=#520 id=520 data-nosnippet>520</a>            <span class="kw">if </span>part.contains(<span class="string">'/'</span>) &amp;&amp; part.contains(<span class="string">'.'</span>) {
<a href=#521 id=521 data-nosnippet>521</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(ip) = part.split(<span class="string">'/'</span>).next() {
<a href=#522 id=522 data-nosnippet>522</a>                    <span class="kw">return </span><span class="prelude-val">Some</span>(ip.to_string());
<a href=#523 id=523 data-nosnippet>523</a>                }
<a href=#524 id=524 data-nosnippet>524</a>            }
<a href=#525 id=525 data-nosnippet>525</a>        }
<a href=#526 id=526 data-nosnippet>526</a>        <span class="prelude-val">None
<a href=#527 id=527 data-nosnippet>527</a>    </span>}
<a href=#528 id=528 data-nosnippet>528</a>
<a href=#529 id=529 data-nosnippet>529</a>    <span class="kw">fn </span>extract_mac_from_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#530 id=530 data-nosnippet>530</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#531 id=531 data-nosnippet>531</a>        <span class="kw">for </span>part <span class="kw">in </span>parts {
<a href=#532 id=532 data-nosnippet>532</a>            <span class="kw">if </span>part.matches(<span class="string">':'</span>).count() == <span class="number">5 </span>&amp;&amp; part.len() == <span class="number">17 </span>{
<a href=#533 id=533 data-nosnippet>533</a>                <span class="kw">return </span><span class="prelude-val">Some</span>(part.to_string());
<a href=#534 id=534 data-nosnippet>534</a>            }
<a href=#535 id=535 data-nosnippet>535</a>        }
<a href=#536 id=536 data-nosnippet>536</a>        <span class="prelude-val">None
<a href=#537 id=537 data-nosnippet>537</a>    </span>}
<a href=#538 id=538 data-nosnippet>538</a>
<a href=#539 id=539 data-nosnippet>539</a>    <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#540 id=540 data-nosnippet>540</a>    </span><span class="kw">fn </span>extract_ip_from_ifconfig_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#541 id=541 data-nosnippet>541</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#542 id=542 data-nosnippet>542</a>        <span class="kw">for </span>(i, part) <span class="kw">in </span>parts.iter().enumerate() {
<a href=#543 id=543 data-nosnippet>543</a>            <span class="kw">if </span><span class="kw-2">*</span>part == <span class="string">"inet" </span>&amp;&amp; i + <span class="number">1 </span>&lt; parts.len() {
<a href=#544 id=544 data-nosnippet>544</a>                <span class="kw">return </span><span class="prelude-val">Some</span>(parts[i + <span class="number">1</span>].to_string());
<a href=#545 id=545 data-nosnippet>545</a>            }
<a href=#546 id=546 data-nosnippet>546</a>        }
<a href=#547 id=547 data-nosnippet>547</a>        <span class="prelude-val">None
<a href=#548 id=548 data-nosnippet>548</a>    </span>}
<a href=#549 id=549 data-nosnippet>549</a>
<a href=#550 id=550 data-nosnippet>550</a>    <span class="attr">#[cfg(target_os = <span class="string">"macos"</span>)]
<a href=#551 id=551 data-nosnippet>551</a>    </span><span class="kw">fn </span>extract_mac_from_ifconfig_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#552 id=552 data-nosnippet>552</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#553 id=553 data-nosnippet>553</a>        <span class="kw">for </span>(i, part) <span class="kw">in </span>parts.iter().enumerate() {
<a href=#554 id=554 data-nosnippet>554</a>            <span class="kw">if </span><span class="kw-2">*</span>part == <span class="string">"ether" </span>&amp;&amp; i + <span class="number">1 </span>&lt; parts.len() {
<a href=#555 id=555 data-nosnippet>555</a>                <span class="kw">let </span>mac = parts[i + <span class="number">1</span>];
<a href=#556 id=556 data-nosnippet>556</a>                <span class="kw">if </span>mac.matches(<span class="string">':'</span>).count() == <span class="number">5 </span>&amp;&amp; mac.len() == <span class="number">17 </span>{
<a href=#557 id=557 data-nosnippet>557</a>                    <span class="kw">return </span><span class="prelude-val">Some</span>(mac.to_string());
<a href=#558 id=558 data-nosnippet>558</a>                }
<a href=#559 id=559 data-nosnippet>559</a>            }
<a href=#560 id=560 data-nosnippet>560</a>        }
<a href=#561 id=561 data-nosnippet>561</a>        <span class="prelude-val">None
<a href=#562 id=562 data-nosnippet>562</a>    </span>}
<a href=#563 id=563 data-nosnippet>563</a>}
<a href=#564 id=564 data-nosnippet>564</a>
<a href=#565 id=565 data-nosnippet>565</a><span class="attr">#[cfg(test)]
<a href=#566 id=566 data-nosnippet>566</a></span><span class="kw">mod </span>tests {
<a href=#567 id=567 data-nosnippet>567</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#568 id=568 data-nosnippet>568</a>
<a href=#569 id=569 data-nosnippet>569</a>    <span class="attr">#[tokio::test]
<a href=#570 id=570 data-nosnippet>570</a>    </span><span class="kw">async fn </span>test_system_info_collection() {
<a href=#571 id=571 data-nosnippet>571</a>        <span class="kw">let </span>collector = SystemInfoCollector::new();
<a href=#572 id=572 data-nosnippet>572</a>        <span class="kw">let </span>info = collector.collect().<span class="kw">await</span>.unwrap();
<a href=#573 id=573 data-nosnippet>573</a>
<a href=#574 id=574 data-nosnippet>574</a>        <span class="macro">assert!</span>(!info.hostname.is_empty());
<a href=#575 id=575 data-nosnippet>575</a>        <span class="macro">assert!</span>(!info.os_version.is_empty());
<a href=#576 id=576 data-nosnippet>576</a>        <span class="macro">assert!</span>(info.memory_total &gt; <span class="number">0</span>);
<a href=#577 id=577 data-nosnippet>577</a>    }
<a href=#578 id=578 data-nosnippet>578</a>
<a href=#579 id=579 data-nosnippet>579</a>    <span class="attr">#[test]
<a href=#580 id=580 data-nosnippet>580</a>    </span><span class="kw">fn </span>test_size_parsing() {
<a href=#581 id=581 data-nosnippet>581</a>        <span class="kw">let </span>collector = SystemInfoCollector::new();
<a href=#582 id=582 data-nosnippet>582</a>
<a href=#583 id=583 data-nosnippet>583</a>        <span class="macro">assert_eq!</span>(collector.parse_size(<span class="string">"1K"</span>), <span class="prelude-val">Some</span>(<span class="number">1024</span>));
<a href=#584 id=584 data-nosnippet>584</a>        <span class="macro">assert_eq!</span>(collector.parse_size(<span class="string">"1M"</span>), <span class="prelude-val">Some</span>(<span class="number">1024 </span>* <span class="number">1024</span>));
<a href=#585 id=585 data-nosnippet>585</a>        <span class="macro">assert_eq!</span>(collector.parse_size(<span class="string">"1G"</span>), <span class="prelude-val">Some</span>(<span class="number">1024 </span>* <span class="number">1024 </span>* <span class="number">1024</span>));
<a href=#586 id=586 data-nosnippet>586</a>        <span class="macro">assert_eq!</span>(
<a href=#587 id=587 data-nosnippet>587</a>            collector.parse_size(<span class="string">"1.5G"</span>),
<a href=#588 id=588 data-nosnippet>588</a>            <span class="prelude-val">Some</span>((<span class="number">1.5 </span>* <span class="number">1024.0 </span>* <span class="number">1024.0 </span>* <span class="number">1024.0</span>) <span class="kw">as </span>u64)
<a href=#589 id=589 data-nosnippet>589</a>        );
<a href=#590 id=590 data-nosnippet>590</a>    }
<a href=#591 id=591 data-nosnippet>591</a>}</code></pre></div></section></main></body></html>