<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `agent/src/system_info.rs`."><title>system_info.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">agent/</div>system_info.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>common::{DiskInfo, NetworkInterface, SystemInfoResult};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::collections::HashMap;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::fs;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tokio::process::Command;
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">pub struct </span>SystemInfoCollector;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">impl </span>SystemInfoCollector {
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#11 id=11 data-nosnippet>11</a>        <span class="self">Self
<a href=#12 id=12 data-nosnippet>12</a>    </span>}
<a href=#13 id=13 data-nosnippet>13</a>
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub async fn </span>collect(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;SystemInfoResult&gt; {
<a href=#15 id=15 data-nosnippet>15</a>        <span class="prelude-val">Ok</span>(SystemInfoResult {
<a href=#16 id=16 data-nosnippet>16</a>            hostname: <span class="self">self</span>.get_hostname().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#17 id=17 data-nosnippet>17</a>            os_version: <span class="self">self</span>.get_os_version().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#18 id=18 data-nosnippet>18</a>            kernel_version: <span class="self">self</span>.get_kernel_version().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#19 id=19 data-nosnippet>19</a>            architecture: <span class="self">self</span>.get_architecture().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#20 id=20 data-nosnippet>20</a>            cpu_info: <span class="self">self</span>.get_cpu_info().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#21 id=21 data-nosnippet>21</a>            memory_total: <span class="self">self</span>.get_memory_total().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#22 id=22 data-nosnippet>22</a>            memory_available: <span class="self">self</span>.get_memory_available().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#23 id=23 data-nosnippet>23</a>            disk_info: <span class="self">self</span>.get_disk_info().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#24 id=24 data-nosnippet>24</a>            network_interfaces: <span class="self">self</span>.get_network_interfaces().<span class="kw">await</span><span class="question-mark">?</span>,
<a href=#25 id=25 data-nosnippet>25</a>        })
<a href=#26 id=26 data-nosnippet>26</a>    }
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">async fn </span>get_hostname(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#29 id=29 data-nosnippet>29</a>        <span class="kw">let </span>output = Command::new(<span class="string">"hostname"</span>).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#30 id=30 data-nosnippet>30</a>        <span class="kw">if </span>output.status.success() {
<a href=#31 id=31 data-nosnippet>31</a>            <span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string())
<a href=#32 id=32 data-nosnippet>32</a>        } <span class="kw">else </span>{
<a href=#33 id=33 data-nosnippet>33</a>            <span class="comment">// 备用方法：读取 /etc/hostname
<a href=#34 id=34 data-nosnippet>34</a>            </span><span class="kw">match </span>fs::read_to_string(<span class="string">"/etc/hostname"</span>) {
<a href=#35 id=35 data-nosnippet>35</a>                <span class="prelude-val">Ok</span>(hostname) =&gt; <span class="prelude-val">Ok</span>(hostname.trim().to_string()),
<a href=#36 id=36 data-nosnippet>36</a>                <span class="prelude-val">Err</span>(<span class="kw">_</span>) =&gt; <span class="prelude-val">Ok</span>(<span class="string">"unknown"</span>.to_string()),
<a href=#37 id=37 data-nosnippet>37</a>            }
<a href=#38 id=38 data-nosnippet>38</a>        }
<a href=#39 id=39 data-nosnippet>39</a>    }
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">async fn </span>get_os_version(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#42 id=42 data-nosnippet>42</a>        <span class="comment">// 尝试读取 /etc/os-release
<a href=#43 id=43 data-nosnippet>43</a>        </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(content) = fs::read_to_string(<span class="string">"/etc/os-release"</span>) {
<a href=#44 id=44 data-nosnippet>44</a>            <span class="kw">let </span><span class="kw-2">mut </span>name = String::new();
<a href=#45 id=45 data-nosnippet>45</a>            <span class="kw">let </span><span class="kw-2">mut </span>version = String::new();
<a href=#46 id=46 data-nosnippet>46</a>
<a href=#47 id=47 data-nosnippet>47</a>            <span class="kw">for </span>line <span class="kw">in </span>content.lines() {
<a href=#48 id=48 data-nosnippet>48</a>                <span class="kw">if </span>line.starts_with(<span class="string">"NAME="</span>) {
<a href=#49 id=49 data-nosnippet>49</a>                    name = line.replace(<span class="string">"NAME="</span>, <span class="string">""</span>).trim_matches(<span class="string">'"'</span>).to_string();
<a href=#50 id=50 data-nosnippet>50</a>                } <span class="kw">else if </span>line.starts_with(<span class="string">"VERSION="</span>) {
<a href=#51 id=51 data-nosnippet>51</a>                    version = line.replace(<span class="string">"VERSION="</span>, <span class="string">""</span>).trim_matches(<span class="string">'"'</span>).to_string();
<a href=#52 id=52 data-nosnippet>52</a>                }
<a href=#53 id=53 data-nosnippet>53</a>            }
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a>            <span class="kw">if </span>!name.is_empty() {
<a href=#56 id=56 data-nosnippet>56</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="macro">format!</span>(<span class="string">"{} {}"</span>, name, version));
<a href=#57 id=57 data-nosnippet>57</a>            }
<a href=#58 id=58 data-nosnippet>58</a>        }
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a>        <span class="comment">// 备用方法：使用 lsb_release
<a href=#61 id=61 data-nosnippet>61</a>        </span><span class="kw">let </span>output = Command::new(<span class="string">"lsb_release"</span>)
<a href=#62 id=62 data-nosnippet>62</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"-d"</span>, <span class="string">"-s"</span>])
<a href=#63 id=63 data-nosnippet>63</a>            .output()
<a href=#64 id=64 data-nosnippet>64</a>            .<span class="kw">await</span>;
<a href=#65 id=65 data-nosnippet>65</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(output) = output {
<a href=#66 id=66 data-nosnippet>66</a>            <span class="kw">if </span>output.status.success() {
<a href=#67 id=67 data-nosnippet>67</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string());
<a href=#68 id=68 data-nosnippet>68</a>            }
<a href=#69 id=69 data-nosnippet>69</a>        }
<a href=#70 id=70 data-nosnippet>70</a>
<a href=#71 id=71 data-nosnippet>71</a>        <span class="prelude-val">Ok</span>(<span class="string">"Unknown Linux"</span>.to_string())
<a href=#72 id=72 data-nosnippet>72</a>    }
<a href=#73 id=73 data-nosnippet>73</a>
<a href=#74 id=74 data-nosnippet>74</a>    <span class="kw">async fn </span>get_kernel_version(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#75 id=75 data-nosnippet>75</a>        <span class="kw">let </span>output = Command::new(<span class="string">"uname"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-r"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw">if </span>output.status.success() {
<a href=#77 id=77 data-nosnippet>77</a>            <span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string())
<a href=#78 id=78 data-nosnippet>78</a>        } <span class="kw">else </span>{
<a href=#79 id=79 data-nosnippet>79</a>            <span class="prelude-val">Ok</span>(<span class="string">"unknown"</span>.to_string())
<a href=#80 id=80 data-nosnippet>80</a>        }
<a href=#81 id=81 data-nosnippet>81</a>    }
<a href=#82 id=82 data-nosnippet>82</a>
<a href=#83 id=83 data-nosnippet>83</a>    <span class="kw">async fn </span>get_architecture(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#84 id=84 data-nosnippet>84</a>        <span class="kw">let </span>output = Command::new(<span class="string">"uname"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-m"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#85 id=85 data-nosnippet>85</a>        <span class="kw">if </span>output.status.success() {
<a href=#86 id=86 data-nosnippet>86</a>            <span class="prelude-val">Ok</span>(String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout).trim().to_string())
<a href=#87 id=87 data-nosnippet>87</a>        } <span class="kw">else </span>{
<a href=#88 id=88 data-nosnippet>88</a>            <span class="prelude-val">Ok</span>(<span class="string">"unknown"</span>.to_string())
<a href=#89 id=89 data-nosnippet>89</a>        }
<a href=#90 id=90 data-nosnippet>90</a>    }
<a href=#91 id=91 data-nosnippet>91</a>
<a href=#92 id=92 data-nosnippet>92</a>    <span class="kw">async fn </span>get_cpu_info(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(content) = fs::read_to_string(<span class="string">"/proc/cpuinfo"</span>) {
<a href=#94 id=94 data-nosnippet>94</a>            <span class="kw">for </span>line <span class="kw">in </span>content.lines() {
<a href=#95 id=95 data-nosnippet>95</a>                <span class="kw">if </span>line.starts_with(<span class="string">"model name"</span>) {
<a href=#96 id=96 data-nosnippet>96</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(cpu_name) = line.split(<span class="string">':'</span>).nth(<span class="number">1</span>) {
<a href=#97 id=97 data-nosnippet>97</a>                        <span class="kw">return </span><span class="prelude-val">Ok</span>(cpu_name.trim().to_string());
<a href=#98 id=98 data-nosnippet>98</a>                    }
<a href=#99 id=99 data-nosnippet>99</a>                }
<a href=#100 id=100 data-nosnippet>100</a>            }
<a href=#101 id=101 data-nosnippet>101</a>        }
<a href=#102 id=102 data-nosnippet>102</a>        <span class="prelude-val">Ok</span>(<span class="string">"Unknown CPU"</span>.to_string())
<a href=#103 id=103 data-nosnippet>103</a>    }
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>    <span class="kw">async fn </span>get_memory_total(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#106 id=106 data-nosnippet>106</a>        <span class="self">self</span>.parse_meminfo(<span class="string">"MemTotal"</span>).<span class="kw">await
<a href=#107 id=107 data-nosnippet>107</a>    </span>}
<a href=#108 id=108 data-nosnippet>108</a>
<a href=#109 id=109 data-nosnippet>109</a>    <span class="kw">async fn </span>get_memory_available(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#110 id=110 data-nosnippet>110</a>        <span class="comment">// 尝试获取 MemAvailable，如果不存在则计算 MemFree + Buffers + Cached
<a href=#111 id=111 data-nosnippet>111</a>        </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(available) = <span class="self">self</span>.parse_meminfo(<span class="string">"MemAvailable"</span>).<span class="kw">await </span>{
<a href=#112 id=112 data-nosnippet>112</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(available);
<a href=#113 id=113 data-nosnippet>113</a>        }
<a href=#114 id=114 data-nosnippet>114</a>
<a href=#115 id=115 data-nosnippet>115</a>        <span class="kw">let </span>free = <span class="self">self</span>.parse_meminfo(<span class="string">"MemFree"</span>).<span class="kw">await</span>.unwrap_or(<span class="number">0</span>);
<a href=#116 id=116 data-nosnippet>116</a>        <span class="kw">let </span>buffers = <span class="self">self</span>.parse_meminfo(<span class="string">"Buffers"</span>).<span class="kw">await</span>.unwrap_or(<span class="number">0</span>);
<a href=#117 id=117 data-nosnippet>117</a>        <span class="kw">let </span>cached = <span class="self">self</span>.parse_meminfo(<span class="string">"Cached"</span>).<span class="kw">await</span>.unwrap_or(<span class="number">0</span>);
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>        <span class="prelude-val">Ok</span>(free + buffers + cached)
<a href=#120 id=120 data-nosnippet>120</a>    }
<a href=#121 id=121 data-nosnippet>121</a>
<a href=#122 id=122 data-nosnippet>122</a>    <span class="kw">async fn </span>parse_meminfo(<span class="kw-2">&amp;</span><span class="self">self</span>, field: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#123 id=123 data-nosnippet>123</a>        <span class="kw">let </span>content = fs::read_to_string(<span class="string">"/proc/meminfo"</span>)<span class="question-mark">?</span>;
<a href=#124 id=124 data-nosnippet>124</a>        <span class="kw">for </span>line <span class="kw">in </span>content.lines() {
<a href=#125 id=125 data-nosnippet>125</a>            <span class="kw">if </span>line.starts_with(field) {
<a href=#126 id=126 data-nosnippet>126</a>                <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#127 id=127 data-nosnippet>127</a>                <span class="kw">if </span>parts.len() &gt;= <span class="number">2 </span>{
<a href=#128 id=128 data-nosnippet>128</a>                    <span class="kw">if let </span><span class="prelude-val">Ok</span>(kb) = parts[<span class="number">1</span>].parse::&lt;u64&gt;() {
<a href=#129 id=129 data-nosnippet>129</a>                        <span class="kw">return </span><span class="prelude-val">Ok</span>(kb * <span class="number">1024</span>); <span class="comment">// 转换为字节
<a href=#130 id=130 data-nosnippet>130</a>                    </span>}
<a href=#131 id=131 data-nosnippet>131</a>                }
<a href=#132 id=132 data-nosnippet>132</a>            }
<a href=#133 id=133 data-nosnippet>133</a>        }
<a href=#134 id=134 data-nosnippet>134</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Field {} not found in /proc/meminfo"</span>, field))
<a href=#135 id=135 data-nosnippet>135</a>    }
<a href=#136 id=136 data-nosnippet>136</a>
<a href=#137 id=137 data-nosnippet>137</a>    <span class="kw">async fn </span>get_disk_info(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;DiskInfo&gt;&gt; {
<a href=#138 id=138 data-nosnippet>138</a>        <span class="kw">let </span>output = Command::new(<span class="string">"df"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"-h"</span>, <span class="string">"-T"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#139 id=139 data-nosnippet>139</a>        <span class="kw">if </span>!output.status.success() {
<a href=#140 id=140 data-nosnippet>140</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(Vec::new());
<a href=#141 id=141 data-nosnippet>141</a>        }
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>        <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#144 id=144 data-nosnippet>144</a>        <span class="kw">let </span><span class="kw-2">mut </span>disks = Vec::new();
<a href=#145 id=145 data-nosnippet>145</a>
<a href=#146 id=146 data-nosnippet>146</a>        <span class="kw">for </span>line <span class="kw">in </span>stdout.lines().skip(<span class="number">1</span>) {
<a href=#147 id=147 data-nosnippet>147</a>            <span class="comment">// 跳过标题行
<a href=#148 id=148 data-nosnippet>148</a>            </span><span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#149 id=149 data-nosnippet>149</a>            <span class="kw">if </span>parts.len() &gt;= <span class="number">7 </span>{
<a href=#150 id=150 data-nosnippet>150</a>                <span class="kw">let </span>device = parts[<span class="number">0</span>].to_string();
<a href=#151 id=151 data-nosnippet>151</a>                <span class="kw">let </span>filesystem = parts[<span class="number">1</span>].to_string();
<a href=#152 id=152 data-nosnippet>152</a>                <span class="kw">let </span>mount_point = parts[<span class="number">6</span>].to_string();
<a href=#153 id=153 data-nosnippet>153</a>
<a href=#154 id=154 data-nosnippet>154</a>                <span class="comment">// 解析大小（需要处理K, M, G, T后缀）
<a href=#155 id=155 data-nosnippet>155</a>                </span><span class="kw">let </span>total_space = <span class="self">self</span>.parse_size(parts[<span class="number">2</span>]).unwrap_or(<span class="number">0</span>);
<a href=#156 id=156 data-nosnippet>156</a>                <span class="kw">let </span>used_space = <span class="self">self</span>.parse_size(parts[<span class="number">3</span>]).unwrap_or(<span class="number">0</span>);
<a href=#157 id=157 data-nosnippet>157</a>                <span class="kw">let </span>available_space = total_space.saturating_sub(used_space);
<a href=#158 id=158 data-nosnippet>158</a>
<a href=#159 id=159 data-nosnippet>159</a>                disks.push(DiskInfo {
<a href=#160 id=160 data-nosnippet>160</a>                    device,
<a href=#161 id=161 data-nosnippet>161</a>                    mount_point,
<a href=#162 id=162 data-nosnippet>162</a>                    total_space,
<a href=#163 id=163 data-nosnippet>163</a>                    available_space,
<a href=#164 id=164 data-nosnippet>164</a>                    filesystem,
<a href=#165 id=165 data-nosnippet>165</a>                });
<a href=#166 id=166 data-nosnippet>166</a>            }
<a href=#167 id=167 data-nosnippet>167</a>        }
<a href=#168 id=168 data-nosnippet>168</a>
<a href=#169 id=169 data-nosnippet>169</a>        <span class="prelude-val">Ok</span>(disks)
<a href=#170 id=170 data-nosnippet>170</a>    }
<a href=#171 id=171 data-nosnippet>171</a>
<a href=#172 id=172 data-nosnippet>172</a>    <span class="kw">fn </span>parse_size(<span class="kw-2">&amp;</span><span class="self">self</span>, size_str: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;u64&gt; {
<a href=#173 id=173 data-nosnippet>173</a>        <span class="kw">if </span>size_str.is_empty() || size_str == <span class="string">"-" </span>{
<a href=#174 id=174 data-nosnippet>174</a>            <span class="kw">return </span><span class="prelude-val">None</span>;
<a href=#175 id=175 data-nosnippet>175</a>        }
<a href=#176 id=176 data-nosnippet>176</a>
<a href=#177 id=177 data-nosnippet>177</a>        <span class="kw">let </span>size_str = size_str.to_uppercase();
<a href=#178 id=178 data-nosnippet>178</a>        <span class="kw">let </span>(number_part, suffix) = <span class="kw">if </span>size_str.ends_with(<span class="string">'K'</span>) {
<a href=#179 id=179 data-nosnippet>179</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024</span>)
<a href=#180 id=180 data-nosnippet>180</a>        } <span class="kw">else if </span>size_str.ends_with(<span class="string">'M'</span>) {
<a href=#181 id=181 data-nosnippet>181</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024 </span>* <span class="number">1024</span>)
<a href=#182 id=182 data-nosnippet>182</a>        } <span class="kw">else if </span>size_str.ends_with(<span class="string">'G'</span>) {
<a href=#183 id=183 data-nosnippet>183</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024 </span>* <span class="number">1024 </span>* <span class="number">1024</span>)
<a href=#184 id=184 data-nosnippet>184</a>        } <span class="kw">else if </span>size_str.ends_with(<span class="string">'T'</span>) {
<a href=#185 id=185 data-nosnippet>185</a>            (<span class="kw-2">&amp;</span>size_str[..size_str.len() - <span class="number">1</span>], <span class="number">1024_u64</span>.pow(<span class="number">4</span>))
<a href=#186 id=186 data-nosnippet>186</a>        } <span class="kw">else </span>{
<a href=#187 id=187 data-nosnippet>187</a>            (size_str.as_str(), <span class="number">1</span>)
<a href=#188 id=188 data-nosnippet>188</a>        };
<a href=#189 id=189 data-nosnippet>189</a>
<a href=#190 id=190 data-nosnippet>190</a>        number_part
<a href=#191 id=191 data-nosnippet>191</a>            .parse::&lt;f64&gt;()
<a href=#192 id=192 data-nosnippet>192</a>            .ok()
<a href=#193 id=193 data-nosnippet>193</a>            .map(|n| (n * suffix <span class="kw">as </span>f64) <span class="kw">as </span>u64)
<a href=#194 id=194 data-nosnippet>194</a>    }
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>    <span class="kw">async fn </span>get_network_interfaces(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;NetworkInterface&gt;&gt; {
<a href=#197 id=197 data-nosnippet>197</a>        <span class="kw">let </span><span class="kw-2">mut </span>interfaces = Vec::new();
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>        <span class="comment">// 获取接口列表
<a href=#200 id=200 data-nosnippet>200</a>        </span><span class="kw">let </span>output = Command::new(<span class="string">"ip"</span>).args(<span class="kw-2">&amp;</span>[<span class="string">"link"</span>, <span class="string">"show"</span>]).output().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw">if </span>!output.status.success() {
<a href=#202 id=202 data-nosnippet>202</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(interfaces);
<a href=#203 id=203 data-nosnippet>203</a>        }
<a href=#204 id=204 data-nosnippet>204</a>
<a href=#205 id=205 data-nosnippet>205</a>        <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#206 id=206 data-nosnippet>206</a>        <span class="kw">let </span><span class="kw-2">mut </span>current_interface: <span class="prelude-ty">Option</span>&lt;String&gt; = <span class="prelude-val">None</span>;
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>        <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#209 id=209 data-nosnippet>209</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(interface_name) = <span class="self">self</span>.parse_interface_line(line) {
<a href=#210 id=210 data-nosnippet>210</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(name) = current_interface.take() {
<a href=#211 id=211 data-nosnippet>211</a>                    <span class="comment">// 处理前一个接口
<a href=#212 id=212 data-nosnippet>212</a>                    </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(interface) = <span class="self">self</span>.get_interface_details(<span class="kw-2">&amp;</span>name).<span class="kw">await </span>{
<a href=#213 id=213 data-nosnippet>213</a>                        interfaces.push(interface);
<a href=#214 id=214 data-nosnippet>214</a>                    }
<a href=#215 id=215 data-nosnippet>215</a>                }
<a href=#216 id=216 data-nosnippet>216</a>                current_interface = <span class="prelude-val">Some</span>(interface_name);
<a href=#217 id=217 data-nosnippet>217</a>            }
<a href=#218 id=218 data-nosnippet>218</a>        }
<a href=#219 id=219 data-nosnippet>219</a>
<a href=#220 id=220 data-nosnippet>220</a>        <span class="comment">// 处理最后一个接口
<a href=#221 id=221 data-nosnippet>221</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(name) = current_interface {
<a href=#222 id=222 data-nosnippet>222</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(interface) = <span class="self">self</span>.get_interface_details(<span class="kw-2">&amp;</span>name).<span class="kw">await </span>{
<a href=#223 id=223 data-nosnippet>223</a>                interfaces.push(interface);
<a href=#224 id=224 data-nosnippet>224</a>            }
<a href=#225 id=225 data-nosnippet>225</a>        }
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>        <span class="prelude-val">Ok</span>(interfaces)
<a href=#228 id=228 data-nosnippet>228</a>    }
<a href=#229 id=229 data-nosnippet>229</a>
<a href=#230 id=230 data-nosnippet>230</a>    <span class="kw">fn </span>parse_interface_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#231 id=231 data-nosnippet>231</a>        <span class="kw">if </span>line.starts_with(char::is_numeric) {
<a href=#232 id=232 data-nosnippet>232</a>            <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#233 id=233 data-nosnippet>233</a>            <span class="kw">if </span>parts.len() &gt;= <span class="number">2 </span>{
<a href=#234 id=234 data-nosnippet>234</a>                <span class="kw">let </span>name = parts[<span class="number">1</span>].trim_end_matches(<span class="string">':'</span>);
<a href=#235 id=235 data-nosnippet>235</a>                <span class="kw">if </span>name != <span class="string">"lo" </span>{
<a href=#236 id=236 data-nosnippet>236</a>                    <span class="comment">// 跳过回环接口
<a href=#237 id=237 data-nosnippet>237</a>                    </span><span class="kw">return </span><span class="prelude-val">Some</span>(name.to_string());
<a href=#238 id=238 data-nosnippet>238</a>                }
<a href=#239 id=239 data-nosnippet>239</a>            }
<a href=#240 id=240 data-nosnippet>240</a>        }
<a href=#241 id=241 data-nosnippet>241</a>        <span class="prelude-val">None
<a href=#242 id=242 data-nosnippet>242</a>    </span>}
<a href=#243 id=243 data-nosnippet>243</a>
<a href=#244 id=244 data-nosnippet>244</a>    <span class="kw">async fn </span>get_interface_details(<span class="kw-2">&amp;</span><span class="self">self</span>, interface_name: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;NetworkInterface&gt; {
<a href=#245 id=245 data-nosnippet>245</a>        <span class="kw">let </span><span class="kw-2">mut </span>ip_addresses = Vec::new();
<a href=#246 id=246 data-nosnippet>246</a>        <span class="kw">let </span><span class="kw-2">mut </span>mac_address = String::new();
<a href=#247 id=247 data-nosnippet>247</a>        <span class="kw">let </span><span class="kw-2">mut </span>is_up = <span class="bool-val">false</span>;
<a href=#248 id=248 data-nosnippet>248</a>
<a href=#249 id=249 data-nosnippet>249</a>        <span class="comment">// 获取IP地址
<a href=#250 id=250 data-nosnippet>250</a>        </span><span class="kw">let </span>output = Command::new(<span class="string">"ip"</span>)
<a href=#251 id=251 data-nosnippet>251</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"addr"</span>, <span class="string">"show"</span>, interface_name])
<a href=#252 id=252 data-nosnippet>252</a>            .output()
<a href=#253 id=253 data-nosnippet>253</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>        <span class="kw">if </span>output.status.success() {
<a href=#256 id=256 data-nosnippet>256</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#257 id=257 data-nosnippet>257</a>            <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#258 id=258 data-nosnippet>258</a>                <span class="kw">if </span>line.contains(<span class="string">"inet "</span>) &amp;&amp; !line.contains(<span class="string">"127.0.0.1"</span>) {
<a href=#259 id=259 data-nosnippet>259</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(ip) = <span class="self">self</span>.extract_ip_from_line(line) {
<a href=#260 id=260 data-nosnippet>260</a>                        ip_addresses.push(ip);
<a href=#261 id=261 data-nosnippet>261</a>                    }
<a href=#262 id=262 data-nosnippet>262</a>                } <span class="kw">else if </span>line.contains(<span class="string">"link/ether"</span>) {
<a href=#263 id=263 data-nosnippet>263</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(mac) = <span class="self">self</span>.extract_mac_from_line(line) {
<a href=#264 id=264 data-nosnippet>264</a>                        mac_address = mac;
<a href=#265 id=265 data-nosnippet>265</a>                    }
<a href=#266 id=266 data-nosnippet>266</a>                } <span class="kw">else if </span>line.contains(<span class="string">"state UP"</span>) {
<a href=#267 id=267 data-nosnippet>267</a>                    is_up = <span class="bool-val">true</span>;
<a href=#268 id=268 data-nosnippet>268</a>                }
<a href=#269 id=269 data-nosnippet>269</a>            }
<a href=#270 id=270 data-nosnippet>270</a>        }
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a>        <span class="prelude-val">Ok</span>(NetworkInterface {
<a href=#273 id=273 data-nosnippet>273</a>            name: interface_name.to_string(),
<a href=#274 id=274 data-nosnippet>274</a>            ip_addresses,
<a href=#275 id=275 data-nosnippet>275</a>            mac_address,
<a href=#276 id=276 data-nosnippet>276</a>            is_up,
<a href=#277 id=277 data-nosnippet>277</a>        })
<a href=#278 id=278 data-nosnippet>278</a>    }
<a href=#279 id=279 data-nosnippet>279</a>
<a href=#280 id=280 data-nosnippet>280</a>    <span class="kw">fn </span>extract_ip_from_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#281 id=281 data-nosnippet>281</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#282 id=282 data-nosnippet>282</a>        <span class="kw">for </span>part <span class="kw">in </span>parts {
<a href=#283 id=283 data-nosnippet>283</a>            <span class="kw">if </span>part.contains(<span class="string">'/'</span>) &amp;&amp; part.contains(<span class="string">'.'</span>) {
<a href=#284 id=284 data-nosnippet>284</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(ip) = part.split(<span class="string">'/'</span>).next() {
<a href=#285 id=285 data-nosnippet>285</a>                    <span class="kw">return </span><span class="prelude-val">Some</span>(ip.to_string());
<a href=#286 id=286 data-nosnippet>286</a>                }
<a href=#287 id=287 data-nosnippet>287</a>            }
<a href=#288 id=288 data-nosnippet>288</a>        }
<a href=#289 id=289 data-nosnippet>289</a>        <span class="prelude-val">None
<a href=#290 id=290 data-nosnippet>290</a>    </span>}
<a href=#291 id=291 data-nosnippet>291</a>
<a href=#292 id=292 data-nosnippet>292</a>    <span class="kw">fn </span>extract_mac_from_line(<span class="kw-2">&amp;</span><span class="self">self</span>, line: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;String&gt; {
<a href=#293 id=293 data-nosnippet>293</a>        <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#294 id=294 data-nosnippet>294</a>        <span class="kw">for </span>part <span class="kw">in </span>parts {
<a href=#295 id=295 data-nosnippet>295</a>            <span class="kw">if </span>part.matches(<span class="string">':'</span>).count() == <span class="number">5 </span>&amp;&amp; part.len() == <span class="number">17 </span>{
<a href=#296 id=296 data-nosnippet>296</a>                <span class="kw">return </span><span class="prelude-val">Some</span>(part.to_string());
<a href=#297 id=297 data-nosnippet>297</a>            }
<a href=#298 id=298 data-nosnippet>298</a>        }
<a href=#299 id=299 data-nosnippet>299</a>        <span class="prelude-val">None
<a href=#300 id=300 data-nosnippet>300</a>    </span>}
<a href=#301 id=301 data-nosnippet>301</a>}
<a href=#302 id=302 data-nosnippet>302</a>
<a href=#303 id=303 data-nosnippet>303</a><span class="attr">#[cfg(test)]
<a href=#304 id=304 data-nosnippet>304</a></span><span class="kw">mod </span>tests {
<a href=#305 id=305 data-nosnippet>305</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#306 id=306 data-nosnippet>306</a>
<a href=#307 id=307 data-nosnippet>307</a>    <span class="attr">#[tokio::test]
<a href=#308 id=308 data-nosnippet>308</a>    </span><span class="kw">async fn </span>test_system_info_collection() {
<a href=#309 id=309 data-nosnippet>309</a>        <span class="kw">let </span>collector = SystemInfoCollector::new();
<a href=#310 id=310 data-nosnippet>310</a>        <span class="kw">let </span>info = collector.collect().<span class="kw">await</span>.unwrap();
<a href=#311 id=311 data-nosnippet>311</a>
<a href=#312 id=312 data-nosnippet>312</a>        <span class="macro">assert!</span>(!info.hostname.is_empty());
<a href=#313 id=313 data-nosnippet>313</a>        <span class="macro">assert!</span>(!info.os_version.is_empty());
<a href=#314 id=314 data-nosnippet>314</a>        <span class="macro">assert!</span>(info.memory_total &gt; <span class="number">0</span>);
<a href=#315 id=315 data-nosnippet>315</a>    }
<a href=#316 id=316 data-nosnippet>316</a>
<a href=#317 id=317 data-nosnippet>317</a>    <span class="attr">#[test]
<a href=#318 id=318 data-nosnippet>318</a>    </span><span class="kw">fn </span>test_size_parsing() {
<a href=#319 id=319 data-nosnippet>319</a>        <span class="kw">let </span>collector = SystemInfoCollector::new();
<a href=#320 id=320 data-nosnippet>320</a>
<a href=#321 id=321 data-nosnippet>321</a>        <span class="macro">assert_eq!</span>(collector.parse_size(<span class="string">"1K"</span>), <span class="prelude-val">Some</span>(<span class="number">1024</span>));
<a href=#322 id=322 data-nosnippet>322</a>        <span class="macro">assert_eq!</span>(collector.parse_size(<span class="string">"1M"</span>), <span class="prelude-val">Some</span>(<span class="number">1024 </span>* <span class="number">1024</span>));
<a href=#323 id=323 data-nosnippet>323</a>        <span class="macro">assert_eq!</span>(collector.parse_size(<span class="string">"1G"</span>), <span class="prelude-val">Some</span>(<span class="number">1024 </span>* <span class="number">1024 </span>* <span class="number">1024</span>));
<a href=#324 id=324 data-nosnippet>324</a>        <span class="macro">assert_eq!</span>(
<a href=#325 id=325 data-nosnippet>325</a>            collector.parse_size(<span class="string">"1.5G"</span>),
<a href=#326 id=326 data-nosnippet>326</a>            <span class="prelude-val">Some</span>((<span class="number">1.5 </span>* <span class="number">1024.0 </span>* <span class="number">1024.0 </span>* <span class="number">1024.0</span>) <span class="kw">as </span>u64)
<a href=#327 id=327 data-nosnippet>327</a>        );
<a href=#328 id=328 data-nosnippet>328</a>    }
<a href=#329 id=329 data-nosnippet>329</a>}</code></pre></div></section></main></body></html>