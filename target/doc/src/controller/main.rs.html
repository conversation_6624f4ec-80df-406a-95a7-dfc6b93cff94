<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `controller/src/main.rs`."><title>main.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="controller" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">controller/</div>main.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">mod </span>controller;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">mod </span>deployment;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">mod </span>reconnaissance;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">mod </span>ui;
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="attr">#[cfg(test)]
<a href=#7 id=7 data-nosnippet>7</a></span><span class="kw">mod </span>tests;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>anyhow::Result;
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">use </span>clap::{Parser, Subcommand};
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">use </span>common::{CryptoManager, Logger};
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">use </span>controller::Controller;
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">use </span>std::path::PathBuf;
<a href=#14 id=14 data-nosnippet>14</a>
<a href=#15 id=15 data-nosnippet>15</a><span class="attr">#[derive(Parser)]
<a href=#16 id=16 data-nosnippet>16</a>#[command(name = <span class="string">"controller"</span>)]
<a href=#17 id=17 data-nosnippet>17</a>#[command(about = <span class="string">"Ubuntu Remote Control - Controller"</span>)]
<a href=#18 id=18 data-nosnippet>18</a></span><span class="kw">struct </span>Cli {
<a href=#19 id=19 data-nosnippet>19</a>    <span class="attr">#[command(subcommand)]
<a href=#20 id=20 data-nosnippet>20</a>    </span>command: Commands,
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"controller.log"</span>)]
<a href=#23 id=23 data-nosnippet>23</a>    </span>log_file: PathBuf,
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a>    <span class="attr">#[arg(short, long, default_value = <span class="string">"http://localhost:8080"</span>)]
<a href=#26 id=26 data-nosnippet>26</a>    </span>server_url: String,
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a>    <span class="attr">#[arg(short, long)]
<a href=#29 id=29 data-nosnippet>29</a>    </span>crypto_key: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#30 id=30 data-nosnippet>30</a>}
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a><span class="attr">#[derive(Subcommand)]
<a href=#33 id=33 data-nosnippet>33</a></span><span class="kw">enum </span>Commands {
<a href=#34 id=34 data-nosnippet>34</a>    <span class="doccomment">/// Start interactive controller
<a href=#35 id=35 data-nosnippet>35</a>    </span>Interactive,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="doccomment">/// Deploy agent to target
<a href=#37 id=37 data-nosnippet>37</a>    </span>Deploy {
<a href=#38 id=38 data-nosnippet>38</a>        <span class="attr">#[arg(short, long)]
<a href=#39 id=39 data-nosnippet>39</a>        </span>target: String,
<a href=#40 id=40 data-nosnippet>40</a>        <span class="attr">#[arg(short, long)]
<a href=#41 id=41 data-nosnippet>41</a>        </span>username: String,
<a href=#42 id=42 data-nosnippet>42</a>        <span class="attr">#[arg(short, long)]
<a href=#43 id=43 data-nosnippet>43</a>        </span>password: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#44 id=44 data-nosnippet>44</a>        <span class="attr">#[arg(short, long)]
<a href=#45 id=45 data-nosnippet>45</a>        </span>key_file: <span class="prelude-ty">Option</span>&lt;PathBuf&gt;,
<a href=#46 id=46 data-nosnippet>46</a>    },
<a href=#47 id=47 data-nosnippet>47</a>    <span class="doccomment">/// Execute single command
<a href=#48 id=48 data-nosnippet>48</a>    </span>Execute {
<a href=#49 id=49 data-nosnippet>49</a>        <span class="attr">#[arg(short, long)]
<a href=#50 id=50 data-nosnippet>50</a>        </span>target: String,
<a href=#51 id=51 data-nosnippet>51</a>        <span class="attr">#[arg(short, long)]
<a href=#52 id=52 data-nosnippet>52</a>        </span>command: String,
<a href=#53 id=53 data-nosnippet>53</a>    },
<a href=#54 id=54 data-nosnippet>54</a>    <span class="doccomment">/// List connected agents
<a href=#55 id=55 data-nosnippet>55</a>    </span>List,
<a href=#56 id=56 data-nosnippet>56</a>    <span class="doccomment">/// Start web interface
<a href=#57 id=57 data-nosnippet>57</a>    </span>Web {
<a href=#58 id=58 data-nosnippet>58</a>        <span class="attr">#[arg(short, long, default_value = <span class="string">"8081"</span>)]
<a href=#59 id=59 data-nosnippet>59</a>        </span>port: u16,
<a href=#60 id=60 data-nosnippet>60</a>    },
<a href=#61 id=61 data-nosnippet>61</a>}
<a href=#62 id=62 data-nosnippet>62</a>
<a href=#63 id=63 data-nosnippet>63</a><span class="attr">#[tokio::main]
<a href=#64 id=64 data-nosnippet>64</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#65 id=65 data-nosnippet>65</a>    <span class="kw">let </span>cli = Cli::parse();
<a href=#66 id=66 data-nosnippet>66</a>
<a href=#67 id=67 data-nosnippet>67</a>    <span class="comment">// 初始化日志
<a href=#68 id=68 data-nosnippet>68</a>    </span>env_logger::init();
<a href=#69 id=69 data-nosnippet>69</a>    <span class="kw">let </span>logger = Logger::new(cli.log_file.to_string_lossy().to_string());
<a href=#70 id=70 data-nosnippet>70</a>    logger.ensure_log_directory()<span class="question-mark">?</span>;
<a href=#71 id=71 data-nosnippet>71</a>
<a href=#72 id=72 data-nosnippet>72</a>    <span class="comment">// 初始化加密密钥
<a href=#73 id=73 data-nosnippet>73</a>    </span><span class="kw">let </span>crypto_key = <span class="kw">if let </span><span class="prelude-val">Some</span>(key) = cli.crypto_key {
<a href=#74 id=74 data-nosnippet>74</a>        key.into_bytes()
<a href=#75 id=75 data-nosnippet>75</a>    } <span class="kw">else </span>{
<a href=#76 id=76 data-nosnippet>76</a>        CryptoManager::generate_key()
<a href=#77 id=77 data-nosnippet>77</a>    };
<a href=#78 id=78 data-nosnippet>78</a>
<a href=#79 id=79 data-nosnippet>79</a>    <span class="comment">// 创建控制器实例
<a href=#80 id=80 data-nosnippet>80</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>controller = Controller::new(cli.server_url, <span class="kw-2">&amp;</span>crypto_key, logger).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#81 id=81 data-nosnippet>81</a>
<a href=#82 id=82 data-nosnippet>82</a>    <span class="kw">match </span>cli.command {
<a href=#83 id=83 data-nosnippet>83</a>        Commands::Interactive =&gt; {
<a href=#84 id=84 data-nosnippet>84</a>            <span class="macro">println!</span>(<span class="string">"Starting interactive controller..."</span>);
<a href=#85 id=85 data-nosnippet>85</a>            ui::start_interactive_mode(<span class="kw-2">&amp;mut </span>controller).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#86 id=86 data-nosnippet>86</a>        }
<a href=#87 id=87 data-nosnippet>87</a>        Commands::Deploy {
<a href=#88 id=88 data-nosnippet>88</a>            target,
<a href=#89 id=89 data-nosnippet>89</a>            username,
<a href=#90 id=90 data-nosnippet>90</a>            password,
<a href=#91 id=91 data-nosnippet>91</a>            key_file,
<a href=#92 id=92 data-nosnippet>92</a>        } =&gt; {
<a href=#93 id=93 data-nosnippet>93</a>            <span class="macro">println!</span>(<span class="string">"Deploying agent to target: {}"</span>, target);
<a href=#94 id=94 data-nosnippet>94</a>            <span class="kw">let </span>key_file_str = key_file.as_ref().map(|p| p.to_str().unwrap());
<a href=#95 id=95 data-nosnippet>95</a>            controller
<a href=#96 id=96 data-nosnippet>96</a>                .deploy_agent(<span class="kw-2">&amp;</span>target, <span class="kw-2">&amp;</span>username, password.as_deref(), key_file_str)
<a href=#97 id=97 data-nosnippet>97</a>                .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#98 id=98 data-nosnippet>98</a>        }
<a href=#99 id=99 data-nosnippet>99</a>        Commands::Execute { target, command } =&gt; {
<a href=#100 id=100 data-nosnippet>100</a>            <span class="macro">println!</span>(<span class="string">"Executing command on target: {}"</span>, target);
<a href=#101 id=101 data-nosnippet>101</a>            <span class="kw">let </span>result = controller.execute_command(<span class="kw-2">&amp;</span>target, <span class="kw-2">&amp;</span>command).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#102 id=102 data-nosnippet>102</a>            <span class="macro">println!</span>(<span class="string">"Result: {:?}"</span>, result);
<a href=#103 id=103 data-nosnippet>103</a>        }
<a href=#104 id=104 data-nosnippet>104</a>        Commands::List =&gt; {
<a href=#105 id=105 data-nosnippet>105</a>            <span class="macro">println!</span>(<span class="string">"Connected agents:"</span>);
<a href=#106 id=106 data-nosnippet>106</a>            <span class="kw">let </span>agents = controller.list_agents().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#107 id=107 data-nosnippet>107</a>            <span class="kw">for </span>agent <span class="kw">in </span>agents {
<a href=#108 id=108 data-nosnippet>108</a>                <span class="macro">println!</span>(<span class="string">"  - {}: {} ({})"</span>, agent.id, agent.hostname, agent.status);
<a href=#109 id=109 data-nosnippet>109</a>            }
<a href=#110 id=110 data-nosnippet>110</a>        }
<a href=#111 id=111 data-nosnippet>111</a>        Commands::Web { port } =&gt; {
<a href=#112 id=112 data-nosnippet>112</a>            <span class="macro">println!</span>(<span class="string">"Starting web interface on port {}"</span>, port);
<a href=#113 id=113 data-nosnippet>113</a>            ui::start_web_interface(<span class="kw-2">&amp;mut </span>controller, port).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#114 id=114 data-nosnippet>114</a>        }
<a href=#115 id=115 data-nosnippet>115</a>    }
<a href=#116 id=116 data-nosnippet>116</a>
<a href=#117 id=117 data-nosnippet>117</a>    <span class="prelude-val">Ok</span>(())
<a href=#118 id=118 data-nosnippet>118</a>}</code></pre></div></section></main></body></html>