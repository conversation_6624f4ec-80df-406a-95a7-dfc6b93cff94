<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `controller/src/controller.rs`."><title>controller.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="controller" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">controller/</div>controller.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>common::{
<a href=#3 id=3 data-nosnippet>3</a>    AuthManager, CommandMessage, CommandResult, FileDownloadMessage, FileUploadMessage,
<a href=#4 id=4 data-nosnippet>4</a>    HttpCommunicator, Logger, Message, MessageType, Permission, ProcessMessage, SystemInfoResult,
<a href=#5 id=5 data-nosnippet>5</a>};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::collections::HashMap;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>std::time::Duration;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">use </span>tokio::time::timeout;
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>uuid::Uuid;
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="attr">#[derive(Debug, Clone)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>AgentInfo {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>id: Uuid,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>hostname: String,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>ip_address: String,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>os_version: String,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>status: AgentStatus,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>last_seen: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>communicator: HttpCommunicator,
<a href=#20 id=20 data-nosnippet>20</a>}
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="attr">#[derive(Debug, Clone)]
<a href=#23 id=23 data-nosnippet>23</a></span><span class="kw">pub enum </span>AgentStatus {
<a href=#24 id=24 data-nosnippet>24</a>    Online,
<a href=#25 id=25 data-nosnippet>25</a>    Offline,
<a href=#26 id=26 data-nosnippet>26</a>    Connecting,
<a href=#27 id=27 data-nosnippet>27</a>    Error(String),
<a href=#28 id=28 data-nosnippet>28</a>}
<a href=#29 id=29 data-nosnippet>29</a>
<a href=#30 id=30 data-nosnippet>30</a><span class="kw">impl </span>std::fmt::Display <span class="kw">for </span>AgentStatus {
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">fn </span>fmt(<span class="kw-2">&amp;</span><span class="self">self</span>, f: <span class="kw-2">&amp;mut </span>std::fmt::Formatter&lt;<span class="lifetime">'_</span>&gt;) -&gt; std::fmt::Result {
<a href=#32 id=32 data-nosnippet>32</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#33 id=33 data-nosnippet>33</a>            AgentStatus::Online =&gt; <span class="macro">write!</span>(f, <span class="string">"Online"</span>),
<a href=#34 id=34 data-nosnippet>34</a>            AgentStatus::Offline =&gt; <span class="macro">write!</span>(f, <span class="string">"Offline"</span>),
<a href=#35 id=35 data-nosnippet>35</a>            AgentStatus::Connecting =&gt; <span class="macro">write!</span>(f, <span class="string">"Connecting"</span>),
<a href=#36 id=36 data-nosnippet>36</a>            AgentStatus::Error(e) =&gt; <span class="macro">write!</span>(f, <span class="string">"Error: {}"</span>, e),
<a href=#37 id=37 data-nosnippet>37</a>        }
<a href=#38 id=38 data-nosnippet>38</a>    }
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="kw">pub struct </span>Controller {
<a href=#42 id=42 data-nosnippet>42</a>    agents: HashMap&lt;Uuid, AgentInfo&gt;,
<a href=#43 id=43 data-nosnippet>43</a>    communicator: HttpCommunicator,
<a href=#44 id=44 data-nosnippet>44</a>    logger: Logger,
<a href=#45 id=45 data-nosnippet>45</a>    auth_manager: AuthManager,
<a href=#46 id=46 data-nosnippet>46</a>}
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a><span class="kw">impl </span>Controller {
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub async fn </span>new(server_url: String, crypto_key: <span class="kw-2">&amp;</span>[u8], logger: Logger) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#50 id=50 data-nosnippet>50</a>        <span class="kw">let </span>communicator = HttpCommunicator::new(server_url, crypto_key)<span class="question-mark">?</span>;
<a href=#51 id=51 data-nosnippet>51</a>        <span class="kw">let </span>auth_manager = AuthManager::new(crypto_key, <span class="number">3600</span>)<span class="question-mark">?</span>; <span class="comment">// 1小时token有效期
<a href=#52 id=52 data-nosnippet>52</a>
<a href=#53 id=53 data-nosnippet>53</a>        </span><span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#54 id=54 data-nosnippet>54</a>            agents: HashMap::new(),
<a href=#55 id=55 data-nosnippet>55</a>            communicator,
<a href=#56 id=56 data-nosnippet>56</a>            logger,
<a href=#57 id=57 data-nosnippet>57</a>            auth_manager,
<a href=#58 id=58 data-nosnippet>58</a>        })
<a href=#59 id=59 data-nosnippet>59</a>    }
<a href=#60 id=60 data-nosnippet>60</a>
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub async fn </span>deploy_agent(
<a href=#62 id=62 data-nosnippet>62</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#63 id=63 data-nosnippet>63</a>        target: <span class="kw-2">&amp;</span>str,
<a href=#64 id=64 data-nosnippet>64</a>        username: <span class="kw-2">&amp;</span>str,
<a href=#65 id=65 data-nosnippet>65</a>        password: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>str&gt;,
<a href=#66 id=66 data-nosnippet>66</a>        key_file: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>str&gt;,
<a href=#67 id=67 data-nosnippet>67</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#68 id=68 data-nosnippet>68</a>        <span class="macro">log::info!</span>(<span class="string">"Starting agent deployment to {}"</span>, target);
<a href=#69 id=69 data-nosnippet>69</a>
<a href=#70 id=70 data-nosnippet>70</a>        <span class="comment">// 这里应该实现SSH连接和agent部署逻辑
<a href=#71 id=71 data-nosnippet>71</a>        // 为了演示，我们模拟部署过程
<a href=#72 id=72 data-nosnippet>72</a>        </span><span class="macro">println!</span>(<span class="string">"Connecting to {}@{}"</span>, username, target);
<a href=#73 id=73 data-nosnippet>73</a>
<a href=#74 id=74 data-nosnippet>74</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(password) = password {
<a href=#75 id=75 data-nosnippet>75</a>            <span class="macro">println!</span>(<span class="string">"Using password authentication"</span>);
<a href=#76 id=76 data-nosnippet>76</a>        } <span class="kw">else if let </span><span class="prelude-val">Some</span>(key_file) = key_file {
<a href=#77 id=77 data-nosnippet>77</a>            <span class="macro">println!</span>(<span class="string">"Using key file: {}"</span>, key_file);
<a href=#78 id=78 data-nosnippet>78</a>        } <span class="kw">else </span>{
<a href=#79 id=79 data-nosnippet>79</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Either password or key file must be provided"</span>));
<a href=#80 id=80 data-nosnippet>80</a>        }
<a href=#81 id=81 data-nosnippet>81</a>
<a href=#82 id=82 data-nosnippet>82</a>        <span class="comment">// 模拟部署过程
<a href=#83 id=83 data-nosnippet>83</a>        </span>tokio::time::sleep(Duration::from_secs(<span class="number">2</span>)).<span class="kw">await</span>;
<a href=#84 id=84 data-nosnippet>84</a>
<a href=#85 id=85 data-nosnippet>85</a>        <span class="comment">// 创建新的agent信息
<a href=#86 id=86 data-nosnippet>86</a>        </span><span class="kw">let </span>agent_id = Uuid::new_v4();
<a href=#87 id=87 data-nosnippet>87</a>        <span class="kw">let </span>agent_info = AgentInfo {
<a href=#88 id=88 data-nosnippet>88</a>            id: agent_id,
<a href=#89 id=89 data-nosnippet>89</a>            hostname: target.to_string(),
<a href=#90 id=90 data-nosnippet>90</a>            ip_address: target.to_string(),
<a href=#91 id=91 data-nosnippet>91</a>            os_version: <span class="string">"Ubuntu 22.04 LTS"</span>.to_string(),
<a href=#92 id=92 data-nosnippet>92</a>            status: AgentStatus::Connecting,
<a href=#93 id=93 data-nosnippet>93</a>            last_seen: chrono::Utc::now(),
<a href=#94 id=94 data-nosnippet>94</a>            communicator: <span class="self">self</span>.communicator.clone(),
<a href=#95 id=95 data-nosnippet>95</a>        };
<a href=#96 id=96 data-nosnippet>96</a>
<a href=#97 id=97 data-nosnippet>97</a>        <span class="self">self</span>.agents.insert(agent_id, agent_info);
<a href=#98 id=98 data-nosnippet>98</a>        <span class="macro">log::info!</span>(<span class="string">"Agent deployed successfully to {}"</span>, target);
<a href=#99 id=99 data-nosnippet>99</a>
<a href=#100 id=100 data-nosnippet>100</a>        <span class="prelude-val">Ok</span>(())
<a href=#101 id=101 data-nosnippet>101</a>    }
<a href=#102 id=102 data-nosnippet>102</a>
<a href=#103 id=103 data-nosnippet>103</a>    <span class="kw">pub async fn </span>execute_command(<span class="kw-2">&amp;mut </span><span class="self">self</span>, target: <span class="kw-2">&amp;</span>str, command: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;CommandResult&gt; {
<a href=#104 id=104 data-nosnippet>104</a>        <span class="comment">// 查找目标agent
<a href=#105 id=105 data-nosnippet>105</a>        </span><span class="kw">let </span>agent = <span class="self">self
<a href=#106 id=106 data-nosnippet>106</a>            </span>.find_agent_by_hostname(target)
<a href=#107 id=107 data-nosnippet>107</a>            .ok_or_else(|| <span class="macro">anyhow!</span>(<span class="string">"Agent not found: {}"</span>, target))<span class="question-mark">?</span>;
<a href=#108 id=108 data-nosnippet>108</a>
<a href=#109 id=109 data-nosnippet>109</a>        <span class="self">self</span>.logger.log_command(<span class="string">"system"</span>, agent.id, command, <span class="bool-val">true</span>)<span class="question-mark">?</span>;
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>        <span class="comment">// 创建命令消息
<a href=#112 id=112 data-nosnippet>112</a>        </span><span class="kw">let </span>cmd_msg = CommandMessage {
<a href=#113 id=113 data-nosnippet>113</a>            command: command.to_string(),
<a href=#114 id=114 data-nosnippet>114</a>            args: <span class="macro">vec!</span>[],
<a href=#115 id=115 data-nosnippet>115</a>            working_dir: <span class="prelude-val">None</span>,
<a href=#116 id=116 data-nosnippet>116</a>            timeout: <span class="prelude-val">Some</span>(<span class="number">30</span>),
<a href=#117 id=117 data-nosnippet>117</a>        };
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>        <span class="kw">let </span>message = Message::new(MessageType::Command(cmd_msg));
<a href=#120 id=120 data-nosnippet>120</a>
<a href=#121 id=121 data-nosnippet>121</a>        <span class="comment">// 发送命令并等待响应
<a href=#122 id=122 data-nosnippet>122</a>        </span><span class="kw">let </span>response = timeout(
<a href=#123 id=123 data-nosnippet>123</a>            Duration::from_secs(<span class="number">35</span>),
<a href=#124 id=124 data-nosnippet>124</a>            agent.communicator.send_message(<span class="kw-2">&amp;</span>message),
<a href=#125 id=125 data-nosnippet>125</a>        )
<a href=#126 id=126 data-nosnippet>126</a>        .<span class="kw">await</span><span class="question-mark">??</span>;
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(response_msg) = response {
<a href=#129 id=129 data-nosnippet>129</a>            <span class="kw">if let </span>MessageType::CommandResult(result) = response_msg.message_type {
<a href=#130 id=130 data-nosnippet>130</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(result);
<a href=#131 id=131 data-nosnippet>131</a>            }
<a href=#132 id=132 data-nosnippet>132</a>        }
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Failed to get command result"</span>))
<a href=#135 id=135 data-nosnippet>135</a>    }
<a href=#136 id=136 data-nosnippet>136</a>
<a href=#137 id=137 data-nosnippet>137</a>    <span class="kw">pub async fn </span>upload_file(
<a href=#138 id=138 data-nosnippet>138</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#139 id=139 data-nosnippet>139</a>        target: <span class="kw-2">&amp;</span>str,
<a href=#140 id=140 data-nosnippet>140</a>        local_path: <span class="kw-2">&amp;</span>str,
<a href=#141 id=141 data-nosnippet>141</a>        remote_path: <span class="kw-2">&amp;</span>str,
<a href=#142 id=142 data-nosnippet>142</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#143 id=143 data-nosnippet>143</a>        <span class="kw">let </span>agent = <span class="self">self
<a href=#144 id=144 data-nosnippet>144</a>            </span>.find_agent_by_hostname(target)
<a href=#145 id=145 data-nosnippet>145</a>            .ok_or_else(|| <span class="macro">anyhow!</span>(<span class="string">"Agent not found: {}"</span>, target))<span class="question-mark">?</span>;
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>        <span class="self">self</span>.logger
<a href=#148 id=148 data-nosnippet>148</a>            .log_file_operation(<span class="string">"system"</span>, agent.id, <span class="string">"upload"</span>, remote_path, <span class="bool-val">true</span>)<span class="question-mark">?</span>;
<a href=#149 id=149 data-nosnippet>149</a>
<a href=#150 id=150 data-nosnippet>150</a>        <span class="comment">// 读取本地文件
<a href=#151 id=151 data-nosnippet>151</a>        </span><span class="kw">let </span>file_data = tokio::fs::read(local_path).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#152 id=152 data-nosnippet>152</a>        <span class="kw">let </span>file_size = file_data.len() <span class="kw">as </span>u64;
<a href=#153 id=153 data-nosnippet>153</a>
<a href=#154 id=154 data-nosnippet>154</a>        <span class="comment">// 分块上传
<a href=#155 id=155 data-nosnippet>155</a>        </span><span class="kw">const </span>CHUNK_SIZE: usize = <span class="number">64 </span>* <span class="number">1024</span>; <span class="comment">// 64KB chunks
<a href=#156 id=156 data-nosnippet>156</a>        </span><span class="kw">let </span>total_chunks = (file_data.len() + CHUNK_SIZE - <span class="number">1</span>) / CHUNK_SIZE;
<a href=#157 id=157 data-nosnippet>157</a>
<a href=#158 id=158 data-nosnippet>158</a>        <span class="kw">for </span>(chunk_index, chunk) <span class="kw">in </span>file_data.chunks(CHUNK_SIZE).enumerate() {
<a href=#159 id=159 data-nosnippet>159</a>            <span class="kw">let </span>upload_msg = FileUploadMessage {
<a href=#160 id=160 data-nosnippet>160</a>                file_path: remote_path.to_string(),
<a href=#161 id=161 data-nosnippet>161</a>                file_size,
<a href=#162 id=162 data-nosnippet>162</a>                chunk_index: chunk_index <span class="kw">as </span>u32,
<a href=#163 id=163 data-nosnippet>163</a>                total_chunks: total_chunks <span class="kw">as </span>u32,
<a href=#164 id=164 data-nosnippet>164</a>                data: chunk.to_vec(),
<a href=#165 id=165 data-nosnippet>165</a>            };
<a href=#166 id=166 data-nosnippet>166</a>
<a href=#167 id=167 data-nosnippet>167</a>            <span class="kw">let </span>message = Message::new(MessageType::FileUpload(upload_msg));
<a href=#168 id=168 data-nosnippet>168</a>            agent.communicator.send_message(<span class="kw-2">&amp;</span>message).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>            <span class="macro">println!</span>(<span class="string">"Uploaded chunk {}/{}"</span>, chunk_index + <span class="number">1</span>, total_chunks);
<a href=#171 id=171 data-nosnippet>171</a>        }
<a href=#172 id=172 data-nosnippet>172</a>
<a href=#173 id=173 data-nosnippet>173</a>        <span class="prelude-val">Ok</span>(())
<a href=#174 id=174 data-nosnippet>174</a>    }
<a href=#175 id=175 data-nosnippet>175</a>
<a href=#176 id=176 data-nosnippet>176</a>    <span class="kw">pub async fn </span>download_file(
<a href=#177 id=177 data-nosnippet>177</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#178 id=178 data-nosnippet>178</a>        target: <span class="kw-2">&amp;</span>str,
<a href=#179 id=179 data-nosnippet>179</a>        remote_path: <span class="kw-2">&amp;</span>str,
<a href=#180 id=180 data-nosnippet>180</a>        local_path: <span class="kw-2">&amp;</span>str,
<a href=#181 id=181 data-nosnippet>181</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#182 id=182 data-nosnippet>182</a>        <span class="kw">let </span>agent = <span class="self">self
<a href=#183 id=183 data-nosnippet>183</a>            </span>.find_agent_by_hostname(target)
<a href=#184 id=184 data-nosnippet>184</a>            .ok_or_else(|| <span class="macro">anyhow!</span>(<span class="string">"Agent not found: {}"</span>, target))<span class="question-mark">?</span>;
<a href=#185 id=185 data-nosnippet>185</a>
<a href=#186 id=186 data-nosnippet>186</a>        <span class="self">self</span>.logger
<a href=#187 id=187 data-nosnippet>187</a>            .log_file_operation(<span class="string">"system"</span>, agent.id, <span class="string">"download"</span>, remote_path, <span class="bool-val">true</span>)<span class="question-mark">?</span>;
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a>        <span class="kw">let </span>download_msg = FileDownloadMessage {
<a href=#190 id=190 data-nosnippet>190</a>            file_path: remote_path.to_string(),
<a href=#191 id=191 data-nosnippet>191</a>            chunk_size: <span class="prelude-val">Some</span>(<span class="number">64 </span>* <span class="number">1024</span>),
<a href=#192 id=192 data-nosnippet>192</a>        };
<a href=#193 id=193 data-nosnippet>193</a>
<a href=#194 id=194 data-nosnippet>194</a>        <span class="kw">let </span>message = Message::new(MessageType::FileDownload(download_msg));
<a href=#195 id=195 data-nosnippet>195</a>        <span class="kw">let </span>response = agent.communicator.send_message(<span class="kw-2">&amp;</span>message).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#196 id=196 data-nosnippet>196</a>
<a href=#197 id=197 data-nosnippet>197</a>        <span class="comment">// 处理文件下载响应
<a href=#198 id=198 data-nosnippet>198</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(response_msg) = response {
<a href=#199 id=199 data-nosnippet>199</a>            <span class="kw">if let </span>MessageType::FileData(file_data) = response_msg.message_type {
<a href=#200 id=200 data-nosnippet>200</a>                tokio::fs::write(local_path, <span class="kw-2">&amp;</span>file_data.data).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#201 id=201 data-nosnippet>201</a>                <span class="macro">println!</span>(<span class="string">"File downloaded successfully to {}"</span>, local_path);
<a href=#202 id=202 data-nosnippet>202</a>            }
<a href=#203 id=203 data-nosnippet>203</a>        }
<a href=#204 id=204 data-nosnippet>204</a>
<a href=#205 id=205 data-nosnippet>205</a>        <span class="prelude-val">Ok</span>(())
<a href=#206 id=206 data-nosnippet>206</a>    }
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>    <span class="kw">pub async fn </span>get_system_info(<span class="kw-2">&amp;mut </span><span class="self">self</span>, target: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;SystemInfoResult&gt; {
<a href=#209 id=209 data-nosnippet>209</a>        <span class="kw">let </span>agent = <span class="self">self
<a href=#210 id=210 data-nosnippet>210</a>            </span>.find_agent_by_hostname(target)
<a href=#211 id=211 data-nosnippet>211</a>            .ok_or_else(|| <span class="macro">anyhow!</span>(<span class="string">"Agent not found: {}"</span>, target))<span class="question-mark">?</span>;
<a href=#212 id=212 data-nosnippet>212</a>
<a href=#213 id=213 data-nosnippet>213</a>        <span class="kw">let </span>message = Message::new(MessageType::SystemInfo);
<a href=#214 id=214 data-nosnippet>214</a>        <span class="kw">let </span>response = timeout(
<a href=#215 id=215 data-nosnippet>215</a>            Duration::from_secs(<span class="number">10</span>),
<a href=#216 id=216 data-nosnippet>216</a>            agent.communicator.send_message(<span class="kw-2">&amp;</span>message),
<a href=#217 id=217 data-nosnippet>217</a>        )
<a href=#218 id=218 data-nosnippet>218</a>        .<span class="kw">await</span><span class="question-mark">??</span>;
<a href=#219 id=219 data-nosnippet>219</a>
<a href=#220 id=220 data-nosnippet>220</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(response_msg) = response {
<a href=#221 id=221 data-nosnippet>221</a>            <span class="kw">if let </span>MessageType::SystemInfoResult(info) = response_msg.message_type {
<a href=#222 id=222 data-nosnippet>222</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(info);
<a href=#223 id=223 data-nosnippet>223</a>            }
<a href=#224 id=224 data-nosnippet>224</a>        }
<a href=#225 id=225 data-nosnippet>225</a>
<a href=#226 id=226 data-nosnippet>226</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Failed to get system information"</span>))
<a href=#227 id=227 data-nosnippet>227</a>    }
<a href=#228 id=228 data-nosnippet>228</a>
<a href=#229 id=229 data-nosnippet>229</a>    <span class="kw">pub async fn </span>manage_process(
<a href=#230 id=230 data-nosnippet>230</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#231 id=231 data-nosnippet>231</a>        target: <span class="kw-2">&amp;</span>str,
<a href=#232 id=232 data-nosnippet>232</a>        action: common::ProcessAction,
<a href=#233 id=233 data-nosnippet>233</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#234 id=234 data-nosnippet>234</a>        <span class="kw">let </span>agent = <span class="self">self
<a href=#235 id=235 data-nosnippet>235</a>            </span>.find_agent_by_hostname(target)
<a href=#236 id=236 data-nosnippet>236</a>            .ok_or_else(|| <span class="macro">anyhow!</span>(<span class="string">"Agent not found: {}"</span>, target))<span class="question-mark">?</span>;
<a href=#237 id=237 data-nosnippet>237</a>
<a href=#238 id=238 data-nosnippet>238</a>        <span class="kw">let </span>process_msg = ProcessMessage {
<a href=#239 id=239 data-nosnippet>239</a>            action,
<a href=#240 id=240 data-nosnippet>240</a>            process_id: <span class="prelude-val">None</span>,
<a href=#241 id=241 data-nosnippet>241</a>            process_name: <span class="prelude-val">None</span>,
<a href=#242 id=242 data-nosnippet>242</a>        };
<a href=#243 id=243 data-nosnippet>243</a>
<a href=#244 id=244 data-nosnippet>244</a>        <span class="kw">let </span>message = Message::new(MessageType::ProcessManagement(process_msg));
<a href=#245 id=245 data-nosnippet>245</a>        agent.communicator.send_message(<span class="kw-2">&amp;</span>message).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#246 id=246 data-nosnippet>246</a>
<a href=#247 id=247 data-nosnippet>247</a>        <span class="prelude-val">Ok</span>(())
<a href=#248 id=248 data-nosnippet>248</a>    }
<a href=#249 id=249 data-nosnippet>249</a>
<a href=#250 id=250 data-nosnippet>250</a>    <span class="kw">pub async fn </span>list_agents(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;AgentInfo&gt;&gt; {
<a href=#251 id=251 data-nosnippet>251</a>        <span class="prelude-val">Ok</span>(<span class="self">self</span>.agents.values().cloned().collect())
<a href=#252 id=252 data-nosnippet>252</a>    }
<a href=#253 id=253 data-nosnippet>253</a>
<a href=#254 id=254 data-nosnippet>254</a>    <span class="kw">pub async fn </span>start_heartbeat_monitoring(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#255 id=255 data-nosnippet>255</a>        <span class="comment">// 启动心跳监控任务
<a href=#256 id=256 data-nosnippet>256</a>        </span><span class="kw">for </span>agent <span class="kw">in </span><span class="self">self</span>.agents.values() {
<a href=#257 id=257 data-nosnippet>257</a>            <span class="kw">let </span>communicator = agent.communicator.clone();
<a href=#258 id=258 data-nosnippet>258</a>            tokio::spawn(<span class="kw">async move </span>{
<a href=#259 id=259 data-nosnippet>259</a>                communicator
<a href=#260 id=260 data-nosnippet>260</a>                    .start_heartbeat_loop(Duration::from_secs(<span class="number">30</span>))
<a href=#261 id=261 data-nosnippet>261</a>                    .<span class="kw">await</span>;
<a href=#262 id=262 data-nosnippet>262</a>            });
<a href=#263 id=263 data-nosnippet>263</a>        }
<a href=#264 id=264 data-nosnippet>264</a>    }
<a href=#265 id=265 data-nosnippet>265</a>
<a href=#266 id=266 data-nosnippet>266</a>    <span class="kw">fn </span>find_agent_by_hostname(<span class="kw-2">&amp;</span><span class="self">self</span>, hostname: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>AgentInfo&gt; {
<a href=#267 id=267 data-nosnippet>267</a>        <span class="self">self</span>.agents
<a href=#268 id=268 data-nosnippet>268</a>            .values()
<a href=#269 id=269 data-nosnippet>269</a>            .find(|agent| agent.hostname == hostname)
<a href=#270 id=270 data-nosnippet>270</a>    }
<a href=#271 id=271 data-nosnippet>271</a>}</code></pre></div></section></main></body></html>