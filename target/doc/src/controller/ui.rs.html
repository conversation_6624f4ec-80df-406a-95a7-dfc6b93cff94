<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `controller/src/ui.rs`."><title>ui.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="controller" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">controller/</div>ui.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::controller::Controller;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>anyhow::Result;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::io::{<span class="self">self</span>, Write};
<a href=#4 id=4 data-nosnippet>4</a>
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">pub async fn </span>start_interactive_mode(controller: <span class="kw-2">&amp;mut </span>Controller) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#6 id=6 data-nosnippet>6</a>    <span class="macro">println!</span>(<span class="string">"=== Ubuntu Remote Control - Interactive Mode ==="</span>);
<a href=#7 id=7 data-nosnippet>7</a>    <span class="macro">println!</span>(<span class="string">"Type 'help' for available commands or 'quit' to exit"</span>);
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">loop </span>{
<a href=#10 id=10 data-nosnippet>10</a>        <span class="macro">print!</span>(<span class="string">"controller&gt; "</span>);
<a href=#11 id=11 data-nosnippet>11</a>        io::stdout().flush()<span class="question-mark">?</span>;
<a href=#12 id=12 data-nosnippet>12</a>
<a href=#13 id=13 data-nosnippet>13</a>        <span class="kw">let </span><span class="kw-2">mut </span>input = String::new();
<a href=#14 id=14 data-nosnippet>14</a>        io::stdin().read_line(<span class="kw-2">&amp;mut </span>input)<span class="question-mark">?</span>;
<a href=#15 id=15 data-nosnippet>15</a>        <span class="kw">let </span>input = input.trim();
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a>        <span class="kw">if </span>input.is_empty() {
<a href=#18 id=18 data-nosnippet>18</a>            <span class="kw">continue</span>;
<a href=#19 id=19 data-nosnippet>19</a>        }
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a>        <span class="kw">match </span>input {
<a href=#22 id=22 data-nosnippet>22</a>            <span class="string">"quit" </span>| <span class="string">"exit" </span>=&gt; {
<a href=#23 id=23 data-nosnippet>23</a>                <span class="macro">println!</span>(<span class="string">"Goodbye!"</span>);
<a href=#24 id=24 data-nosnippet>24</a>                <span class="kw">break</span>;
<a href=#25 id=25 data-nosnippet>25</a>            }
<a href=#26 id=26 data-nosnippet>26</a>            <span class="string">"help" </span>=&gt; {
<a href=#27 id=27 data-nosnippet>27</a>                print_help();
<a href=#28 id=28 data-nosnippet>28</a>            }
<a href=#29 id=29 data-nosnippet>29</a>            <span class="string">"list" </span>=&gt; {
<a href=#30 id=30 data-nosnippet>30</a>                <span class="kw">let </span>agents = controller.list_agents().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#31 id=31 data-nosnippet>31</a>                <span class="kw">if </span>agents.is_empty() {
<a href=#32 id=32 data-nosnippet>32</a>                    <span class="macro">println!</span>(<span class="string">"No agents connected"</span>);
<a href=#33 id=33 data-nosnippet>33</a>                } <span class="kw">else </span>{
<a href=#34 id=34 data-nosnippet>34</a>                    <span class="macro">println!</span>(<span class="string">"Connected agents:"</span>);
<a href=#35 id=35 data-nosnippet>35</a>                    <span class="kw">for </span>agent <span class="kw">in </span>agents {
<a href=#36 id=36 data-nosnippet>36</a>                        <span class="macro">println!</span>(<span class="string">"  - {}: {} ({})"</span>, agent.id, agent.hostname, agent.status);
<a href=#37 id=37 data-nosnippet>37</a>                    }
<a href=#38 id=38 data-nosnippet>38</a>                }
<a href=#39 id=39 data-nosnippet>39</a>            }
<a href=#40 id=40 data-nosnippet>40</a>            <span class="kw">_ </span>=&gt; {
<a href=#41 id=41 data-nosnippet>41</a>                <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = handle_command(controller, input).<span class="kw">await </span>{
<a href=#42 id=42 data-nosnippet>42</a>                    <span class="macro">println!</span>(<span class="string">"Error: {}"</span>, e);
<a href=#43 id=43 data-nosnippet>43</a>                }
<a href=#44 id=44 data-nosnippet>44</a>            }
<a href=#45 id=45 data-nosnippet>45</a>        }
<a href=#46 id=46 data-nosnippet>46</a>    }
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a>    <span class="prelude-val">Ok</span>(())
<a href=#49 id=49 data-nosnippet>49</a>}
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a><span class="kw">async fn </span>handle_command(controller: <span class="kw-2">&amp;mut </span>Controller, input: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = input.split_whitespace().collect();
<a href=#53 id=53 data-nosnippet>53</a>    <span class="kw">if </span>parts.is_empty() {
<a href=#54 id=54 data-nosnippet>54</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#55 id=55 data-nosnippet>55</a>    }
<a href=#56 id=56 data-nosnippet>56</a>
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">match </span>parts[<span class="number">0</span>] {
<a href=#58 id=58 data-nosnippet>58</a>        <span class="string">"deploy" </span>=&gt; {
<a href=#59 id=59 data-nosnippet>59</a>            <span class="kw">if </span>parts.len() &lt; <span class="number">3 </span>{
<a href=#60 id=60 data-nosnippet>60</a>                <span class="macro">println!</span>(<span class="string">"Usage: deploy &lt;target&gt; &lt;username&gt; [password]"</span>);
<a href=#61 id=61 data-nosnippet>61</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#62 id=62 data-nosnippet>62</a>            }
<a href=#63 id=63 data-nosnippet>63</a>            <span class="kw">let </span>target = parts[<span class="number">1</span>];
<a href=#64 id=64 data-nosnippet>64</a>            <span class="kw">let </span>username = parts[<span class="number">2</span>];
<a href=#65 id=65 data-nosnippet>65</a>            <span class="kw">let </span>password = parts.get(<span class="number">3</span>).copied();
<a href=#66 id=66 data-nosnippet>66</a>
<a href=#67 id=67 data-nosnippet>67</a>            controller
<a href=#68 id=68 data-nosnippet>68</a>                .deploy_agent(target, username, password, <span class="prelude-val">None</span>)
<a href=#69 id=69 data-nosnippet>69</a>                .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#70 id=70 data-nosnippet>70</a>            <span class="macro">println!</span>(<span class="string">"Agent deployed successfully to {}"</span>, target);
<a href=#71 id=71 data-nosnippet>71</a>        }
<a href=#72 id=72 data-nosnippet>72</a>        <span class="string">"exec" </span>=&gt; {
<a href=#73 id=73 data-nosnippet>73</a>            <span class="kw">if </span>parts.len() &lt; <span class="number">3 </span>{
<a href=#74 id=74 data-nosnippet>74</a>                <span class="macro">println!</span>(<span class="string">"Usage: exec &lt;target&gt; &lt;command&gt;"</span>);
<a href=#75 id=75 data-nosnippet>75</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#76 id=76 data-nosnippet>76</a>            }
<a href=#77 id=77 data-nosnippet>77</a>            <span class="kw">let </span>target = parts[<span class="number">1</span>];
<a href=#78 id=78 data-nosnippet>78</a>            <span class="kw">let </span>command = parts[<span class="number">2</span>..].join(<span class="string">" "</span>);
<a href=#79 id=79 data-nosnippet>79</a>
<a href=#80 id=80 data-nosnippet>80</a>            <span class="kw">let </span>result = controller.execute_command(target, <span class="kw-2">&amp;</span>command).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#81 id=81 data-nosnippet>81</a>            <span class="macro">println!</span>(<span class="string">"Exit code: {}"</span>, result.exit_code);
<a href=#82 id=82 data-nosnippet>82</a>            <span class="kw">if </span>!result.stdout.is_empty() {
<a href=#83 id=83 data-nosnippet>83</a>                <span class="macro">println!</span>(<span class="string">"Stdout:\n{}"</span>, result.stdout);
<a href=#84 id=84 data-nosnippet>84</a>            }
<a href=#85 id=85 data-nosnippet>85</a>            <span class="kw">if </span>!result.stderr.is_empty() {
<a href=#86 id=86 data-nosnippet>86</a>                <span class="macro">println!</span>(<span class="string">"Stderr:\n{}"</span>, result.stderr);
<a href=#87 id=87 data-nosnippet>87</a>            }
<a href=#88 id=88 data-nosnippet>88</a>        }
<a href=#89 id=89 data-nosnippet>89</a>        <span class="string">"upload" </span>=&gt; {
<a href=#90 id=90 data-nosnippet>90</a>            <span class="kw">if </span>parts.len() &lt; <span class="number">4 </span>{
<a href=#91 id=91 data-nosnippet>91</a>                <span class="macro">println!</span>(<span class="string">"Usage: upload &lt;target&gt; &lt;local_path&gt; &lt;remote_path&gt;"</span>);
<a href=#92 id=92 data-nosnippet>92</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#93 id=93 data-nosnippet>93</a>            }
<a href=#94 id=94 data-nosnippet>94</a>            <span class="kw">let </span>target = parts[<span class="number">1</span>];
<a href=#95 id=95 data-nosnippet>95</a>            <span class="kw">let </span>local_path = parts[<span class="number">2</span>];
<a href=#96 id=96 data-nosnippet>96</a>            <span class="kw">let </span>remote_path = parts[<span class="number">3</span>];
<a href=#97 id=97 data-nosnippet>97</a>
<a href=#98 id=98 data-nosnippet>98</a>            controller
<a href=#99 id=99 data-nosnippet>99</a>                .upload_file(target, local_path, remote_path)
<a href=#100 id=100 data-nosnippet>100</a>                .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#101 id=101 data-nosnippet>101</a>            <span class="macro">println!</span>(<span class="string">"File uploaded successfully"</span>);
<a href=#102 id=102 data-nosnippet>102</a>        }
<a href=#103 id=103 data-nosnippet>103</a>        <span class="string">"download" </span>=&gt; {
<a href=#104 id=104 data-nosnippet>104</a>            <span class="kw">if </span>parts.len() &lt; <span class="number">4 </span>{
<a href=#105 id=105 data-nosnippet>105</a>                <span class="macro">println!</span>(<span class="string">"Usage: download &lt;target&gt; &lt;remote_path&gt; &lt;local_path&gt;"</span>);
<a href=#106 id=106 data-nosnippet>106</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#107 id=107 data-nosnippet>107</a>            }
<a href=#108 id=108 data-nosnippet>108</a>            <span class="kw">let </span>target = parts[<span class="number">1</span>];
<a href=#109 id=109 data-nosnippet>109</a>            <span class="kw">let </span>remote_path = parts[<span class="number">2</span>];
<a href=#110 id=110 data-nosnippet>110</a>            <span class="kw">let </span>local_path = parts[<span class="number">3</span>];
<a href=#111 id=111 data-nosnippet>111</a>
<a href=#112 id=112 data-nosnippet>112</a>            controller
<a href=#113 id=113 data-nosnippet>113</a>                .download_file(target, remote_path, local_path)
<a href=#114 id=114 data-nosnippet>114</a>                .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#115 id=115 data-nosnippet>115</a>            <span class="macro">println!</span>(<span class="string">"File downloaded successfully"</span>);
<a href=#116 id=116 data-nosnippet>116</a>        }
<a href=#117 id=117 data-nosnippet>117</a>        <span class="string">"sysinfo" </span>=&gt; {
<a href=#118 id=118 data-nosnippet>118</a>            <span class="kw">if </span>parts.len() &lt; <span class="number">2 </span>{
<a href=#119 id=119 data-nosnippet>119</a>                <span class="macro">println!</span>(<span class="string">"Usage: sysinfo &lt;target&gt;"</span>);
<a href=#120 id=120 data-nosnippet>120</a>                <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#121 id=121 data-nosnippet>121</a>            }
<a href=#122 id=122 data-nosnippet>122</a>            <span class="kw">let </span>target = parts[<span class="number">1</span>];
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>            <span class="kw">let </span>info = controller.get_system_info(target).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#125 id=125 data-nosnippet>125</a>            <span class="macro">println!</span>(<span class="string">"System Information for {}:"</span>, target);
<a href=#126 id=126 data-nosnippet>126</a>            <span class="macro">println!</span>(<span class="string">"  Hostname: {}"</span>, info.hostname);
<a href=#127 id=127 data-nosnippet>127</a>            <span class="macro">println!</span>(<span class="string">"  OS: {}"</span>, info.os_version);
<a href=#128 id=128 data-nosnippet>128</a>            <span class="macro">println!</span>(<span class="string">"  Kernel: {}"</span>, info.kernel_version);
<a href=#129 id=129 data-nosnippet>129</a>            <span class="macro">println!</span>(<span class="string">"  Architecture: {}"</span>, info.architecture);
<a href=#130 id=130 data-nosnippet>130</a>            <span class="macro">println!</span>(<span class="string">"  CPU: {}"</span>, info.cpu_info);
<a href=#131 id=131 data-nosnippet>131</a>            <span class="macro">println!</span>(
<a href=#132 id=132 data-nosnippet>132</a>                <span class="string">"  Memory: {} MB total, {} MB available"</span>,
<a href=#133 id=133 data-nosnippet>133</a>                info.memory_total / <span class="number">1024 </span>/ <span class="number">1024</span>,
<a href=#134 id=134 data-nosnippet>134</a>                info.memory_available / <span class="number">1024 </span>/ <span class="number">1024
<a href=#135 id=135 data-nosnippet>135</a>            </span>);
<a href=#136 id=136 data-nosnippet>136</a>        }
<a href=#137 id=137 data-nosnippet>137</a>        <span class="kw">_ </span>=&gt; {
<a href=#138 id=138 data-nosnippet>138</a>            <span class="macro">println!</span>(
<a href=#139 id=139 data-nosnippet>139</a>                <span class="string">"Unknown command: {}. Type 'help' for available commands."</span>,
<a href=#140 id=140 data-nosnippet>140</a>                parts[<span class="number">0</span>]
<a href=#141 id=141 data-nosnippet>141</a>            );
<a href=#142 id=142 data-nosnippet>142</a>        }
<a href=#143 id=143 data-nosnippet>143</a>    }
<a href=#144 id=144 data-nosnippet>144</a>
<a href=#145 id=145 data-nosnippet>145</a>    <span class="prelude-val">Ok</span>(())
<a href=#146 id=146 data-nosnippet>146</a>}
<a href=#147 id=147 data-nosnippet>147</a>
<a href=#148 id=148 data-nosnippet>148</a><span class="kw">fn </span>print_help() {
<a href=#149 id=149 data-nosnippet>149</a>    <span class="macro">println!</span>(<span class="string">"Available commands:"</span>);
<a href=#150 id=150 data-nosnippet>150</a>    <span class="macro">println!</span>(<span class="string">"  help                                    - Show this help message"</span>);
<a href=#151 id=151 data-nosnippet>151</a>    <span class="macro">println!</span>(<span class="string">"  list                                    - List connected agents"</span>);
<a href=#152 id=152 data-nosnippet>152</a>    <span class="macro">println!</span>(<span class="string">"  deploy &lt;target&gt; &lt;username&gt; [password]   - Deploy agent to target"</span>);
<a href=#153 id=153 data-nosnippet>153</a>    <span class="macro">println!</span>(<span class="string">"  exec &lt;target&gt; &lt;command&gt;                 - Execute command on target"</span>);
<a href=#154 id=154 data-nosnippet>154</a>    <span class="macro">println!</span>(<span class="string">"  upload &lt;target&gt; &lt;local&gt; &lt;remote&gt;        - Upload file to target"</span>);
<a href=#155 id=155 data-nosnippet>155</a>    <span class="macro">println!</span>(<span class="string">"  download &lt;target&gt; &lt;remote&gt; &lt;local&gt;      - Download file from target"</span>);
<a href=#156 id=156 data-nosnippet>156</a>    <span class="macro">println!</span>(<span class="string">"  sysinfo &lt;target&gt;                        - Get system information"</span>);
<a href=#157 id=157 data-nosnippet>157</a>    <span class="macro">println!</span>(<span class="string">"  quit/exit                               - Exit the program"</span>);
<a href=#158 id=158 data-nosnippet>158</a>}
<a href=#159 id=159 data-nosnippet>159</a>
<a href=#160 id=160 data-nosnippet>160</a><span class="kw">pub async fn </span>start_web_interface(controller: <span class="kw-2">&amp;mut </span>Controller, port: u16) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#161 id=161 data-nosnippet>161</a>    <span class="kw">use </span>warp::Filter;
<a href=#162 id=162 data-nosnippet>162</a>
<a href=#163 id=163 data-nosnippet>163</a>    <span class="macro">println!</span>(<span class="string">"Starting web interface on port {}"</span>, port);
<a href=#164 id=164 data-nosnippet>164</a>
<a href=#165 id=165 data-nosnippet>165</a>    <span class="comment">// 创建基本的Web路由
<a href=#166 id=166 data-nosnippet>166</a>    </span><span class="kw">let </span>hello = <span class="macro">warp::path!</span>(<span class="string">"hello" </span>/ String).map(|name| <span class="macro">format!</span>(<span class="string">"Hello, {}!"</span>, name));
<a href=#167 id=167 data-nosnippet>167</a>
<a href=#168 id=168 data-nosnippet>168</a>    <span class="kw">let </span>routes = hello;
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>    warp::serve(routes).run(([<span class="number">127</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">1</span>], port)).<span class="kw">await</span>;
<a href=#171 id=171 data-nosnippet>171</a>
<a href=#172 id=172 data-nosnippet>172</a>    <span class="prelude-val">Ok</span>(())
<a href=#173 id=173 data-nosnippet>173</a>}
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a><span class="comment">// 简化的Web界面实现
<a href=#176 id=176 data-nosnippet>176</a></span><span class="kw">pub async fn </span>start_simple_web_interface(_controller: <span class="kw-2">&amp;mut </span>Controller, port: u16) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#177 id=177 data-nosnippet>177</a>    <span class="kw">use </span>std::convert::Infallible;
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">use </span>std::net::SocketAddr;
<a href=#179 id=179 data-nosnippet>179</a>    <span class="kw">use </span>tokio::io::{AsyncReadExt, AsyncWriteExt};
<a href=#180 id=180 data-nosnippet>180</a>    <span class="kw">use </span>tokio::net::TcpListener;
<a href=#181 id=181 data-nosnippet>181</a>
<a href=#182 id=182 data-nosnippet>182</a>    <span class="kw">let </span>addr = SocketAddr::from(([<span class="number">127</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">1</span>], port));
<a href=#183 id=183 data-nosnippet>183</a>    <span class="kw">let </span>listener = TcpListener::bind(addr).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#184 id=184 data-nosnippet>184</a>
<a href=#185 id=185 data-nosnippet>185</a>    <span class="macro">println!</span>(<span class="string">"Web interface listening on http://{}"</span>, addr);
<a href=#186 id=186 data-nosnippet>186</a>
<a href=#187 id=187 data-nosnippet>187</a>    <span class="kw">loop </span>{
<a href=#188 id=188 data-nosnippet>188</a>        <span class="kw">let </span>(<span class="kw-2">mut </span>socket, <span class="kw">_</span>) = listener.accept().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#189 id=189 data-nosnippet>189</a>
<a href=#190 id=190 data-nosnippet>190</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#191 id=191 data-nosnippet>191</a>            <span class="kw">let </span><span class="kw-2">mut </span>buffer = [<span class="number">0</span>; <span class="number">1024</span>];
<a href=#192 id=192 data-nosnippet>192</a>
<a href=#193 id=193 data-nosnippet>193</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(n) = socket.read(<span class="kw-2">&amp;mut </span>buffer).<span class="kw">await </span>{
<a href=#194 id=194 data-nosnippet>194</a>                <span class="kw">let </span>request = String::from_utf8_lossy(<span class="kw-2">&amp;</span>buffer[..n]);
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>                <span class="kw">let </span>response = <span class="kw">if </span>request.contains(<span class="string">"GET / "</span>) {
<a href=#197 id=197 data-nosnippet>197</a>                    create_dashboard_html()
<a href=#198 id=198 data-nosnippet>198</a>                } <span class="kw">else if </span>request.contains(<span class="string">"GET /api/agents"</span>) {
<a href=#199 id=199 data-nosnippet>199</a>                    <span class="string">r#"HTTP/1.1 200 OK
<a href=#200 id=200 data-nosnippet>200</a>Content-Type: application/json
<a href=#201 id=201 data-nosnippet>201</a>
<a href=#202 id=202 data-nosnippet>202</a>{"agents": []}"#
<a href=#203 id=203 data-nosnippet>203</a>                        </span>.to_string()
<a href=#204 id=204 data-nosnippet>204</a>                } <span class="kw">else </span>{
<a href=#205 id=205 data-nosnippet>205</a>                    <span class="string">r#"HTTP/1.1 404 Not Found
<a href=#206 id=206 data-nosnippet>206</a>Content-Type: text/plain
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>Not Found"#
<a href=#209 id=209 data-nosnippet>209</a>                        </span>.to_string()
<a href=#210 id=210 data-nosnippet>210</a>                };
<a href=#211 id=211 data-nosnippet>211</a>
<a href=#212 id=212 data-nosnippet>212</a>                <span class="kw">let _ </span>= socket.write_all(response.as_bytes()).<span class="kw">await</span>;
<a href=#213 id=213 data-nosnippet>213</a>            }
<a href=#214 id=214 data-nosnippet>214</a>        });
<a href=#215 id=215 data-nosnippet>215</a>    }
<a href=#216 id=216 data-nosnippet>216</a>}
<a href=#217 id=217 data-nosnippet>217</a>
<a href=#218 id=218 data-nosnippet>218</a><span class="kw">fn </span>create_dashboard_html() -&gt; String {
<a href=#219 id=219 data-nosnippet>219</a>    <span class="string">r#"HTTP/1.1 200 OK
<a href=#220 id=220 data-nosnippet>220</a>Content-Type: text/html
<a href=#221 id=221 data-nosnippet>221</a>
<a href=#222 id=222 data-nosnippet>222</a>&lt;!DOCTYPE html&gt;
<a href=#223 id=223 data-nosnippet>223</a>&lt;html&gt;
<a href=#224 id=224 data-nosnippet>224</a>&lt;head&gt;
<a href=#225 id=225 data-nosnippet>225</a>    &lt;title&gt;Ubuntu Remote Control Dashboard&lt;/title&gt;
<a href=#226 id=226 data-nosnippet>226</a>    &lt;style&gt;
<a href=#227 id=227 data-nosnippet>227</a>        body { font-family: Arial, sans-serif; margin: 20px; }
<a href=#228 id=228 data-nosnippet>228</a>        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
<a href=#229 id=229 data-nosnippet>229</a>        .agents { margin-top: 20px; }
<a href=#230 id=230 data-nosnippet>230</a>        .agent { border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px; }
<a href=#231 id=231 data-nosnippet>231</a>        .online { background-color: #e8f5e8; }
<a href=#232 id=232 data-nosnippet>232</a>        .offline { background-color: #f5e8e8; }
<a href=#233 id=233 data-nosnippet>233</a>    &lt;/style&gt;
<a href=#234 id=234 data-nosnippet>234</a>&lt;/head&gt;
<a href=#235 id=235 data-nosnippet>235</a>&lt;body&gt;
<a href=#236 id=236 data-nosnippet>236</a>    &lt;div class="header"&gt;
<a href=#237 id=237 data-nosnippet>237</a>        &lt;h1&gt;Ubuntu Remote Control Dashboard&lt;/h1&gt;
<a href=#238 id=238 data-nosnippet>238</a>        &lt;p&gt;Manage your remote agents from this web interface&lt;/p&gt;
<a href=#239 id=239 data-nosnippet>239</a>    &lt;/div&gt;
<a href=#240 id=240 data-nosnippet>240</a>    
<a href=#241 id=241 data-nosnippet>241</a>    &lt;div class="agents"&gt;
<a href=#242 id=242 data-nosnippet>242</a>        &lt;h2&gt;Connected Agents&lt;/h2&gt;
<a href=#243 id=243 data-nosnippet>243</a>        &lt;div id="agent-list"&gt;
<a href=#244 id=244 data-nosnippet>244</a>            &lt;p&gt;Loading agents...&lt;/p&gt;
<a href=#245 id=245 data-nosnippet>245</a>        &lt;/div&gt;
<a href=#246 id=246 data-nosnippet>246</a>    &lt;/div&gt;
<a href=#247 id=247 data-nosnippet>247</a>    
<a href=#248 id=248 data-nosnippet>248</a>    &lt;script&gt;
<a href=#249 id=249 data-nosnippet>249</a>        // 简单的JavaScript来获取agent列表
<a href=#250 id=250 data-nosnippet>250</a>        fetch('/api/agents')
<a href=#251 id=251 data-nosnippet>251</a>            .then(response =&gt; response.json())
<a href=#252 id=252 data-nosnippet>252</a>            .then(data =&gt; {
<a href=#253 id=253 data-nosnippet>253</a>                const agentList = document.getElementById('agent-list');
<a href=#254 id=254 data-nosnippet>254</a>                if (data.agents.length === 0) {
<a href=#255 id=255 data-nosnippet>255</a>                    agentList.innerHTML = '&lt;p&gt;No agents connected&lt;/p&gt;';
<a href=#256 id=256 data-nosnippet>256</a>                } else {
<a href=#257 id=257 data-nosnippet>257</a>                    agentList.innerHTML = data.agents.map(agent =&gt; 
<a href=#258 id=258 data-nosnippet>258</a>                        `&lt;div class="agent ${agent.status.toLowerCase()}"&gt;
<a href=#259 id=259 data-nosnippet>259</a>                            &lt;strong&gt;${agent.hostname}&lt;/strong&gt; (${agent.ip_address})
<a href=#260 id=260 data-nosnippet>260</a>                            &lt;br&gt;Status: ${agent.status}
<a href=#261 id=261 data-nosnippet>261</a>                            &lt;br&gt;Last seen: ${agent.last_seen}
<a href=#262 id=262 data-nosnippet>262</a>                        &lt;/div&gt;`
<a href=#263 id=263 data-nosnippet>263</a>                    ).join('');
<a href=#264 id=264 data-nosnippet>264</a>                }
<a href=#265 id=265 data-nosnippet>265</a>            })
<a href=#266 id=266 data-nosnippet>266</a>            .catch(error =&gt; {
<a href=#267 id=267 data-nosnippet>267</a>                document.getElementById('agent-list').innerHTML = '&lt;p&gt;Error loading agents&lt;/p&gt;';
<a href=#268 id=268 data-nosnippet>268</a>            });
<a href=#269 id=269 data-nosnippet>269</a>    &lt;/script&gt;
<a href=#270 id=270 data-nosnippet>270</a>&lt;/body&gt;
<a href=#271 id=271 data-nosnippet>271</a>&lt;/html&gt;"#
<a href=#272 id=272 data-nosnippet>272</a>        </span>.to_string()
<a href=#273 id=273 data-nosnippet>273</a>}</code></pre></div></section></main></body></html>