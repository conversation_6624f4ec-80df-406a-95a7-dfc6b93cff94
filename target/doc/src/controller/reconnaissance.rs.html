<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `controller/src/reconnaissance.rs`."><title>reconnaissance.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="controller" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">controller/</div>reconnaissance.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>anyhow::{anyhow, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::collections::HashMap;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::net::{IpAddr, Ipv4Addr};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::process::Command;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>tokio::process::Command <span class="kw">as </span>AsyncCommand;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>uuid::Uuid;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#10 id=10 data-nosnippet>10</a></span><span class="kw">pub struct </span>TargetInfo {
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>id: Uuid,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>ip_address: IpAddr,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>hostname: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>os_info: <span class="prelude-ty">Option</span>&lt;OsInfo&gt;,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>open_ports: Vec&lt;PortInfo&gt;,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>services: Vec&lt;ServiceInfo&gt;,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>vulnerabilities: Vec&lt;VulnerabilityInfo&gt;,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>last_scanned: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#19 id=19 data-nosnippet>19</a>}
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#22 id=22 data-nosnippet>22</a></span><span class="kw">pub struct </span>OsInfo {
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>name: String,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>version: String,
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>architecture: String,
<a href=#26 id=26 data-nosnippet>26</a>    <span class="kw">pub </span>kernel_version: String,
<a href=#27 id=27 data-nosnippet>27</a>}
<a href=#28 id=28 data-nosnippet>28</a>
<a href=#29 id=29 data-nosnippet>29</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#30 id=30 data-nosnippet>30</a></span><span class="kw">pub struct </span>PortInfo {
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub </span>port: u16,
<a href=#32 id=32 data-nosnippet>32</a>    <span class="kw">pub </span>protocol: String,
<a href=#33 id=33 data-nosnippet>33</a>    <span class="kw">pub </span>state: String,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>service: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>version: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#36 id=36 data-nosnippet>36</a>}
<a href=#37 id=37 data-nosnippet>37</a>
<a href=#38 id=38 data-nosnippet>38</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#39 id=39 data-nosnippet>39</a></span><span class="kw">pub struct </span>ServiceInfo {
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">pub </span>name: String,
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">pub </span>version: String,
<a href=#42 id=42 data-nosnippet>42</a>    <span class="kw">pub </span>port: u16,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>banner: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#47 id=47 data-nosnippet>47</a></span><span class="kw">pub struct </span>VulnerabilityInfo {
<a href=#48 id=48 data-nosnippet>48</a>    <span class="kw">pub </span>cve_id: String,
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>severity: String,
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub </span>description: String,
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub </span>affected_service: String,
<a href=#52 id=52 data-nosnippet>52</a>}
<a href=#53 id=53 data-nosnippet>53</a>
<a href=#54 id=54 data-nosnippet>54</a><span class="kw">pub struct </span>ReconnaissanceEngine {
<a href=#55 id=55 data-nosnippet>55</a>    targets: HashMap&lt;IpAddr, TargetInfo&gt;,
<a href=#56 id=56 data-nosnippet>56</a>}
<a href=#57 id=57 data-nosnippet>57</a>
<a href=#58 id=58 data-nosnippet>58</a><span class="kw">impl </span>ReconnaissanceEngine {
<a href=#59 id=59 data-nosnippet>59</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#60 id=60 data-nosnippet>60</a>        <span class="self">Self </span>{
<a href=#61 id=61 data-nosnippet>61</a>            targets: HashMap::new(),
<a href=#62 id=62 data-nosnippet>62</a>        }
<a href=#63 id=63 data-nosnippet>63</a>    }
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a>    <span class="kw">pub async fn </span>scan_network(<span class="kw-2">&amp;mut </span><span class="self">self</span>, network: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TargetInfo&gt;&gt; {
<a href=#66 id=66 data-nosnippet>66</a>        <span class="macro">println!</span>(<span class="string">"Scanning network: {}"</span>, network);
<a href=#67 id=67 data-nosnippet>67</a>
<a href=#68 id=68 data-nosnippet>68</a>        <span class="comment">// 使用nmap进行网络扫描
<a href=#69 id=69 data-nosnippet>69</a>        </span><span class="kw">let </span>hosts = <span class="self">self</span>.discover_hosts(network).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#70 id=70 data-nosnippet>70</a>
<a href=#71 id=71 data-nosnippet>71</a>        <span class="kw">for </span>host <span class="kw">in </span>hosts {
<a href=#72 id=72 data-nosnippet>72</a>            <span class="kw">let </span>target_info = <span class="self">self</span>.scan_host(host).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#73 id=73 data-nosnippet>73</a>            <span class="self">self</span>.targets.insert(host, target_info);
<a href=#74 id=74 data-nosnippet>74</a>        }
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>        <span class="prelude-val">Ok</span>(<span class="self">self</span>.targets.values().cloned().collect())
<a href=#77 id=77 data-nosnippet>77</a>    }
<a href=#78 id=78 data-nosnippet>78</a>
<a href=#79 id=79 data-nosnippet>79</a>    <span class="kw">pub async fn </span>scan_host(<span class="kw-2">&amp;mut </span><span class="self">self</span>, ip: IpAddr) -&gt; <span class="prelude-ty">Result</span>&lt;TargetInfo&gt; {
<a href=#80 id=80 data-nosnippet>80</a>        <span class="macro">println!</span>(<span class="string">"Scanning host: {}"</span>, ip);
<a href=#81 id=81 data-nosnippet>81</a>
<a href=#82 id=82 data-nosnippet>82</a>        <span class="kw">let </span><span class="kw-2">mut </span>target_info = TargetInfo {
<a href=#83 id=83 data-nosnippet>83</a>            id: Uuid::new_v4(),
<a href=#84 id=84 data-nosnippet>84</a>            ip_address: ip,
<a href=#85 id=85 data-nosnippet>85</a>            hostname: <span class="prelude-val">None</span>,
<a href=#86 id=86 data-nosnippet>86</a>            os_info: <span class="prelude-val">None</span>,
<a href=#87 id=87 data-nosnippet>87</a>            open_ports: Vec::new(),
<a href=#88 id=88 data-nosnippet>88</a>            services: Vec::new(),
<a href=#89 id=89 data-nosnippet>89</a>            vulnerabilities: Vec::new(),
<a href=#90 id=90 data-nosnippet>90</a>            last_scanned: chrono::Utc::now(),
<a href=#91 id=91 data-nosnippet>91</a>        };
<a href=#92 id=92 data-nosnippet>92</a>
<a href=#93 id=93 data-nosnippet>93</a>        <span class="comment">// 获取主机名
<a href=#94 id=94 data-nosnippet>94</a>        </span>target_info.hostname = <span class="self">self</span>.resolve_hostname(ip).<span class="kw">await</span>.ok();
<a href=#95 id=95 data-nosnippet>95</a>
<a href=#96 id=96 data-nosnippet>96</a>        <span class="comment">// 端口扫描
<a href=#97 id=97 data-nosnippet>97</a>        </span>target_info.open_ports = <span class="self">self</span>.scan_ports(ip).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#98 id=98 data-nosnippet>98</a>
<a href=#99 id=99 data-nosnippet>99</a>        <span class="comment">// 服务识别
<a href=#100 id=100 data-nosnippet>100</a>        </span>target_info.services = <span class="self">self</span>.identify_services(ip, <span class="kw-2">&amp;</span>target_info.open_ports).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#101 id=101 data-nosnippet>101</a>
<a href=#102 id=102 data-nosnippet>102</a>        <span class="comment">// 操作系统识别
<a href=#103 id=103 data-nosnippet>103</a>        </span>target_info.os_info = <span class="self">self</span>.identify_os(ip).<span class="kw">await</span>.ok();
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>        <span class="comment">// 漏洞扫描
<a href=#106 id=106 data-nosnippet>106</a>        </span>target_info.vulnerabilities = <span class="self">self</span>.scan_vulnerabilities(ip, <span class="kw-2">&amp;</span>target_info.services).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a>        <span class="prelude-val">Ok</span>(target_info)
<a href=#109 id=109 data-nosnippet>109</a>    }
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>    <span class="kw">async fn </span>discover_hosts(<span class="kw-2">&amp;</span><span class="self">self</span>, network: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;IpAddr&gt;&gt; {
<a href=#112 id=112 data-nosnippet>112</a>        <span class="comment">// 使用ping扫描发现活跃主机
<a href=#113 id=113 data-nosnippet>113</a>        </span><span class="kw">let </span>output = AsyncCommand::new(<span class="string">"nmap"</span>)
<a href=#114 id=114 data-nosnippet>114</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"-sn"</span>, network])
<a href=#115 id=115 data-nosnippet>115</a>            .output()
<a href=#116 id=116 data-nosnippet>116</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a>        <span class="kw">if </span>!output.status.success() {
<a href=#119 id=119 data-nosnippet>119</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Host discovery failed"</span>));
<a href=#120 id=120 data-nosnippet>120</a>        }
<a href=#121 id=121 data-nosnippet>121</a>
<a href=#122 id=122 data-nosnippet>122</a>        <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#123 id=123 data-nosnippet>123</a>        <span class="kw">let </span><span class="kw-2">mut </span>hosts = Vec::new();
<a href=#124 id=124 data-nosnippet>124</a>
<a href=#125 id=125 data-nosnippet>125</a>        <span class="comment">// 解析nmap输出
<a href=#126 id=126 data-nosnippet>126</a>        </span><span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#127 id=127 data-nosnippet>127</a>            <span class="kw">if </span>line.contains(<span class="string">"Nmap scan report for"</span>) {
<a href=#128 id=128 data-nosnippet>128</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(ip_str) = line.split_whitespace().last() {
<a href=#129 id=129 data-nosnippet>129</a>                    <span class="kw">if let </span><span class="prelude-val">Ok</span>(ip) = ip_str.parse::&lt;IpAddr&gt;() {
<a href=#130 id=130 data-nosnippet>130</a>                        hosts.push(ip);
<a href=#131 id=131 data-nosnippet>131</a>                    }
<a href=#132 id=132 data-nosnippet>132</a>                }
<a href=#133 id=133 data-nosnippet>133</a>            }
<a href=#134 id=134 data-nosnippet>134</a>        }
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>        <span class="prelude-val">Ok</span>(hosts)
<a href=#137 id=137 data-nosnippet>137</a>    }
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>    <span class="kw">async fn </span>resolve_hostname(<span class="kw-2">&amp;</span><span class="self">self</span>, ip: IpAddr) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#140 id=140 data-nosnippet>140</a>        <span class="kw">let </span>output = AsyncCommand::new(<span class="string">"nslookup"</span>)
<a href=#141 id=141 data-nosnippet>141</a>            .arg(ip.to_string())
<a href=#142 id=142 data-nosnippet>142</a>            .output()
<a href=#143 id=143 data-nosnippet>143</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#144 id=144 data-nosnippet>144</a>
<a href=#145 id=145 data-nosnippet>145</a>        <span class="kw">if </span>output.status.success() {
<a href=#146 id=146 data-nosnippet>146</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#147 id=147 data-nosnippet>147</a>            <span class="comment">// 解析nslookup输出获取主机名
<a href=#148 id=148 data-nosnippet>148</a>            </span><span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#149 id=149 data-nosnippet>149</a>                <span class="kw">if </span>line.contains(<span class="string">"name ="</span>) {
<a href=#150 id=150 data-nosnippet>150</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(hostname) = line.split(<span class="string">"name ="</span>).nth(<span class="number">1</span>) {
<a href=#151 id=151 data-nosnippet>151</a>                        <span class="kw">return </span><span class="prelude-val">Ok</span>(hostname.trim().trim_end_matches(<span class="string">'.'</span>).to_string());
<a href=#152 id=152 data-nosnippet>152</a>                    }
<a href=#153 id=153 data-nosnippet>153</a>                }
<a href=#154 id=154 data-nosnippet>154</a>            }
<a href=#155 id=155 data-nosnippet>155</a>        }
<a href=#156 id=156 data-nosnippet>156</a>
<a href=#157 id=157 data-nosnippet>157</a>        <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Hostname resolution failed"</span>))
<a href=#158 id=158 data-nosnippet>158</a>    }
<a href=#159 id=159 data-nosnippet>159</a>
<a href=#160 id=160 data-nosnippet>160</a>    <span class="kw">async fn </span>scan_ports(<span class="kw-2">&amp;</span><span class="self">self</span>, ip: IpAddr) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;PortInfo&gt;&gt; {
<a href=#161 id=161 data-nosnippet>161</a>        <span class="kw">let </span>output = AsyncCommand::new(<span class="string">"nmap"</span>)
<a href=#162 id=162 data-nosnippet>162</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"-sS"</span>, <span class="string">"-O"</span>, <span class="string">"-sV"</span>, <span class="kw-2">&amp;</span>ip.to_string()])
<a href=#163 id=163 data-nosnippet>163</a>            .output()
<a href=#164 id=164 data-nosnippet>164</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>        <span class="kw">if </span>!output.status.success() {
<a href=#167 id=167 data-nosnippet>167</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Port scan failed"</span>));
<a href=#168 id=168 data-nosnippet>168</a>        }
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>        <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#171 id=171 data-nosnippet>171</a>        <span class="kw">let </span><span class="kw-2">mut </span>ports = Vec::new();
<a href=#172 id=172 data-nosnippet>172</a>
<a href=#173 id=173 data-nosnippet>173</a>        <span class="comment">// 解析nmap输出
<a href=#174 id=174 data-nosnippet>174</a>        </span><span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#175 id=175 data-nosnippet>175</a>            <span class="kw">if </span>line.contains(<span class="string">"/tcp"</span>) || line.contains(<span class="string">"/udp"</span>) {
<a href=#176 id=176 data-nosnippet>176</a>                <span class="kw">let </span>parts: Vec&lt;<span class="kw-2">&amp;</span>str&gt; = line.split_whitespace().collect();
<a href=#177 id=177 data-nosnippet>177</a>                <span class="kw">if </span>parts.len() &gt;= <span class="number">3 </span>{
<a href=#178 id=178 data-nosnippet>178</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(port_proto) = parts[<span class="number">0</span>].split(<span class="string">'/'</span>).next() {
<a href=#179 id=179 data-nosnippet>179</a>                        <span class="kw">if let </span><span class="prelude-val">Ok</span>(port) = port_proto.parse::&lt;u16&gt;() {
<a href=#180 id=180 data-nosnippet>180</a>                            <span class="kw">let </span>protocol = <span class="kw">if </span>line.contains(<span class="string">"/tcp"</span>) { <span class="string">"tcp" </span>} <span class="kw">else </span>{ <span class="string">"udp" </span>};
<a href=#181 id=181 data-nosnippet>181</a>                            <span class="kw">let </span>state = parts[<span class="number">1</span>].to_string();
<a href=#182 id=182 data-nosnippet>182</a>                            <span class="kw">let </span>service = <span class="kw">if </span>parts.len() &gt; <span class="number">2 </span>{
<a href=#183 id=183 data-nosnippet>183</a>                                <span class="prelude-val">Some</span>(parts[<span class="number">2</span>].to_string())
<a href=#184 id=184 data-nosnippet>184</a>                            } <span class="kw">else </span>{
<a href=#185 id=185 data-nosnippet>185</a>                                <span class="prelude-val">None
<a href=#186 id=186 data-nosnippet>186</a>                            </span>};
<a href=#187 id=187 data-nosnippet>187</a>
<a href=#188 id=188 data-nosnippet>188</a>                            ports.push(PortInfo {
<a href=#189 id=189 data-nosnippet>189</a>                                port,
<a href=#190 id=190 data-nosnippet>190</a>                                protocol: protocol.to_string(),
<a href=#191 id=191 data-nosnippet>191</a>                                state,
<a href=#192 id=192 data-nosnippet>192</a>                                service,
<a href=#193 id=193 data-nosnippet>193</a>                                version: <span class="prelude-val">None</span>,
<a href=#194 id=194 data-nosnippet>194</a>                            });
<a href=#195 id=195 data-nosnippet>195</a>                        }
<a href=#196 id=196 data-nosnippet>196</a>                    }
<a href=#197 id=197 data-nosnippet>197</a>                }
<a href=#198 id=198 data-nosnippet>198</a>            }
<a href=#199 id=199 data-nosnippet>199</a>        }
<a href=#200 id=200 data-nosnippet>200</a>
<a href=#201 id=201 data-nosnippet>201</a>        <span class="prelude-val">Ok</span>(ports)
<a href=#202 id=202 data-nosnippet>202</a>    }
<a href=#203 id=203 data-nosnippet>203</a>
<a href=#204 id=204 data-nosnippet>204</a>    <span class="kw">async fn </span>identify_services(<span class="kw-2">&amp;</span><span class="self">self</span>, ip: IpAddr, ports: <span class="kw-2">&amp;</span>[PortInfo]) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;ServiceInfo&gt;&gt; {
<a href=#205 id=205 data-nosnippet>205</a>        <span class="kw">let </span><span class="kw-2">mut </span>services = Vec::new();
<a href=#206 id=206 data-nosnippet>206</a>
<a href=#207 id=207 data-nosnippet>207</a>        <span class="kw">for </span>port_info <span class="kw">in </span>ports {
<a href=#208 id=208 data-nosnippet>208</a>            <span class="kw">if </span>port_info.state == <span class="string">"open" </span>{
<a href=#209 id=209 data-nosnippet>209</a>                <span class="comment">// 尝试连接并获取banner
<a href=#210 id=210 data-nosnippet>210</a>                </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(banner) = <span class="self">self</span>.get_service_banner(ip, port_info.port).<span class="kw">await </span>{
<a href=#211 id=211 data-nosnippet>211</a>                    <span class="kw">let </span>service = ServiceInfo {
<a href=#212 id=212 data-nosnippet>212</a>                        name: port_info
<a href=#213 id=213 data-nosnippet>213</a>                            .service
<a href=#214 id=214 data-nosnippet>214</a>                            .clone()
<a href=#215 id=215 data-nosnippet>215</a>                            .unwrap_or_else(|| <span class="string">"unknown"</span>.to_string()),
<a href=#216 id=216 data-nosnippet>216</a>                        version: <span class="string">"unknown"</span>.to_string(),
<a href=#217 id=217 data-nosnippet>217</a>                        port: port_info.port,
<a href=#218 id=218 data-nosnippet>218</a>                        banner: <span class="prelude-val">Some</span>(banner),
<a href=#219 id=219 data-nosnippet>219</a>                    };
<a href=#220 id=220 data-nosnippet>220</a>                    services.push(service);
<a href=#221 id=221 data-nosnippet>221</a>                }
<a href=#222 id=222 data-nosnippet>222</a>            }
<a href=#223 id=223 data-nosnippet>223</a>        }
<a href=#224 id=224 data-nosnippet>224</a>
<a href=#225 id=225 data-nosnippet>225</a>        <span class="prelude-val">Ok</span>(services)
<a href=#226 id=226 data-nosnippet>226</a>    }
<a href=#227 id=227 data-nosnippet>227</a>
<a href=#228 id=228 data-nosnippet>228</a>    <span class="kw">async fn </span>get_service_banner(<span class="kw-2">&amp;</span><span class="self">self</span>, ip: IpAddr, port: u16) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#229 id=229 data-nosnippet>229</a>        <span class="comment">// 使用netcat获取服务banner
<a href=#230 id=230 data-nosnippet>230</a>        </span><span class="kw">let </span>output = AsyncCommand::new(<span class="string">"nc"</span>)
<a href=#231 id=231 data-nosnippet>231</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"-w"</span>, <span class="string">"3"</span>, <span class="kw-2">&amp;</span>ip.to_string(), <span class="kw-2">&amp;</span>port.to_string()])
<a href=#232 id=232 data-nosnippet>232</a>            .output()
<a href=#233 id=233 data-nosnippet>233</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#234 id=234 data-nosnippet>234</a>
<a href=#235 id=235 data-nosnippet>235</a>        <span class="kw">if </span>output.status.success() {
<a href=#236 id=236 data-nosnippet>236</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#237 id=237 data-nosnippet>237</a>            <span class="prelude-val">Ok</span>(stdout.lines().next().unwrap_or(<span class="string">""</span>).to_string())
<a href=#238 id=238 data-nosnippet>238</a>        } <span class="kw">else </span>{
<a href=#239 id=239 data-nosnippet>239</a>            <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Failed to get banner"</span>))
<a href=#240 id=240 data-nosnippet>240</a>        }
<a href=#241 id=241 data-nosnippet>241</a>    }
<a href=#242 id=242 data-nosnippet>242</a>
<a href=#243 id=243 data-nosnippet>243</a>    <span class="kw">async fn </span>identify_os(<span class="kw-2">&amp;</span><span class="self">self</span>, ip: IpAddr) -&gt; <span class="prelude-ty">Result</span>&lt;OsInfo&gt; {
<a href=#244 id=244 data-nosnippet>244</a>        <span class="kw">let </span>output = AsyncCommand::new(<span class="string">"nmap"</span>)
<a href=#245 id=245 data-nosnippet>245</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"-O"</span>, <span class="kw-2">&amp;</span>ip.to_string()])
<a href=#246 id=246 data-nosnippet>246</a>            .output()
<a href=#247 id=247 data-nosnippet>247</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#248 id=248 data-nosnippet>248</a>
<a href=#249 id=249 data-nosnippet>249</a>        <span class="kw">if </span>!output.status.success() {
<a href=#250 id=250 data-nosnippet>250</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"OS detection failed"</span>));
<a href=#251 id=251 data-nosnippet>251</a>        }
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>        <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>        <span class="comment">// 解析OS信息
<a href=#256 id=256 data-nosnippet>256</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>os_name = <span class="string">"Unknown"</span>.to_string();
<a href=#257 id=257 data-nosnippet>257</a>        <span class="kw">let </span><span class="kw-2">mut </span>os_version = <span class="string">"Unknown"</span>.to_string();
<a href=#258 id=258 data-nosnippet>258</a>
<a href=#259 id=259 data-nosnippet>259</a>        <span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#260 id=260 data-nosnippet>260</a>            <span class="kw">if </span>line.contains(<span class="string">"Running:"</span>) {
<a href=#261 id=261 data-nosnippet>261</a>                os_name = line.replace(<span class="string">"Running:"</span>, <span class="string">""</span>).trim().to_string();
<a href=#262 id=262 data-nosnippet>262</a>            } <span class="kw">else if </span>line.contains(<span class="string">"OS details:"</span>) {
<a href=#263 id=263 data-nosnippet>263</a>                os_version = line.replace(<span class="string">"OS details:"</span>, <span class="string">""</span>).trim().to_string();
<a href=#264 id=264 data-nosnippet>264</a>            }
<a href=#265 id=265 data-nosnippet>265</a>        }
<a href=#266 id=266 data-nosnippet>266</a>
<a href=#267 id=267 data-nosnippet>267</a>        <span class="prelude-val">Ok</span>(OsInfo {
<a href=#268 id=268 data-nosnippet>268</a>            name: os_name,
<a href=#269 id=269 data-nosnippet>269</a>            version: os_version,
<a href=#270 id=270 data-nosnippet>270</a>            architecture: <span class="string">"x86_64"</span>.to_string(), <span class="comment">// 默认值
<a href=#271 id=271 data-nosnippet>271</a>            </span>kernel_version: <span class="string">"Unknown"</span>.to_string(),
<a href=#272 id=272 data-nosnippet>272</a>        })
<a href=#273 id=273 data-nosnippet>273</a>    }
<a href=#274 id=274 data-nosnippet>274</a>
<a href=#275 id=275 data-nosnippet>275</a>    <span class="kw">async fn </span>scan_vulnerabilities(
<a href=#276 id=276 data-nosnippet>276</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#277 id=277 data-nosnippet>277</a>        ip: IpAddr,
<a href=#278 id=278 data-nosnippet>278</a>        services: <span class="kw-2">&amp;</span>[ServiceInfo],
<a href=#279 id=279 data-nosnippet>279</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;VulnerabilityInfo&gt;&gt; {
<a href=#280 id=280 data-nosnippet>280</a>        <span class="kw">let </span><span class="kw-2">mut </span>vulnerabilities = Vec::new();
<a href=#281 id=281 data-nosnippet>281</a>
<a href=#282 id=282 data-nosnippet>282</a>        <span class="comment">// 使用nmap脚本扫描漏洞
<a href=#283 id=283 data-nosnippet>283</a>        </span><span class="kw">let </span>output = AsyncCommand::new(<span class="string">"nmap"</span>)
<a href=#284 id=284 data-nosnippet>284</a>            .args(<span class="kw-2">&amp;</span>[<span class="string">"--script"</span>, <span class="string">"vuln"</span>, <span class="kw-2">&amp;</span>ip.to_string()])
<a href=#285 id=285 data-nosnippet>285</a>            .output()
<a href=#286 id=286 data-nosnippet>286</a>            .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#287 id=287 data-nosnippet>287</a>
<a href=#288 id=288 data-nosnippet>288</a>        <span class="kw">if </span>output.status.success() {
<a href=#289 id=289 data-nosnippet>289</a>            <span class="kw">let </span>stdout = String::from_utf8_lossy(<span class="kw-2">&amp;</span>output.stdout);
<a href=#290 id=290 data-nosnippet>290</a>
<a href=#291 id=291 data-nosnippet>291</a>            <span class="comment">// 解析漏洞信息
<a href=#292 id=292 data-nosnippet>292</a>            </span><span class="kw">for </span>line <span class="kw">in </span>stdout.lines() {
<a href=#293 id=293 data-nosnippet>293</a>                <span class="kw">if </span>line.contains(<span class="string">"CVE-"</span>) {
<a href=#294 id=294 data-nosnippet>294</a>                    <span class="comment">// 提取CVE信息
<a href=#295 id=295 data-nosnippet>295</a>                    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(cve_start) = line.find(<span class="string">"CVE-"</span>) {
<a href=#296 id=296 data-nosnippet>296</a>                        <span class="kw">if let </span><span class="prelude-val">Some</span>(cve_end) = line[cve_start..].find(<span class="string">' '</span>) {
<a href=#297 id=297 data-nosnippet>297</a>                            <span class="kw">let </span>cve_id = line[cve_start..cve_start + cve_end].to_string();
<a href=#298 id=298 data-nosnippet>298</a>
<a href=#299 id=299 data-nosnippet>299</a>                            vulnerabilities.push(VulnerabilityInfo {
<a href=#300 id=300 data-nosnippet>300</a>                                cve_id,
<a href=#301 id=301 data-nosnippet>301</a>                                severity: <span class="string">"Medium"</span>.to_string(), <span class="comment">// 默认值
<a href=#302 id=302 data-nosnippet>302</a>                                </span>description: line.trim().to_string(),
<a href=#303 id=303 data-nosnippet>303</a>                                affected_service: <span class="string">"Unknown"</span>.to_string(),
<a href=#304 id=304 data-nosnippet>304</a>                            });
<a href=#305 id=305 data-nosnippet>305</a>                        }
<a href=#306 id=306 data-nosnippet>306</a>                    }
<a href=#307 id=307 data-nosnippet>307</a>                }
<a href=#308 id=308 data-nosnippet>308</a>            }
<a href=#309 id=309 data-nosnippet>309</a>        }
<a href=#310 id=310 data-nosnippet>310</a>
<a href=#311 id=311 data-nosnippet>311</a>        <span class="prelude-val">Ok</span>(vulnerabilities)
<a href=#312 id=312 data-nosnippet>312</a>    }
<a href=#313 id=313 data-nosnippet>313</a>
<a href=#314 id=314 data-nosnippet>314</a>    <span class="kw">pub fn </span>get_target_info(<span class="kw-2">&amp;</span><span class="self">self</span>, ip: IpAddr) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>TargetInfo&gt; {
<a href=#315 id=315 data-nosnippet>315</a>        <span class="self">self</span>.targets.get(<span class="kw-2">&amp;</span>ip)
<a href=#316 id=316 data-nosnippet>316</a>    }
<a href=#317 id=317 data-nosnippet>317</a>
<a href=#318 id=318 data-nosnippet>318</a>    <span class="kw">pub fn </span>get_all_targets(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;<span class="kw-2">&amp;</span>TargetInfo&gt; {
<a href=#319 id=319 data-nosnippet>319</a>        <span class="self">self</span>.targets.values().collect()
<a href=#320 id=320 data-nosnippet>320</a>    }
<a href=#321 id=321 data-nosnippet>321</a>
<a href=#322 id=322 data-nosnippet>322</a>    <span class="kw">pub fn </span>export_results(<span class="kw-2">&amp;</span><span class="self">self</span>, format: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#323 id=323 data-nosnippet>323</a>        <span class="kw">match </span>format {
<a href=#324 id=324 data-nosnippet>324</a>            <span class="string">"json" </span>=&gt; <span class="prelude-val">Ok</span>(serde_json::to_string_pretty(<span class="kw-2">&amp;</span><span class="self">self</span>.targets)<span class="question-mark">?</span>),
<a href=#325 id=325 data-nosnippet>325</a>            <span class="string">"csv" </span>=&gt; <span class="self">self</span>.export_csv(),
<a href=#326 id=326 data-nosnippet>326</a>            <span class="kw">_ </span>=&gt; <span class="prelude-val">Err</span>(<span class="macro">anyhow!</span>(<span class="string">"Unsupported export format: {}"</span>, format)),
<a href=#327 id=327 data-nosnippet>327</a>        }
<a href=#328 id=328 data-nosnippet>328</a>    }
<a href=#329 id=329 data-nosnippet>329</a>
<a href=#330 id=330 data-nosnippet>330</a>    <span class="kw">fn </span>export_csv(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#331 id=331 data-nosnippet>331</a>        <span class="kw">let </span><span class="kw-2">mut </span>csv = String::from(<span class="string">"IP,Hostname,OS,Open Ports,Services,Vulnerabilities\n"</span>);
<a href=#332 id=332 data-nosnippet>332</a>
<a href=#333 id=333 data-nosnippet>333</a>        <span class="kw">for </span>target <span class="kw">in </span><span class="self">self</span>.targets.values() {
<a href=#334 id=334 data-nosnippet>334</a>            <span class="kw">let </span>hostname = target.hostname.as_deref().unwrap_or(<span class="string">"Unknown"</span>);
<a href=#335 id=335 data-nosnippet>335</a>            <span class="kw">let </span>os = target
<a href=#336 id=336 data-nosnippet>336</a>                .os_info
<a href=#337 id=337 data-nosnippet>337</a>                .as_ref()
<a href=#338 id=338 data-nosnippet>338</a>                .map(|os| <span class="macro">format!</span>(<span class="string">"{} {}"</span>, os.name, os.version))
<a href=#339 id=339 data-nosnippet>339</a>                .unwrap_or_else(|| <span class="string">"Unknown"</span>.to_string());
<a href=#340 id=340 data-nosnippet>340</a>            <span class="kw">let </span>ports = target
<a href=#341 id=341 data-nosnippet>341</a>                .open_ports
<a href=#342 id=342 data-nosnippet>342</a>                .iter()
<a href=#343 id=343 data-nosnippet>343</a>                .map(|p| p.port.to_string())
<a href=#344 id=344 data-nosnippet>344</a>                .collect::&lt;Vec&lt;<span class="kw">_</span>&gt;&gt;()
<a href=#345 id=345 data-nosnippet>345</a>                .join(<span class="string">";"</span>);
<a href=#346 id=346 data-nosnippet>346</a>            <span class="kw">let </span>services = target
<a href=#347 id=347 data-nosnippet>347</a>                .services
<a href=#348 id=348 data-nosnippet>348</a>                .iter()
<a href=#349 id=349 data-nosnippet>349</a>                .map(|s| <span class="macro">format!</span>(<span class="string">"{}:{}"</span>, s.name, s.port))
<a href=#350 id=350 data-nosnippet>350</a>                .collect::&lt;Vec&lt;<span class="kw">_</span>&gt;&gt;()
<a href=#351 id=351 data-nosnippet>351</a>                .join(<span class="string">";"</span>);
<a href=#352 id=352 data-nosnippet>352</a>            <span class="kw">let </span>vulns = target
<a href=#353 id=353 data-nosnippet>353</a>                .vulnerabilities
<a href=#354 id=354 data-nosnippet>354</a>                .iter()
<a href=#355 id=355 data-nosnippet>355</a>                .map(|v| v.cve_id.as_str())
<a href=#356 id=356 data-nosnippet>356</a>                .collect::&lt;Vec&lt;<span class="kw">_</span>&gt;&gt;()
<a href=#357 id=357 data-nosnippet>357</a>                .join(<span class="string">";"</span>);
<a href=#358 id=358 data-nosnippet>358</a>
<a href=#359 id=359 data-nosnippet>359</a>            csv.push_str(<span class="kw-2">&amp;</span><span class="macro">format!</span>(
<a href=#360 id=360 data-nosnippet>360</a>                <span class="string">"{},{},{},{},{},{}\n"</span>,
<a href=#361 id=361 data-nosnippet>361</a>                target.ip_address, hostname, os, ports, services, vulns
<a href=#362 id=362 data-nosnippet>362</a>            ));
<a href=#363 id=363 data-nosnippet>363</a>        }
<a href=#364 id=364 data-nosnippet>364</a>
<a href=#365 id=365 data-nosnippet>365</a>        <span class="prelude-val">Ok</span>(csv)
<a href=#366 id=366 data-nosnippet>366</a>    }
<a href=#367 id=367 data-nosnippet>367</a>}</code></pre></div></section></main></body></html>