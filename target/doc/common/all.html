<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="List of all items in this crate"><title>List of all items in this crate</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../static.files/storage-82c7156e.js"></script><script defer src="../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod sys"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../common/index.html">common</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h3><a href="#structs">Crate Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><h1>List of all items</h1><h3 id="structs">Structs</h3><ul class="all-items"><li><a href="auth/struct.AuthManager.html">auth::AuthManager</a></li><li><a href="auth/struct.AuthToken.html">auth::AuthToken</a></li><li><a href="auth/struct.ClientInfo.html">auth::ClientInfo</a></li><li><a href="auth/struct.LoginRequest.html">auth::LoginRequest</a></li><li><a href="auth/struct.LoginResponse.html">auth::LoginResponse</a></li><li><a href="communication/struct.HttpCommunicator.html">communication::HttpCommunicator</a></li><li><a href="crypto/struct.CryptoManager.html">crypto::CryptoManager</a></li><li><a href="logging/struct.LogEntry.html">logging::LogEntry</a></li><li><a href="logging/struct.Logger.html">logging::Logger</a></li><li><a href="protocol/struct.CommandMessage.html">protocol::CommandMessage</a></li><li><a href="protocol/struct.CommandResult.html">protocol::CommandResult</a></li><li><a href="protocol/struct.DiskInfo.html">protocol::DiskInfo</a></li><li><a href="protocol/struct.ErrorMessage.html">protocol::ErrorMessage</a></li><li><a href="protocol/struct.FileData.html">protocol::FileData</a></li><li><a href="protocol/struct.FileDownloadMessage.html">protocol::FileDownloadMessage</a></li><li><a href="protocol/struct.FileUploadMessage.html">protocol::FileUploadMessage</a></li><li><a href="protocol/struct.Message.html">protocol::Message</a></li><li><a href="protocol/struct.NetworkInterface.html">protocol::NetworkInterface</a></li><li><a href="protocol/struct.ProcessDetails.html">protocol::ProcessDetails</a></li><li><a href="protocol/struct.ProcessInfo.html">protocol::ProcessInfo</a></li><li><a href="protocol/struct.ProcessMessage.html">protocol::ProcessMessage</a></li><li><a href="protocol/struct.SystemInfoResult.html">protocol::SystemInfoResult</a></li><li><a href="tls/struct.CertificateInfo.html">tls::CertificateInfo</a></li><li><a href="tls/struct.TlsConfig.html">tls::TlsConfig</a></li><li><a href="tls/struct.TlsManager.html">tls::TlsManager</a></li></ul><h3 id="enums">Enums</h3><ul class="all-items"><li><a href="auth/enum.Permission.html">auth::Permission</a></li><li><a href="logging/enum.LogCategory.html">logging::LogCategory</a></li><li><a href="logging/enum.LogLevel.html">logging::LogLevel</a></li><li><a href="protocol/enum.MessageType.html">protocol::MessageType</a></li><li><a href="protocol/enum.ProcessAction.html">protocol::ProcessAction</a></li></ul><h3 id="functions">Functions</h3><ul class="all-items"><li><a href="auth/fn.authenticate_user.html">auth::authenticate_user</a></li></ul></section></div></main></body></html>