<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `protocol` mod in crate `common`."><title>common::protocol - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="common" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../common/index.html">common</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module protocol</a></h2><h3><a href="#structs">Module Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="../index.html">In crate common</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">common</a></div><h1>Module <span>protocol</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/common/protocol.rs.html#1-159">Source</a> </span></div><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.CommandMessage.html" title="struct common::protocol::CommandMessage">Command<wbr>Message</a></dt><dt><a class="struct" href="struct.CommandResult.html" title="struct common::protocol::CommandResult">Command<wbr>Result</a></dt><dt><a class="struct" href="struct.DiskInfo.html" title="struct common::protocol::DiskInfo">Disk<wbr>Info</a></dt><dt><a class="struct" href="struct.ErrorMessage.html" title="struct common::protocol::ErrorMessage">Error<wbr>Message</a></dt><dt><a class="struct" href="struct.FileData.html" title="struct common::protocol::FileData">File<wbr>Data</a></dt><dt><a class="struct" href="struct.FileDownloadMessage.html" title="struct common::protocol::FileDownloadMessage">File<wbr>Download<wbr>Message</a></dt><dt><a class="struct" href="struct.FileUploadMessage.html" title="struct common::protocol::FileUploadMessage">File<wbr>Upload<wbr>Message</a></dt><dt><a class="struct" href="struct.Message.html" title="struct common::protocol::Message">Message</a></dt><dt><a class="struct" href="struct.NetworkInterface.html" title="struct common::protocol::NetworkInterface">Network<wbr>Interface</a></dt><dt><a class="struct" href="struct.ProcessDetails.html" title="struct common::protocol::ProcessDetails">Process<wbr>Details</a></dt><dt><a class="struct" href="struct.ProcessInfo.html" title="struct common::protocol::ProcessInfo">Process<wbr>Info</a></dt><dt><a class="struct" href="struct.ProcessMessage.html" title="struct common::protocol::ProcessMessage">Process<wbr>Message</a></dt><dt><a class="struct" href="struct.SystemInfoResult.html" title="struct common::protocol::SystemInfoResult">System<wbr>Info<wbr>Result</a></dt></dl><h2 id="enums" class="section-header">Enums<a href="#enums" class="anchor">§</a></h2><dl class="item-table"><dt><a class="enum" href="enum.MessageType.html" title="enum common::protocol::MessageType">Message<wbr>Type</a></dt><dt><a class="enum" href="enum.ProcessAction.html" title="enum common::protocol::ProcessAction">Process<wbr>Action</a></dt></dl></section></div></main></body></html>