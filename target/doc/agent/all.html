<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="List of all items in this crate"><title>List of all items in this crate</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="agent" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../static.files/storage-82c7156e.js"></script><script defer src="../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod sys"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../agent/index.html">agent</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h3><a href="#structs">Crate Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><h1>List of all items</h1><h3 id="structs">Structs</h3><ul class="all-items"><li><a href="struct.Cli.html">Cli</a></li><li><a href="agent/struct.Agent.html">agent::Agent</a></li><li><a href="elf_loader/struct.ElfInfo.html">elf_loader::ElfInfo</a></li><li><a href="elf_loader/struct.ElfLoader.html">elf_loader::ElfLoader</a></li><li><a href="executor/struct.CommandExecutor.html">executor::CommandExecutor</a></li><li><a href="file_manager/struct.FileInfo.html">file_manager::FileInfo</a></li><li><a href="file_manager/struct.FileManager.html">file_manager::FileManager</a></li><li><a href="file_manager/struct.TempFileInfo.html">file_manager::TempFileInfo</a></li><li><a href="system_info/struct.SystemInfoCollector.html">system_info::SystemInfoCollector</a></li></ul><h3 id="enums">Enums</h3><ul class="all-items"><li><a href="enum.Commands.html">Commands</a></li><li><a href="file_manager/enum.FileType.html">file_manager::FileType</a></li></ul><h3 id="functions">Functions</h3><ul class="all-items"><li><a href="fn.check_launchd_status.html">check_launchd_status</a></li><li><a href="fn.check_status.html">check_status</a></li><li><a href="fn.daemonize.html">daemonize</a></li><li><a href="fn.install_launchd_service.html">install_launchd_service</a></li><li><a href="fn.install_service.html">install_service</a></li><li><a href="fn.main.html">main</a></li><li><a href="fn.uninstall_launchd_service.html">uninstall_launchd_service</a></li><li><a href="fn.uninstall_service.html">uninstall_service</a></li></ul></section></div></main></body></html>