(function() {
    var implementors = Object.fromEntries([["agent",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::<PERSON>lone\"><PERSON><PERSON></a> for <a class=\"enum\" href=\"agent/enum.Commands.html\" title=\"enum agent::Commands\">Commands</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::<PERSON>lone\">Clone</a> for <a class=\"enum\" href=\"agent/file_manager/enum.FileType.html\" title=\"enum agent::file_manager::FileType\">FileType</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::<PERSON><PERSON>\"><PERSON><PERSON></a> for <a class=\"struct\" href=\"agent/file_manager/struct.FileInfo.html\" title=\"struct agent::file_manager::FileInfo\">FileInfo</a>"]]],["common",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"common/auth/enum.Permission.html\" title=\"enum common::auth::Permission\">Permission</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"common/logging/enum.LogCategory.html\" title=\"enum common::logging::LogCategory\">LogCategory</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"common/logging/enum.LogLevel.html\" title=\"enum common::logging::LogLevel\">LogLevel</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"common/protocol/enum.MessageType.html\" title=\"enum common::protocol::MessageType\">MessageType</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"common/protocol/enum.ProcessAction.html\" title=\"enum common::protocol::ProcessAction\">ProcessAction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/auth/struct.AuthManager.html\" title=\"struct common::auth::AuthManager\">AuthManager</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/auth/struct.AuthToken.html\" title=\"struct common::auth::AuthToken\">AuthToken</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/communication/struct.HttpCommunicator.html\" title=\"struct common::communication::HttpCommunicator\">HttpCommunicator</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/crypto/struct.CryptoManager.html\" title=\"struct common::crypto::CryptoManager\">CryptoManager</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/logging/struct.LogEntry.html\" title=\"struct common::logging::LogEntry\">LogEntry</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.CommandMessage.html\" title=\"struct common::protocol::CommandMessage\">CommandMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.CommandResult.html\" title=\"struct common::protocol::CommandResult\">CommandResult</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.DiskInfo.html\" title=\"struct common::protocol::DiskInfo\">DiskInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.ErrorMessage.html\" title=\"struct common::protocol::ErrorMessage\">ErrorMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.FileData.html\" title=\"struct common::protocol::FileData\">FileData</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.FileDownloadMessage.html\" title=\"struct common::protocol::FileDownloadMessage\">FileDownloadMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.FileUploadMessage.html\" title=\"struct common::protocol::FileUploadMessage\">FileUploadMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.Message.html\" title=\"struct common::protocol::Message\">Message</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.NetworkInterface.html\" title=\"struct common::protocol::NetworkInterface\">NetworkInterface</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.ProcessDetails.html\" title=\"struct common::protocol::ProcessDetails\">ProcessDetails</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.ProcessInfo.html\" title=\"struct common::protocol::ProcessInfo\">ProcessInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.ProcessMessage.html\" title=\"struct common::protocol::ProcessMessage\">ProcessMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/protocol/struct.SystemInfoResult.html\" title=\"struct common::protocol::SystemInfoResult\">SystemInfoResult</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"common/tls/struct.CertificateInfo.html\" title=\"struct common::tls::CertificateInfo\">CertificateInfo</a>"]]],["controller",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"controller/controller/enum.AgentStatus.html\" title=\"enum controller::controller::AgentStatus\">AgentStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"controller/controller/struct.AgentInfo.html\" title=\"struct controller::controller::AgentInfo\">AgentInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"controller/reconnaissance/struct.OsInfo.html\" title=\"struct controller::reconnaissance::OsInfo\">OsInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"controller/reconnaissance/struct.PortInfo.html\" title=\"struct controller::reconnaissance::PortInfo\">PortInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"controller/reconnaissance/struct.ServiceInfo.html\" title=\"struct controller::reconnaissance::ServiceInfo\">ServiceInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"controller/reconnaissance/struct.TargetInfo.html\" title=\"struct controller::reconnaissance::TargetInfo\">TargetInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"controller/reconnaissance/struct.VulnerabilityInfo.html\" title=\"struct controller::reconnaissance::VulnerabilityInfo\">VulnerabilityInfo</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[800,6735,2073]}