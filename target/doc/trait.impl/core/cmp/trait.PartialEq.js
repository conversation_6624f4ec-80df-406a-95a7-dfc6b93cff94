(function() {
    var implementors = Object.fromEntries([["common",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a> for <a class=\"enum\" href=\"common/auth/enum.Permission.html\" title=\"enum common::auth::Permission\">Permission</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[282]}