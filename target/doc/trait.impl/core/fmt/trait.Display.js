(function() {
    var implementors = Object.fromEntries([["controller",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/fmt/trait.Display.html\" title=\"trait core::fmt::Display\">Display</a> for <a class=\"enum\" href=\"controller/controller/enum.AgentStatus.html\" title=\"enum controller::controller::AgentStatus\">AgentStatus</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[303]}