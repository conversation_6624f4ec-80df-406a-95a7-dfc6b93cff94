(function() {
    var implementors = Object.fromEntries([["agent",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"agent/elf_loader/struct.ElfInfo.html\" title=\"struct agent::elf_loader::ElfInfo\">ElfInfo</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[290]}