(function() {
    var implementors = Object.fromEntries([["agent",[["impl Args for <a class=\"struct\" href=\"agent/struct.Cli.html\" title=\"struct agent::Cli\">Cli</a>"]]],["controller",[["impl Args for <a class=\"struct\" href=\"controller/struct.Cli.html\" title=\"struct controller::Cli\">Cli</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[116,132]}