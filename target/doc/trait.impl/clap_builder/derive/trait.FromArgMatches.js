(function() {
    var implementors = Object.fromEntries([["agent",[["impl FromArgMatches for <a class=\"enum\" href=\"agent/enum.Commands.html\" title=\"enum agent::Commands\">Commands</a>"],["impl FromArgMatches for <a class=\"struct\" href=\"agent/struct.Cli.html\" title=\"struct agent::Cli\">Cli</a>"]]],["controller",[["impl FromArgMatches for <a class=\"enum\" href=\"controller/enum.Commands.html\" title=\"enum controller::Commands\">Commands</a>"],["impl FromArgMatches for <a class=\"struct\" href=\"controller/struct.Cli.html\" title=\"struct controller::Cli\">Cli</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[250,276]}