(function() {
    var implementors = Object.fromEntries([["agent",[["impl Subcommand for <a class=\"enum\" href=\"agent/enum.Commands.html\" title=\"enum agent::Commands\">Commands</a>"]]],["controller",[["impl Subcommand for <a class=\"enum\" href=\"controller/enum.Commands.html\" title=\"enum controller::Commands\">Commands</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[131,147]}