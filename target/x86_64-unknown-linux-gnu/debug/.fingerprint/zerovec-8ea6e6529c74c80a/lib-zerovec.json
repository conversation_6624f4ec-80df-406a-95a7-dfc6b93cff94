{"rustc": 15497389221046826682, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 7111364811161841961, "deps": [[9620753569207166497, "zerovec_derive", false, 12457357911490132313], [10706449961930108323, "yoke", false, 12598996452300754063], [17046516144589451410, "zerofrom", false, 1095513435907962877]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/zerovec-8ea6e6529c74c80a/dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 13270707523875659407}