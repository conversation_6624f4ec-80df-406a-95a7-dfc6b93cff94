{"rustc": 15497389221046826682, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 2040997289075261528, "path": 13911930050628075899, "deps": [[99287295355353247, "data_encoding", false, 12092055012834146219], [3150220818285335163, "url", false, 4063601965239398606], [3712811570531045576, "byteorder", false, 2537801541688342734], [4359956005902820838, "utf8", false, 12292312776022107715], [5986029879202738730, "log", false, 6316864552605243978], [6163892036024256188, "httparse", false, 18274574267474452874], [8008191657135824715, "thiserror", false, 11707065795130067393], [9010263965687315507, "http", false, 17692354617958924355], [10724389056617919257, "sha1", false, 12884278453747990037], [13208667028893622512, "rand", false, 12895002062565812167], [16066129441945555748, "bytes", false, 11501074220354007908]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tungstenite-b498154627cf6c01/dep-lib-tungstenite", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}