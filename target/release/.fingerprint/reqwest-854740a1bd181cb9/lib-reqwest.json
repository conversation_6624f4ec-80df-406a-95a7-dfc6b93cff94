{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 1350602797799047379, "deps": [[40386456601120721, "percent_encoding", false, 13056066026272772757], [95042085696191081, "ipnet", false, 5791673304335136863], [264090853244900308, "sync_wrapper", false, 7167860641028376605], [784494742817713399, "tower_service", false, 4219875163079690543], [1044435446100926395, "hyper_rustls", false, 11020841664447347845], [1906322745568073236, "pin_project_lite", false, 2220881443612682196], [3150220818285335163, "url", false, 4063601965239398606], [3722963349756955755, "once_cell", false, 16404913605721102029], [4405182208873388884, "http", false, 8099292218548199256], [5986029879202738730, "log", false, 6316864552605243978], [7414427314941361239, "hyper", false, 10719191438066258432], [7620660491849607393, "futures_core", false, 17996673418544276400], [8915503303801890683, "http_body", false, 5268592315116741931], [9538054652646069845, "tokio", false, 11050141154789550860], [9689903380558560274, "serde", false, 15439079988477941960], [10229185211513642314, "mime", false, 14723661288955231533], [10629569228670356391, "futures_util", false, 2698025385165188469], [11107720164717273507, "system_configuration", false, 6174547190211941150], [11295624341523567602, "rustls", false, 683777396242574296], [13809605890706463735, "h2", false, 821615319911032090], [14564311161534545801, "encoding_rs", false, 64402110731690762], [15367738274754116744, "serde_json", false, 3901210029338496369], [16066129441945555748, "bytes", false, 11501074220354007908], [16311359161338405624, "rustls_pemfile", false, 2355740063497018293], [16542808166767769916, "serde_urlencoded", false, 10364604033362409976], [16622232390123975175, "tokio_rustls", false, 16239989160807783361], [17652733826348741533, "webpki_roots", false, 1452040205877046142], [18066890886671768183, "base64", false, 1264975470845131083]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-854740a1bd181cb9/dep-lib-reqwest", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}