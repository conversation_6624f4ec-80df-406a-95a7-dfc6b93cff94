{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 10402470647724236303, "deps": [[555019317135488525, "regex_automata", false, 11925503238277553806], [2779309023524819297, "aho_corasick", false, 9357691500223024940], [9408802513701742484, "regex_syntax", false, 2759525442241654793], [15932120279885307830, "memchr", false, 11633282477876169413]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-5c6b56b882a475e3/dep-lib-regex", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}