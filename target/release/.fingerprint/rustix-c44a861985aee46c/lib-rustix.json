{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 9298903534527576498, "path": 14512022105962168629, "deps": [[4684437522915235464, "libc", false, 12321759522719068347], [7896293946984509699, "bitflags", false, 5691515148525677204], [8253628577145923712, "libc_errno", false, 17967907886553556766], [12053020504183902936, "build_script_build", false, 3480203236663918939]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-c44a861985aee46c/dep-lib-rustix", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}