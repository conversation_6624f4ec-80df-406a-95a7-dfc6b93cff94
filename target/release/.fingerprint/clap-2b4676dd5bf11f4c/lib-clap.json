{"rustc": 15497389221046826682, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9656904095642909417, "path": 6049094194910101152, "deps": [[4925398738524877221, "clap_derive", false, 9262931479581539448], [14814905555676593471, "clap_builder", false, 3137226930767515185]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-2b4676dd5bf11f4c/dep-lib-clap", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}