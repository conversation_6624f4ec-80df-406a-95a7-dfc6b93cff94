{"rustc": 15497389221046826682, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 10882751587789703073, "deps": [[5820056977320921005, "anstream", false, 14328547419186562832], [9394696648929125047, "anstyle", false, 16102748493259787833], [11166530783118767604, "strsim", false, 16189256122230569118], [11649982696571033535, "clap_lex", false, 918601486249702683]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-ecea36d074beadd0/dep-lib-clap_builder", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}