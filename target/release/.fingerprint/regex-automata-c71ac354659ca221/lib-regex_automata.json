{"rustc": 15497389221046826682, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 2040997289075261528, "path": 2090420592701158852, "deps": [[2779309023524819297, "aho_corasick", false, 9357691500223024940], [9408802513701742484, "regex_syntax", false, 2759525442241654793], [15932120279885307830, "memchr", false, 11633282477876169413]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-automata-c71ac354659ca221/dep-lib-regex_automata", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}