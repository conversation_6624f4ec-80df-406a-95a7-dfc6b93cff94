{"rustc": 15497389221046826682, "features": "[\"connect\", \"default\", \"handshake\", \"stream\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "target": 2433367608443825, "profile": 2040997289075261528, "path": 12165220293968292212, "deps": [[5986029879202738730, "log", false, 6316864552605243978], [8258418851280347661, "tungstenite", false, 10935134843827260736], [9538054652646069845, "tokio", false, 11050141154789550860], [10629569228670356391, "futures_util", false, 2698025385165188469]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tokio-tungstenite-dc96f859c88993d7/dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}