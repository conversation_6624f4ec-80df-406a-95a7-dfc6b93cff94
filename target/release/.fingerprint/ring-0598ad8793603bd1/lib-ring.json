{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17591616432441575691, "profile": 2040997289075261528, "path": 6274624895143969252, "deps": [[2317793503723491507, "untrusted", false, 4195408951493760988], [3016319839805820069, "build_script_build", false, 2420987469546322851]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ring-0598ad8793603bd1/dep-lib-ring", "checksum": false}}], "rustflags": ["-C", "target-cpu=apple-m2"], "config": 2069994364910194474, "compile_kind": 0}