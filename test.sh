#!/bin/bash

# Ubuntu服务器远程控制方案测试脚本

set -e

echo "=== Ubuntu Remote Control Test Script ==="
echo "开始运行测试..."

# 设置测试环境变量
export RUST_LOG=debug
export RUST_BACKTRACE=1

# 创建测试目录
mkdir -p test_results
cd test_results

# 运行单元测试
echo "1. 运行单元测试..."
echo "测试common库..."
cargo test --package common --lib -- --nocapture > common_tests.log 2>&1 || {
    echo "Common库测试失败，查看 test_results/common_tests.log"
    cat common_tests.log
}

echo "测试controller..."
cargo test --package controller --lib -- --nocapture > controller_tests.log 2>&1 || {
    echo "Controller测试失败，查看 test_results/controller_tests.log"
    cat controller_tests.log
}

echo "测试agent..."
cargo test --package agent --lib -- --nocapture > agent_tests.log 2>&1 || {
    echo "Agent测试失败，查看 test_results/agent_tests.log"
    cat agent_tests.log
}

# 运行集成测试
echo "2. 运行集成测试..."
cargo test --test integration_tests -- --nocapture > integration_tests.log 2>&1 || {
    echo "集成测试失败，查看 test_results/integration_tests.log"
    cat integration_tests.log
}

# 功能测试
echo "3. 运行功能测试..."

# 测试controller帮助信息
echo "测试controller帮助信息..."
../target/release/controller --help > controller_help.log 2>&1 || echo "Controller帮助信息测试完成"

# 测试agent帮助信息
echo "测试agent帮助信息..."
../target/release/agent --help > agent_help.log 2>&1 || echo "Agent帮助信息测试完成"

# 测试配置文件生成
echo "测试配置文件生成..."
echo "test_key_12345" > test_crypto_key.txt

# 测试controller启动（快速退出）
echo "测试controller启动..."
timeout 5s ../target/release/controller list --crypto-key "$(cat test_crypto_key.txt)" > controller_start.log 2>&1 || echo "Controller启动测试完成"

# 测试agent状态检查
echo "测试agent状态检查..."
timeout 5s ../target/release/agent status > agent_status.log 2>&1 || echo "Agent状态检查测试完成"

# 性能测试
echo "4. 运行性能测试..."

# 测试加密性能
echo "测试加密性能..."
cat > crypto_perf_test.rs << 'EOF'
use common::CryptoManager;
use std::time::Instant;

fn main() {
    let key = CryptoManager::generate_key();
    let crypto = CryptoManager::new(&key).unwrap();
    
    let data = vec![0u8; 1024 * 1024]; // 1MB
    
    let start = Instant::now();
    for _ in 0..10 {
        let encrypted = crypto.encrypt(&data).unwrap();
        let _decrypted = crypto.decrypt(&encrypted).unwrap();
    }
    let duration = start.elapsed();
    
    println!("10次1MB数据加密解密耗时: {:?}", duration);
    println!("平均每次耗时: {:?}", duration / 10);
}
EOF

# 编译并运行性能测试
rustc --edition 2021 -L ../target/release/deps crypto_perf_test.rs -o crypto_perf_test --extern common=../target/release/libcommon.rlib 2>/dev/null || echo "性能测试编译失败"

if [ -f "crypto_perf_test" ]; then
    ./crypto_perf_test > crypto_performance.log 2>&1 || echo "性能测试运行完成"
fi

# 内存泄漏测试
echo "5. 内存泄漏测试..."
if command -v valgrind &> /dev/null; then
    echo "使用Valgrind检测内存泄漏..."
    valgrind --leak-check=full --show-leak-kinds=all ../target/release/controller --help > valgrind_controller.log 2>&1 || echo "Valgrind测试完成"
    valgrind --leak-check=full --show-leak-kinds=all ../target/release/agent --help > valgrind_agent.log 2>&1 || echo "Valgrind测试完成"
else
    echo "Valgrind未安装，跳过内存泄漏测试"
fi

# 安全测试
echo "6. 安全测试..."

# 测试命令注入防护
echo "测试命令注入防护..."
cat > security_test.rs << 'EOF'
use agent::executor::CommandExecutor;

#[tokio::main]
async fn main() {
    let executor = CommandExecutor::new();
    
    // 测试危险命令
    let dangerous_commands = [
        ("rm", vec!["-rf".to_string(), "/".to_string()]),
        ("dd", vec!["if=/dev/zero".to_string(), "of=/dev/sda".to_string()]),
        ("shutdown", vec!["-h".to_string(), "now".to_string()]),
    ];
    
    for (cmd, args) in &dangerous_commands {
        let result = executor.execute_command(cmd, args, None, None).await.unwrap();
        if result.exit_code != -1 {
            println!("警告: 危险命令 {} 未被阻止", cmd);
        } else {
            println!("✓ 危险命令 {} 已被阻止", cmd);
        }
    }
}
EOF

# 编译并运行安全测试
rustc --edition 2021 -L ../target/release/deps security_test.rs -o security_test --extern agent=../target/release/libagent.rlib --extern tokio=../target/release/deps/libtokio*.rlib 2>/dev/null || echo "安全测试编译失败"

if [ -f "security_test" ]; then
    ./security_test > security_test.log 2>&1 || echo "安全测试运行完成"
fi

# 生成测试报告
echo "7. 生成测试报告..."
cat > test_report.md << EOF
# Ubuntu Remote Control 测试报告

## 测试时间
$(date)

## 测试环境
- 操作系统: $(uname -a)
- Rust版本: $(rustc --version)
- Cargo版本: $(cargo --version)

## 测试结果

### 单元测试
- Common库: $([ -f common_tests.log ] && echo "完成" || echo "失败")
- Controller: $([ -f controller_tests.log ] && echo "完成" || echo "失败")
- Agent: $([ -f agent_tests.log ] && echo "完成" || echo "失败")

### 集成测试
- 集成测试: $([ -f integration_tests.log ] && echo "完成" || echo "失败")

### 功能测试
- Controller启动: $([ -f controller_start.log ] && echo "完成" || echo "失败")
- Agent状态: $([ -f agent_status.log ] && echo "完成" || echo "失败")

### 性能测试
- 加密性能: $([ -f crypto_performance.log ] && echo "完成" || echo "失败")

### 安全测试
- 命令注入防护: $([ -f security_test.log ] && echo "完成" || echo "失败")

## 详细日志
各测试的详细日志保存在 test_results/ 目录中。

## 建议
1. 定期运行测试确保代码质量
2. 在生产环境部署前进行完整测试
3. 监控性能指标确保系统稳定性
EOF

cd ..

echo "=== 测试完成 ==="
echo "测试结果保存在 test_results/ 目录中"
echo "测试报告: test_results/test_report.md"
echo ""
echo "主要测试文件:"
echo "  - test_results/common_tests.log"
echo "  - test_results/controller_tests.log"
echo "  - test_results/agent_tests.log"
echo "  - test_results/integration_tests.log"
echo "  - test_results/test_report.md"
