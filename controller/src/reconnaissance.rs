use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr};
use std::process::Command;
use tokio::process::Command as AsyncCommand;
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TargetInfo {
    pub id: Uuid,
    pub ip_address: IpAddr,
    pub hostname: Option<String>,
    pub os_info: Option<OsInfo>,
    pub open_ports: Vec<PortInfo>,
    pub services: Vec<ServiceInfo>,
    pub vulnerabilities: Vec<VulnerabilityInfo>,
    pub last_scanned: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OsInfo {
    pub name: String,
    pub version: String,
    pub architecture: String,
    pub kernel_version: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PortInfo {
    pub port: u16,
    pub protocol: String,
    pub state: String,
    pub service: Option<String>,
    pub version: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServiceInfo {
    pub name: String,
    pub version: String,
    pub port: u16,
    pub banner: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityInfo {
    pub cve_id: String,
    pub severity: String,
    pub description: String,
    pub affected_service: String,
}

pub struct ReconnaissanceEngine {
    targets: HashMap<IpAddr, TargetInfo>,
}

impl ReconnaissanceEngine {
    pub fn new() -> Self {
        Self {
            targets: HashMap::new(),
        }
    }

    pub async fn scan_network(&mut self, network: &str) -> Result<Vec<TargetInfo>> {
        println!("Scanning network: {}", network);
        
        // 使用nmap进行网络扫描
        let hosts = self.discover_hosts(network).await?;
        
        for host in hosts {
            let target_info = self.scan_host(host).await?;
            self.targets.insert(host, target_info);
        }
        
        Ok(self.targets.values().cloned().collect())
    }

    pub async fn scan_host(&mut self, ip: IpAddr) -> Result<TargetInfo> {
        println!("Scanning host: {}", ip);
        
        let mut target_info = TargetInfo {
            id: Uuid::new_v4(),
            ip_address: ip,
            hostname: None,
            os_info: None,
            open_ports: Vec::new(),
            services: Vec::new(),
            vulnerabilities: Vec::new(),
            last_scanned: chrono::Utc::now(),
        };

        // 获取主机名
        target_info.hostname = self.resolve_hostname(ip).await.ok();
        
        // 端口扫描
        target_info.open_ports = self.scan_ports(ip).await?;
        
        // 服务识别
        target_info.services = self.identify_services(ip, &target_info.open_ports).await?;
        
        // 操作系统识别
        target_info.os_info = self.identify_os(ip).await.ok();
        
        // 漏洞扫描
        target_info.vulnerabilities = self.scan_vulnerabilities(ip, &target_info.services).await?;
        
        Ok(target_info)
    }

    async fn discover_hosts(&self, network: &str) -> Result<Vec<IpAddr>> {
        // 使用ping扫描发现活跃主机
        let output = AsyncCommand::new("nmap")
            .args(&["-sn", network])
            .output()
            .await?;

        if !output.status.success() {
            return Err(anyhow!("Host discovery failed"));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let mut hosts = Vec::new();

        // 解析nmap输出
        for line in stdout.lines() {
            if line.contains("Nmap scan report for") {
                if let Some(ip_str) = line.split_whitespace().last() {
                    if let Ok(ip) = ip_str.parse::<IpAddr>() {
                        hosts.push(ip);
                    }
                }
            }
        }

        Ok(hosts)
    }

    async fn resolve_hostname(&self, ip: IpAddr) -> Result<String> {
        let output = AsyncCommand::new("nslookup")
            .arg(ip.to_string())
            .output()
            .await?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            // 解析nslookup输出获取主机名
            for line in stdout.lines() {
                if line.contains("name =") {
                    if let Some(hostname) = line.split("name =").nth(1) {
                        return Ok(hostname.trim().trim_end_matches('.').to_string());
                    }
                }
            }
        }

        Err(anyhow!("Hostname resolution failed"))
    }

    async fn scan_ports(&self, ip: IpAddr) -> Result<Vec<PortInfo>> {
        let output = AsyncCommand::new("nmap")
            .args(&["-sS", "-O", "-sV", &ip.to_string()])
            .output()
            .await?;

        if !output.status.success() {
            return Err(anyhow!("Port scan failed"));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let mut ports = Vec::new();

        // 解析nmap输出
        for line in stdout.lines() {
            if line.contains("/tcp") || line.contains("/udp") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 3 {
                    if let Some(port_proto) = parts[0].split('/').next() {
                        if let Ok(port) = port_proto.parse::<u16>() {
                            let protocol = if line.contains("/tcp") { "tcp" } else { "udp" };
                            let state = parts[1].to_string();
                            let service = if parts.len() > 2 { Some(parts[2].to_string()) } else { None };
                            
                            ports.push(PortInfo {
                                port,
                                protocol: protocol.to_string(),
                                state,
                                service,
                                version: None,
                            });
                        }
                    }
                }
            }
        }

        Ok(ports)
    }

    async fn identify_services(&self, ip: IpAddr, ports: &[PortInfo]) -> Result<Vec<ServiceInfo>> {
        let mut services = Vec::new();

        for port_info in ports {
            if port_info.state == "open" {
                // 尝试连接并获取banner
                if let Ok(banner) = self.get_service_banner(ip, port_info.port).await {
                    let service = ServiceInfo {
                        name: port_info.service.clone().unwrap_or_else(|| "unknown".to_string()),
                        version: "unknown".to_string(),
                        port: port_info.port,
                        banner: Some(banner),
                    };
                    services.push(service);
                }
            }
        }

        Ok(services)
    }

    async fn get_service_banner(&self, ip: IpAddr, port: u16) -> Result<String> {
        // 使用netcat获取服务banner
        let output = AsyncCommand::new("nc")
            .args(&["-w", "3", &ip.to_string(), &port.to_string()])
            .output()
            .await?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            Ok(stdout.lines().next().unwrap_or("").to_string())
        } else {
            Err(anyhow!("Failed to get banner"))
        }
    }

    async fn identify_os(&self, ip: IpAddr) -> Result<OsInfo> {
        let output = AsyncCommand::new("nmap")
            .args(&["-O", &ip.to_string()])
            .output()
            .await?;

        if !output.status.success() {
            return Err(anyhow!("OS detection failed"));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        
        // 解析OS信息
        let mut os_name = "Unknown".to_string();
        let mut os_version = "Unknown".to_string();
        
        for line in stdout.lines() {
            if line.contains("Running:") {
                os_name = line.replace("Running:", "").trim().to_string();
            } else if line.contains("OS details:") {
                os_version = line.replace("OS details:", "").trim().to_string();
            }
        }

        Ok(OsInfo {
            name: os_name,
            version: os_version,
            architecture: "x86_64".to_string(), // 默认值
            kernel_version: "Unknown".to_string(),
        })
    }

    async fn scan_vulnerabilities(&self, ip: IpAddr, services: &[ServiceInfo]) -> Result<Vec<VulnerabilityInfo>> {
        let mut vulnerabilities = Vec::new();

        // 使用nmap脚本扫描漏洞
        let output = AsyncCommand::new("nmap")
            .args(&["--script", "vuln", &ip.to_string()])
            .output()
            .await?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            
            // 解析漏洞信息
            for line in stdout.lines() {
                if line.contains("CVE-") {
                    // 提取CVE信息
                    if let Some(cve_start) = line.find("CVE-") {
                        if let Some(cve_end) = line[cve_start..].find(' ') {
                            let cve_id = line[cve_start..cve_start + cve_end].to_string();
                            
                            vulnerabilities.push(VulnerabilityInfo {
                                cve_id,
                                severity: "Medium".to_string(), // 默认值
                                description: line.trim().to_string(),
                                affected_service: "Unknown".to_string(),
                            });
                        }
                    }
                }
            }
        }

        Ok(vulnerabilities)
    }

    pub fn get_target_info(&self, ip: IpAddr) -> Option<&TargetInfo> {
        self.targets.get(&ip)
    }

    pub fn get_all_targets(&self) -> Vec<&TargetInfo> {
        self.targets.values().collect()
    }

    pub fn export_results(&self, format: &str) -> Result<String> {
        match format {
            "json" => Ok(serde_json::to_string_pretty(&self.targets)?),
            "csv" => self.export_csv(),
            _ => Err(anyhow!("Unsupported export format: {}", format)),
        }
    }

    fn export_csv(&self) -> Result<String> {
        let mut csv = String::from("IP,Hostname,OS,Open Ports,Services,Vulnerabilities\n");
        
        for target in self.targets.values() {
            let hostname = target.hostname.as_deref().unwrap_or("Unknown");
            let os = target.os_info.as_ref()
                .map(|os| format!("{} {}", os.name, os.version))
                .unwrap_or_else(|| "Unknown".to_string());
            let ports = target.open_ports.iter()
                .map(|p| p.port.to_string())
                .collect::<Vec<_>>()
                .join(";");
            let services = target.services.iter()
                .map(|s| format!("{}:{}", s.name, s.port))
                .collect::<Vec<_>>()
                .join(";");
            let vulns = target.vulnerabilities.iter()
                .map(|v| &v.cve_id)
                .collect::<Vec<_>>()
                .join(";");
            
            csv.push_str(&format!("{},{},{},{},{},{}\n", 
                target.ip_address, hostname, os, ports, services, vulns));
        }
        
        Ok(csv)
    }
}
