mod controller;
mod deployment;
mod reconnaissance;
mod ui;

#[cfg(test)]
mod tests;

use anyhow::Result;
use clap::{Parser, Subcommand};
use common::{CryptoManager, Logger};
use controller::Controller;
use std::path::PathBuf;

#[derive(Parser)]
#[command(name = "controller")]
#[command(about = "Ubuntu Remote Control - Controller")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
    
    #[arg(short, long, default_value = "controller.log")]
    log_file: PathBuf,
    
    #[arg(short, long, default_value = "http://localhost:8080")]
    server_url: String,
    
    #[arg(short, long)]
    crypto_key: Option<String>,
}

#[derive(Subcommand)]
enum Commands {
    /// Start interactive controller
    Interactive,
    /// Deploy agent to target
    Deploy {
        #[arg(short, long)]
        target: String,
        #[arg(short, long)]
        username: String,
        #[arg(short, long)]
        password: Option<String>,
        #[arg(short, long)]
        key_file: Option<PathBuf>,
    },
    /// Execute single command
    Execute {
        #[arg(short, long)]
        target: String,
        #[arg(short, long)]
        command: String,
    },
    /// List connected agents
    List,
    /// Start web interface
    Web {
        #[arg(short, long, default_value = "8081")]
        port: u16,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // 初始化日志
    env_logger::init();
    let logger = Logger::new(cli.log_file.to_string_lossy().to_string());
    logger.ensure_log_directory()?;
    
    // 初始化加密密钥
    let crypto_key = if let Some(key) = cli.crypto_key {
        key.into_bytes()
    } else {
        CryptoManager::generate_key()
    };
    
    // 创建控制器实例
    let mut controller = Controller::new(cli.server_url, &crypto_key, logger).await?;
    
    match cli.command {
        Commands::Interactive => {
            println!("Starting interactive controller...");
            ui::start_interactive_mode(&mut controller).await?;
        },
        Commands::Deploy { target, username, password, key_file } => {
            println!("Deploying agent to target: {}", target);
            controller.deploy_agent(&target, &username, password.as_deref(), key_file.as_deref()).await?;
        },
        Commands::Execute { target, command } => {
            println!("Executing command on target: {}", target);
            let result = controller.execute_command(&target, &command).await?;
            println!("Result: {:?}", result);
        },
        Commands::List => {
            println!("Connected agents:");
            let agents = controller.list_agents().await?;
            for agent in agents {
                println!("  - {}: {} ({})", agent.id, agent.hostname, agent.status);
            }
        },
        Commands::Web { port } => {
            println!("Starting web interface on port {}", port);
            ui::start_web_interface(&mut controller, port).await?;
        },
    }
    
    Ok(())
}
