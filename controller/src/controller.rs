use std::collections::HashMap;
use std::time::Duration;
use anyhow::{Result, anyhow};
use tokio::time::timeout;
use uuid::Uuid;
use common::{
    HttpCommunicator, Message, MessageType, CommandMessage, CommandResult,
    FileUploadMessage, FileDownloadMessage, ProcessMessage, SystemInfoResult,
    Logger, AuthManager, Permission
};

#[derive(Debug, Clone)]
pub struct AgentInfo {
    pub id: Uuid,
    pub hostname: String,
    pub ip_address: String,
    pub os_version: String,
    pub status: AgentStatus,
    pub last_seen: chrono::DateTime<chrono::Utc>,
    pub communicator: HttpCommunicator,
}

#[derive(Debug, Clone)]
pub enum AgentStatus {
    Online,
    Offline,
    Connecting,
    Error(String),
}

impl std::fmt::Display for AgentStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AgentStatus::Online => write!(f, "Online"),
            AgentStatus::Offline => write!(f, "Offline"),
            AgentStatus::Connecting => write!(f, "Connecting"),
            AgentStatus::Error(e) => write!(f, "Error: {}", e),
        }
    }
}

pub struct Controller {
    agents: HashMap<Uuid, AgentInfo>,
    communicator: HttpCommunicator,
    logger: Logger,
    auth_manager: AuthManager,
}

impl Controller {
    pub async fn new(server_url: String, crypto_key: &[u8], logger: Logger) -> Result<Self> {
        let communicator = HttpCommunicator::new(server_url, crypto_key)?;
        let auth_manager = AuthManager::new(crypto_key, 3600)?; // 1小时token有效期
        
        Ok(Self {
            agents: HashMap::new(),
            communicator,
            logger,
            auth_manager,
        })
    }

    pub async fn deploy_agent(&mut self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {
        self.logger.log_info(&format!("Starting agent deployment to {}", target))?;
        
        // 这里应该实现SSH连接和agent部署逻辑
        // 为了演示，我们模拟部署过程
        println!("Connecting to {}@{}", username, target);
        
        if let Some(password) = password {
            println!("Using password authentication");
        } else if let Some(key_file) = key_file {
            println!("Using key file: {}", key_file);
        } else {
            return Err(anyhow!("Either password or key file must be provided"));
        }
        
        // 模拟部署过程
        tokio::time::sleep(Duration::from_secs(2)).await;
        
        // 创建新的agent信息
        let agent_id = Uuid::new_v4();
        let agent_info = AgentInfo {
            id: agent_id,
            hostname: target.to_string(),
            ip_address: target.to_string(),
            os_version: "Ubuntu 22.04 LTS".to_string(),
            status: AgentStatus::Connecting,
            last_seen: chrono::Utc::now(),
            communicator: self.communicator.clone(),
        };
        
        self.agents.insert(agent_id, agent_info);
        self.logger.log_info(&format!("Agent deployed successfully to {}", target))?;
        
        Ok(())
    }

    pub async fn execute_command(&mut self, target: &str, command: &str) -> Result<CommandResult> {
        // 查找目标agent
        let agent = self.find_agent_by_hostname(target)
            .ok_or_else(|| anyhow!("Agent not found: {}", target))?;
        
        self.logger.log_command("system", agent.id, command, true)?;
        
        // 创建命令消息
        let cmd_msg = CommandMessage {
            command: command.to_string(),
            args: vec![],
            working_dir: None,
            timeout: Some(30),
        };
        
        let message = Message::new(MessageType::Command(cmd_msg));
        
        // 发送命令并等待响应
        let response = timeout(
            Duration::from_secs(35),
            agent.communicator.send_message(&message)
        ).await??;
        
        if let Some(response_msg) = response {
            if let MessageType::CommandResult(result) = response_msg.message_type {
                return Ok(result);
            }
        }
        
        Err(anyhow!("Failed to get command result"))
    }

    pub async fn upload_file(&mut self, target: &str, local_path: &str, remote_path: &str) -> Result<()> {
        let agent = self.find_agent_by_hostname(target)
            .ok_or_else(|| anyhow!("Agent not found: {}", target))?;
        
        self.logger.log_file_operation("system", agent.id, "upload", remote_path, true)?;
        
        // 读取本地文件
        let file_data = tokio::fs::read(local_path).await?;
        let file_size = file_data.len() as u64;
        
        // 分块上传
        const CHUNK_SIZE: usize = 64 * 1024; // 64KB chunks
        let total_chunks = (file_data.len() + CHUNK_SIZE - 1) / CHUNK_SIZE;
        
        for (chunk_index, chunk) in file_data.chunks(CHUNK_SIZE).enumerate() {
            let upload_msg = FileUploadMessage {
                file_path: remote_path.to_string(),
                file_size,
                chunk_index: chunk_index as u32,
                total_chunks: total_chunks as u32,
                data: chunk.to_vec(),
            };
            
            let message = Message::new(MessageType::FileUpload(upload_msg));
            agent.communicator.send_message(&message).await?;
            
            println!("Uploaded chunk {}/{}", chunk_index + 1, total_chunks);
        }
        
        Ok(())
    }

    pub async fn download_file(&mut self, target: &str, remote_path: &str, local_path: &str) -> Result<()> {
        let agent = self.find_agent_by_hostname(target)
            .ok_or_else(|| anyhow!("Agent not found: {}", target))?;
        
        self.logger.log_file_operation("system", agent.id, "download", remote_path, true)?;
        
        let download_msg = FileDownloadMessage {
            file_path: remote_path.to_string(),
            chunk_size: Some(64 * 1024),
        };
        
        let message = Message::new(MessageType::FileDownload(download_msg));
        let response = agent.communicator.send_message(&message).await?;
        
        // 处理文件下载响应
        if let Some(response_msg) = response {
            if let MessageType::FileData(file_data) = response_msg.message_type {
                tokio::fs::write(local_path, &file_data.data).await?;
                println!("File downloaded successfully to {}", local_path);
            }
        }
        
        Ok(())
    }

    pub async fn get_system_info(&mut self, target: &str) -> Result<SystemInfoResult> {
        let agent = self.find_agent_by_hostname(target)
            .ok_or_else(|| anyhow!("Agent not found: {}", target))?;
        
        let message = Message::new(MessageType::SystemInfo);
        let response = timeout(
            Duration::from_secs(10),
            agent.communicator.send_message(&message)
        ).await??;
        
        if let Some(response_msg) = response {
            if let MessageType::SystemInfoResult(info) = response_msg.message_type {
                return Ok(info);
            }
        }
        
        Err(anyhow!("Failed to get system information"))
    }

    pub async fn manage_process(&mut self, target: &str, action: common::ProcessAction) -> Result<()> {
        let agent = self.find_agent_by_hostname(target)
            .ok_or_else(|| anyhow!("Agent not found: {}", target))?;
        
        let process_msg = ProcessMessage {
            action,
            process_id: None,
            process_name: None,
        };
        
        let message = Message::new(MessageType::ProcessManagement(process_msg));
        agent.communicator.send_message(&message).await?;
        
        Ok(())
    }

    pub async fn list_agents(&self) -> Result<Vec<AgentInfo>> {
        Ok(self.agents.values().cloned().collect())
    }

    pub async fn start_heartbeat_monitoring(&self) {
        // 启动心跳监控任务
        for agent in self.agents.values() {
            let communicator = agent.communicator.clone();
            tokio::spawn(async move {
                communicator.start_heartbeat_loop(Duration::from_secs(30)).await;
            });
        }
    }

    fn find_agent_by_hostname(&self, hostname: &str) -> Option<&AgentInfo> {
        self.agents.values().find(|agent| agent.hostname == hostname)
    }
}

impl Logger {
    pub fn log_info(&self, message: &str) -> Result<()> {
        let entry = common::LogEntry {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            level: common::LogLevel::Info,
            category: common::LogCategory::Communication,
            user_id: Some("system".to_string()),
            session_id: None,
            message: message.to_string(),
            details: None,
        };
        self.log(entry)
    }
}
