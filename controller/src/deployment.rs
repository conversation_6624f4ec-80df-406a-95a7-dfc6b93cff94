use anyhow::{Result, anyhow};
use std::path::Path;
use std::process::Command;
use tokio::process::Command as AsyncCommand;

pub struct DeploymentManager {
    agent_binary_path: String,
}

impl DeploymentManager {
    pub fn new(agent_binary_path: String) -> Self {
        Self { agent_binary_path }
    }

    pub async fn deploy_via_ssh(&self, target: &str, username: &str, password: Option<&str>, key_file: Option<&str>) -> Result<()> {
        // 验证agent二进制文件存在
        if !Path::new(&self.agent_binary_path).exists() {
            return Err(anyhow!("Agent binary not found: {}", self.agent_binary_path));
        }

        // 构建SSH连接参数
        let mut ssh_args = vec![
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
        ];

        if let Some(key_file) = key_file {
            ssh_args.extend_from_slice(&["-i", key_file]);
        }

        let target_with_user = format!("{}@{}", username, target);
        ssh_args.push(&target_with_user);

        // 1. 上传agent二进制文件
        println!("Uploading agent binary...");
        self.upload_file_via_scp(&target_with_user, &self.agent_binary_path, "/tmp/agent", key_file).await?;

        // 2. 设置执行权限
        println!("Setting execute permissions...");
        let chmod_cmd = "chmod +x /tmp/agent";
        self.execute_ssh_command(&target_with_user, chmod_cmd, key_file).await?;

        // 3. 创建systemd服务文件
        println!("Creating systemd service...");
        let service_content = self.generate_systemd_service();
        let create_service_cmd = format!("echo '{}' | sudo tee /etc/systemd/system/remote-agent.service", service_content);
        self.execute_ssh_command(&target_with_user, &create_service_cmd, key_file).await?;

        // 4. 启用并启动服务
        println!("Starting agent service...");
        self.execute_ssh_command(&target_with_user, "sudo systemctl daemon-reload", key_file).await?;
        self.execute_ssh_command(&target_with_user, "sudo systemctl enable remote-agent", key_file).await?;
        self.execute_ssh_command(&target_with_user, "sudo systemctl start remote-agent", key_file).await?;

        // 5. 验证服务状态
        println!("Verifying deployment...");
        self.execute_ssh_command(&target_with_user, "sudo systemctl status remote-agent", key_file).await?;

        println!("Agent deployed successfully!");
        Ok(())
    }

    async fn upload_file_via_scp(&self, target: &str, local_path: &str, remote_path: &str, key_file: Option<&str>) -> Result<()> {
        let mut scp_args = vec![
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
        ];

        if let Some(key_file) = key_file {
            scp_args.extend_from_slice(&["-i", key_file]);
        }

        scp_args.push(local_path);
        scp_args.push(&format!("{}:{}", target, remote_path));

        let output = AsyncCommand::new("scp")
            .args(&scp_args)
            .output()
            .await?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow!("SCP failed: {}", error));
        }

        Ok(())
    }

    async fn execute_ssh_command(&self, target: &str, command: &str, key_file: Option<&str>) -> Result<String> {
        let mut ssh_args = vec![
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
        ];

        if let Some(key_file) = key_file {
            ssh_args.extend_from_slice(&["-i", key_file]);
        }

        ssh_args.push(target);
        ssh_args.push(command);

        let output = AsyncCommand::new("ssh")
            .args(&ssh_args)
            .output()
            .await?;

        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        if !output.status.success() {
            return Err(anyhow!("SSH command failed: {}\nStderr: {}", stdout, stderr));
        }

        Ok(stdout.to_string())
    }

    fn generate_systemd_service(&self) -> String {
        r#"[Unit]
Description=Remote Control Agent
After=network.target

[Service]
Type=simple
User=root
ExecStart=/tmp/agent --server-url http://controller:8080 --log-file /var/log/remote-agent.log
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target"#.to_string()
    }

    pub async fn deploy_via_web(&self, target_url: &str, auth_token: &str) -> Result<()> {
        // 通过Web接口部署agent
        let client = reqwest::Client::new();
        
        // 读取agent二进制文件
        let agent_binary = tokio::fs::read(&self.agent_binary_path).await?;
        let encoded_binary = base64::encode(&agent_binary);

        // 构建部署请求
        let deploy_request = serde_json::json!({
            "binary_data": encoded_binary,
            "service_name": "remote-agent",
            "auto_start": true
        });

        let response = client
            .post(&format!("{}/api/deploy", target_url))
            .header("Authorization", format!("Bearer {}", auth_token))
            .json(&deploy_request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Web deployment failed: {}", error_text));
        }

        println!("Agent deployed successfully via web interface!");
        Ok(())
    }

    pub fn build_agent(&self, target_arch: &str) -> Result<String> {
        println!("Building agent for architecture: {}", target_arch);
        
        let output = Command::new("cargo")
            .args(&["build", "--release", "--bin", "agent"])
            .env("CARGO_TARGET_DIR", "target")
            .output()?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow!("Build failed: {}", error));
        }

        let binary_path = format!("target/release/agent");
        Ok(binary_path)
    }

    pub async fn generate_deployment_script(&self, target: &str, username: &str) -> Result<String> {
        let script = format!(r#"#!/bin/bash
set -e

echo "Starting remote agent deployment..."

# Download agent binary
wget -O /tmp/agent http://controller:8080/download/agent
chmod +x /tmp/agent

# Create systemd service
cat > /etc/systemd/system/remote-agent.service << 'EOF'
{}
EOF

# Start service
systemctl daemon-reload
systemctl enable remote-agent
systemctl start remote-agent

echo "Agent deployed and started successfully!"
"#, self.generate_systemd_service());

        Ok(script)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_systemd_service_generation() {
        let deployment_manager = DeploymentManager::new("test_agent".to_string());
        let service = deployment_manager.generate_systemd_service();
        
        assert!(service.contains("[Unit]"));
        assert!(service.contains("Description=Remote Control Agent"));
        assert!(service.contains("ExecStart=/tmp/agent"));
    }
}
