use anyhow::Result;
use std::io::{self, Write};
use crate::controller::Controller;

pub async fn start_interactive_mode(controller: &mut Controller) -> Result<()> {
    println!("=== Ubuntu Remote Control - Interactive Mode ===");
    println!("Type 'help' for available commands or 'quit' to exit");
    
    loop {
        print!("controller> ");
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let input = input.trim();
        
        if input.is_empty() {
            continue;
        }
        
        match input {
            "quit" | "exit" => {
                println!("Goodbye!");
                break;
            },
            "help" => {
                print_help();
            },
            "list" => {
                let agents = controller.list_agents().await?;
                if agents.is_empty() {
                    println!("No agents connected");
                } else {
                    println!("Connected agents:");
                    for agent in agents {
                        println!("  - {}: {} ({})", agent.id, agent.hostname, agent.status);
                    }
                }
            },
            _ => {
                if let Err(e) = handle_command(controller, input).await {
                    println!("Error: {}", e);
                }
            }
        }
    }
    
    Ok(())
}

async fn handle_command(controller: &mut Controller, input: &str) -> Result<()> {
    let parts: Vec<&str> = input.split_whitespace().collect();
    if parts.is_empty() {
        return Ok(());
    }
    
    match parts[0] {
        "deploy" => {
            if parts.len() < 3 {
                println!("Usage: deploy <target> <username> [password]");
                return Ok(());
            }
            let target = parts[1];
            let username = parts[2];
            let password = parts.get(3).copied();
            
            controller.deploy_agent(target, username, password, None).await?;
            println!("Agent deployed successfully to {}", target);
        },
        "exec" => {
            if parts.len() < 3 {
                println!("Usage: exec <target> <command>");
                return Ok(());
            }
            let target = parts[1];
            let command = parts[2..].join(" ");
            
            let result = controller.execute_command(target, &command).await?;
            println!("Exit code: {}", result.exit_code);
            if !result.stdout.is_empty() {
                println!("Stdout:\n{}", result.stdout);
            }
            if !result.stderr.is_empty() {
                println!("Stderr:\n{}", result.stderr);
            }
        },
        "upload" => {
            if parts.len() < 4 {
                println!("Usage: upload <target> <local_path> <remote_path>");
                return Ok(());
            }
            let target = parts[1];
            let local_path = parts[2];
            let remote_path = parts[3];
            
            controller.upload_file(target, local_path, remote_path).await?;
            println!("File uploaded successfully");
        },
        "download" => {
            if parts.len() < 4 {
                println!("Usage: download <target> <remote_path> <local_path>");
                return Ok(());
            }
            let target = parts[1];
            let remote_path = parts[2];
            let local_path = parts[3];
            
            controller.download_file(target, remote_path, local_path).await?;
            println!("File downloaded successfully");
        },
        "sysinfo" => {
            if parts.len() < 2 {
                println!("Usage: sysinfo <target>");
                return Ok(());
            }
            let target = parts[1];
            
            let info = controller.get_system_info(target).await?;
            println!("System Information for {}:", target);
            println!("  Hostname: {}", info.hostname);
            println!("  OS: {}", info.os_version);
            println!("  Kernel: {}", info.kernel_version);
            println!("  Architecture: {}", info.architecture);
            println!("  CPU: {}", info.cpu_info);
            println!("  Memory: {} MB total, {} MB available", 
                info.memory_total / 1024 / 1024, 
                info.memory_available / 1024 / 1024);
        },
        _ => {
            println!("Unknown command: {}. Type 'help' for available commands.", parts[0]);
        }
    }
    
    Ok(())
}

fn print_help() {
    println!("Available commands:");
    println!("  help                                    - Show this help message");
    println!("  list                                    - List connected agents");
    println!("  deploy <target> <username> [password]   - Deploy agent to target");
    println!("  exec <target> <command>                 - Execute command on target");
    println!("  upload <target> <local> <remote>        - Upload file to target");
    println!("  download <target> <remote> <local>      - Download file from target");
    println!("  sysinfo <target>                        - Get system information");
    println!("  quit/exit                               - Exit the program");
}

pub async fn start_web_interface(controller: &mut Controller, port: u16) -> Result<()> {
    use warp::Filter;
    
    println!("Starting web interface on port {}", port);
    
    // 创建基本的Web路由
    let hello = warp::path!("hello" / String)
        .map(|name| format!("Hello, {}!", name));
    
    let routes = hello;
    
    warp::serve(routes)
        .run(([127, 0, 0, 1], port))
        .await;
    
    Ok(())
}

// 简化的Web界面实现
pub async fn start_simple_web_interface(_controller: &mut Controller, port: u16) -> Result<()> {
    use std::convert::Infallible;
    use std::net::SocketAddr;
    use tokio::net::TcpListener;
    use tokio::io::{AsyncReadExt, AsyncWriteExt};
    
    let addr = SocketAddr::from(([127, 0, 0, 1], port));
    let listener = TcpListener::bind(addr).await?;
    
    println!("Web interface listening on http://{}", addr);
    
    loop {
        let (mut socket, _) = listener.accept().await?;
        
        tokio::spawn(async move {
            let mut buffer = [0; 1024];
            
            if let Ok(n) = socket.read(&mut buffer).await {
                let request = String::from_utf8_lossy(&buffer[..n]);
                
                let response = if request.contains("GET / ") {
                    create_dashboard_html()
                } else if request.contains("GET /api/agents") {
                    r#"HTTP/1.1 200 OK
Content-Type: application/json

{"agents": []}"#.to_string()
                } else {
                    r#"HTTP/1.1 404 Not Found
Content-Type: text/plain

Not Found"#.to_string()
                };
                
                let _ = socket.write_all(response.as_bytes()).await;
            }
        });
    }
}

fn create_dashboard_html() -> String {
    r#"HTTP/1.1 200 OK
Content-Type: text/html

<!DOCTYPE html>
<html>
<head>
    <title>Ubuntu Remote Control Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .agents { margin-top: 20px; }
        .agent { border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .online { background-color: #e8f5e8; }
        .offline { background-color: #f5e8e8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Ubuntu Remote Control Dashboard</h1>
        <p>Manage your remote agents from this web interface</p>
    </div>
    
    <div class="agents">
        <h2>Connected Agents</h2>
        <div id="agent-list">
            <p>Loading agents...</p>
        </div>
    </div>
    
    <script>
        // 简单的JavaScript来获取agent列表
        fetch('/api/agents')
            .then(response => response.json())
            .then(data => {
                const agentList = document.getElementById('agent-list');
                if (data.agents.length === 0) {
                    agentList.innerHTML = '<p>No agents connected</p>';
                } else {
                    agentList.innerHTML = data.agents.map(agent => 
                        `<div class="agent ${agent.status.toLowerCase()}">
                            <strong>${agent.hostname}</strong> (${agent.ip_address})
                            <br>Status: ${agent.status}
                            <br>Last seen: ${agent.last_seen}
                        </div>`
                    ).join('');
                }
            })
            .catch(error => {
                document.getElementById('agent-list').innerHTML = '<p>Error loading agents</p>';
            });
    </script>
</body>
</html>"#.to_string()
}
