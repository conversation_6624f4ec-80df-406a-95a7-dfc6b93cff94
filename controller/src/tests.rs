#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::time::Duration;

    #[tokio::test]
    async fn test_controller_initialization() {
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        let controller = Controller::new(server_url, &crypto_key, logger).await;
        assert!(controller.is_ok());
    }

    #[tokio::test]
    async fn test_agent_management() {
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        let mut controller = Controller::new(server_url, &crypto_key, logger).await.unwrap();
        
        // 测试agent列表（初始应该为空）
        let agents = controller.list_agents().await.unwrap();
        assert!(agents.is_empty());
        
        // 模拟添加agent（在实际实现中，这会通过部署完成）
        // 这里我们直接测试内部状态
    }

    #[tokio::test]
    async fn test_deployment_manager() {
        use crate::deployment::DeploymentManager;
        
        let temp_dir = TempDir::new().unwrap();
        let agent_binary = temp_dir.path().join("test_agent");
        
        // 创建一个假的agent二进制文件
        std::fs::write(&agent_binary, b"fake binary content").unwrap();
        
        let deployment_manager = DeploymentManager::new(agent_binary.to_string_lossy().to_string());
        
        // 测试systemd服务生成
        let service_content = deployment_manager.generate_systemd_service();
        assert!(service_content.contains("[Unit]"));
        assert!(service_content.contains("Description=Remote Control Agent"));
        assert!(service_content.contains("ExecStart=/tmp/agent"));
        
        // 测试部署脚本生成
        let script = deployment_manager.generate_deployment_script("*************", "root").await.unwrap();
        assert!(script.contains("#!/bin/bash"));
        assert!(script.contains("wget"));
        assert!(script.contains("systemctl"));
    }

    #[tokio::test]
    async fn test_reconnaissance_engine() {
        use crate::reconnaissance::ReconnaissanceEngine;
        use std::net::IpAddr;
        
        let mut recon_engine = ReconnaissanceEngine::new();
        
        // 测试单个主机扫描（使用localhost）
        let localhost: IpAddr = "127.0.0.1".parse().unwrap();
        let result = recon_engine.scan_host(localhost).await;
        
        // 在某些环境中可能失败（如果没有nmap），这是可以接受的
        match result {
            Ok(target_info) => {
                assert_eq!(target_info.ip_address, localhost);
                assert!(!target_info.id.to_string().is_empty());
            },
            Err(_) => {
                // 如果扫描失败（例如没有nmap），跳过测试
                println!("Reconnaissance test skipped (nmap not available)");
            }
        }
        
        // 测试结果导出
        let json_export = recon_engine.export_results("json").unwrap();
        assert!(json_export.contains("{"));
        
        let csv_export = recon_engine.export_results("csv").unwrap();
        assert!(csv_export.contains("IP,Hostname"));
    }

    #[tokio::test]
    async fn test_ui_command_parsing() {
        use crate::ui;
        
        // 这里我们可以测试命令解析逻辑
        // 由于UI模块主要是交互式的，我们测试一些辅助函数
        
        // 测试帮助信息生成
        // 在实际实现中，可以将print_help改为返回字符串以便测试
    }

    #[test]
    fn test_agent_status_display() {
        use crate::controller::AgentStatus;
        
        let online = AgentStatus::Online;
        assert_eq!(format!("{}", online), "Online");
        
        let offline = AgentStatus::Offline;
        assert_eq!(format!("{}", offline), "Offline");
        
        let error = AgentStatus::Error("Connection failed".to_string());
        assert_eq!(format!("{}", error), "Error: Connection failed");
    }

    #[tokio::test]
    async fn test_concurrent_agent_operations() {
        use std::sync::Arc;
        use tokio::sync::Mutex;
        
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        let controller = Arc::new(Mutex::new(
            Controller::new(server_url, &crypto_key, logger).await.unwrap()
        ));
        
        // 模拟并发操作
        let mut handles = vec![];
        
        for i in 0..5 {
            let controller_clone = controller.clone();
            let handle = tokio::spawn(async move {
                let controller = controller_clone.lock().await;
                controller.list_agents().await
            });
            handles.push(handle);
        }
        
        // 等待所有操作完成
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok());
        }
    }

    #[tokio::test]
    async fn test_error_recovery() {
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        
        // 测试无效的服务器URL
        let invalid_url = "invalid://url".to_string();
        let controller = Controller::new(invalid_url, &crypto_key, logger).await;
        
        // 应该能够创建controller，但在实际通信时会失败
        assert!(controller.is_ok());
    }

    #[test]
    fn test_configuration_validation() {
        // 测试各种配置参数的验证
        
        // 测试空密钥
        let empty_key = vec![];
        let crypto = CryptoManager::new(&empty_key);
        assert!(crypto.is_ok()); // 应该使用哈希处理
        
        // 测试长密钥
        let long_key = vec![0u8; 1024];
        let crypto = CryptoManager::new(&long_key);
        assert!(crypto.is_ok());
    }

    #[tokio::test]
    async fn test_logging_integration() {
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("controller_test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        // 确保日志目录存在
        logger.ensure_log_directory().unwrap();
        
        // 测试日志记录
        logger.log_info("Controller test started").unwrap();
        logger.log_error("Test error message").unwrap();
        
        // 验证日志文件内容
        let log_content = std::fs::read_to_string(&log_file).unwrap();
        assert!(log_content.contains("Controller test started"));
        assert!(log_content.contains("Test error message"));
    }

    #[tokio::test]
    async fn test_memory_usage() {
        // 简单的内存使用测试
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        // 创建多个controller实例来测试内存使用
        let mut controllers = vec![];
        for _ in 0..10 {
            let controller = Controller::new(
                server_url.clone(),
                &crypto_key,
                logger.clone()
            ).await.unwrap();
            controllers.push(controller);
        }
        
        // 验证所有controller都已创建
        assert_eq!(controllers.len(), 10);
        
        // 清理
        drop(controllers);
    }

    #[tokio::test]
    async fn test_timeout_handling() {
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        let mut controller = Controller::new(server_url, &crypto_key, logger).await.unwrap();
        
        // 测试命令执行超时（使用不存在的目标）
        let result = controller.execute_command("nonexistent_target", "echo hello").await;
        assert!(result.is_err()); // 应该失败，因为目标不存在
    }
}
