[package]
name = "controller"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "controller"
path = "src/main.rs"

[dependencies]
common = { path = "../common" }
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
reqwest = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
clap = { workspace = true }
rustls = { workspace = true }
rustls-pemfile = { workspace = true }
tempfile = { workspace = true }
