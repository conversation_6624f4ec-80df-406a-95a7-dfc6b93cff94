#!/bin/bash

# Linux AMD64 静态链接构建脚本 (使用 musl)
# 在 macOS 上构建适用于任何 Linux 发行版的静态链接二进制文件

set -e  # 遇到错误时退出

echo "=== Linux AMD64 静态链接构建脚本 (musl) ==="
echo "在 macOS 上为 Linux AMD64 构建静态链接项目..."

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    echo "错误: 未找到Cargo。请安装Rust工具链。"
    echo "安装命令: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

echo "Rust版本信息:"
rustc --version
cargo --version

# 使用 musl 目标进行静态链接
TARGET="x86_64-unknown-linux-musl"
echo "检查Linux musl目标平台: $TARGET"

if ! rustup target list --installed | grep -q "$TARGET"; then
    echo "安装Linux musl目标平台: $TARGET"
    rustup target add $TARGET
else
    echo "✓ Linux musl目标平台已安装: $TARGET"
fi

# 清理之前的构建
echo "清理之前的构建..."
cargo clean

# 设置静态链接环境变量
export RUSTFLAGS="-C target-feature=+crt-static"

# 构建项目 (Linux AMD64 静态链接)
echo "为 Linux AMD64 构建静态链接项目..."
echo "目标平台: $TARGET"
echo "使用静态链接，生成的二进制文件可在任何Linux发行版运行"

echo "1. 构建common库..."
cargo build --package common --target $TARGET

echo "2. 构建controller..."
cargo build --package controller --target $TARGET

echo "3. 构建agent..."
cargo build --package agent --target $TARGET

echo "4. 构建release版本..."
cargo build --release --target $TARGET

# 检查二进制文件
echo "检查生成的Linux静态二进制文件..."
LINUX_TARGET_DIR="target/$TARGET/release"

if [ -f "$LINUX_TARGET_DIR/controller" ]; then
    echo "✓ Controller Linux静态二进制文件已生成: $LINUX_TARGET_DIR/controller"
    ls -lh "$LINUX_TARGET_DIR/controller"
    file "$LINUX_TARGET_DIR/controller"
    echo "检查动态链接依赖:"
    otool -L "$LINUX_TARGET_DIR/controller" 2>/dev/null || echo "  (静态链接，无外部依赖)"
else
    echo "✗ Controller Linux静态二进制文件未找到"
fi

if [ -f "$LINUX_TARGET_DIR/agent" ]; then
    echo "✓ Agent Linux静态二进制文件已生成: $LINUX_TARGET_DIR/agent"
    ls -lh "$LINUX_TARGET_DIR/agent"
    file "$LINUX_TARGET_DIR/agent"
    echo "检查动态链接依赖:"
    otool -L "$LINUX_TARGET_DIR/agent" 2>/dev/null || echo "  (静态链接，无外部依赖)"
else
    echo "✗ Agent Linux静态二进制文件未找到"
fi

# 创建Linux静态部署包
echo "创建Linux静态部署包..."
DIST_DIR="dist-linux-static"
mkdir -p $DIST_DIR

if [ -f "$LINUX_TARGET_DIR/controller" ]; then
    cp "$LINUX_TARGET_DIR/controller" $DIST_DIR/
fi

if [ -f "$LINUX_TARGET_DIR/agent" ]; then
    cp "$LINUX_TARGET_DIR/agent" $DIST_DIR/
fi

cp README.md $DIST_DIR/ 2>/dev/null || echo "README未复制到$DIST_DIR目录"

# 创建Linux安装脚本
cat > $DIST_DIR/install.sh << 'EOF'
#!/bin/bash
# Ubuntu Remote Control Linux 静态版本安装脚本

echo "安装Ubuntu Remote Control (Linux AMD64 静态版本)..."

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "x86_64" ]; then
    echo "错误: 当前架构是 $ARCH，此程序仅支持 x86_64"
    exit 1
fi

echo "✓ 架构检查通过: $ARCH"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本，或手动复制文件到目标目录"
    echo "手动安装命令:"
    echo "  cp controller ~/.local/bin/ (或其他PATH目录)"
    echo "  cp agent ~/.local/bin/"
    echo "  chmod +x ~/.local/bin/controller ~/.local/bin/agent"
    exit 1
fi

# 复制二进制文件
if [ -f "controller" ]; then
    cp controller /usr/local/bin/
    chmod +x /usr/local/bin/controller
    echo "✓ Controller已安装到 /usr/local/bin/controller"
fi

if [ -f "agent" ]; then
    cp agent /usr/local/bin/
    chmod +x /usr/local/bin/agent
    echo "✓ Agent已安装到 /usr/local/bin/agent"
fi

# 创建配置目录
mkdir -p /etc/remote-control
mkdir -p /var/log/remote-control

echo "安装完成！"
echo "使用方法:"
echo "  控制端: /usr/local/bin/controller --help"
echo "  受控端: /usr/local/bin/agent --help"
echo ""
echo "特性: 静态链接版本，无需额外依赖库"
EOF

chmod +x $DIST_DIR/install.sh

# 创建用户安装脚本（无需root权限）
cat > $DIST_DIR/install_user.sh << 'EOF'
#!/bin/bash
# Ubuntu Remote Control 用户安装脚本（无需root权限）

echo "安装Ubuntu Remote Control到用户目录..."

# 创建用户bin目录
mkdir -p ~/.local/bin

# 复制二进制文件
if [ -f "controller" ]; then
    cp controller ~/.local/bin/
    chmod +x ~/.local/bin/controller
    echo "✓ Controller已安装到 ~/.local/bin/controller"
fi

if [ -f "agent" ]; then
    cp agent ~/.local/bin/
    chmod +x ~/.local/bin/agent
    echo "✓ Agent已安装到 ~/.local/bin/agent"
fi

# 检查PATH
if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
    echo ""
    echo "注意: ~/.local/bin 不在您的PATH中"
    echo "请将以下行添加到 ~/.bashrc 或 ~/.zshrc:"
    echo "  export PATH=\"\$HOME/.local/bin:\$PATH\""
    echo "然后运行: source ~/.bashrc"
fi

echo ""
echo "安装完成！"
echo "使用方法:"
echo "  控制端: ~/.local/bin/controller --help"
echo "  受控端: ~/.local/bin/agent --help"
EOF

chmod +x $DIST_DIR/install_user.sh

# 创建打包脚本
cat > $DIST_DIR/package.sh << 'EOF'
#!/bin/bash
# 创建分发包

echo "创建Linux静态分发包..."
tar -czf ubuntu-remote-control-linux-amd64-static.tar.gz controller agent install.sh install_user.sh README.md
echo "✓ 静态分发包已创建: ubuntu-remote-control-linux-amd64-static.tar.gz"
echo ""
echo "分发包内容:"
echo "  - controller: 控制端程序（静态链接）"
echo "  - agent: 受控端程序（静态链接）"
echo "  - install.sh: 系统安装脚本（需要root权限）"
echo "  - install_user.sh: 用户安装脚本（无需root权限）"
echo "  - README.md: 说明文档"
EOF

chmod +x $DIST_DIR/package.sh

echo "=== Linux AMD64 静态链接构建完成 ==="
echo "生成的文件:"
echo "  - $LINUX_TARGET_DIR/controller (Linux控制端，静态链接)"
echo "  - $LINUX_TARGET_DIR/agent (Linux受控端，静态链接)"
echo "  - $DIST_DIR/ (Linux静态部署包)"
echo ""
echo "静态链接的优势:"
echo "  ✓ 无需安装额外的系统库"
echo "  ✓ 可在任何Linux发行版上运行"
echo "  ✓ 部署简单，只需复制二进制文件"
echo ""
echo "部署到Linux服务器:"
echo "  1. 复制 $DIST_DIR 目录到Linux服务器"
echo "  2. 系统安装: sudo ./install.sh"
echo "  3. 用户安装: ./install_user.sh"
echo "  4. 创建分发包: ./package.sh"
echo ""
echo "注意: 这些二进制文件是为Linux AMD64构建的，无法在macOS上运行。"
