use anyhow::{Result, anyhow};
use std::fs;
use std::collections::HashMap;
use tokio::process::Command;
use common::{SystemInfoResult, DiskInfo, NetworkInterface};

pub struct SystemInfoCollector;

impl SystemInfoCollector {
    pub fn new() -> Self {
        Self
    }

    pub async fn collect(&self) -> Result<SystemInfoResult> {
        Ok(SystemInfoResult {
            hostname: self.get_hostname().await?,
            os_version: self.get_os_version().await?,
            kernel_version: self.get_kernel_version().await?,
            architecture: self.get_architecture().await?,
            cpu_info: self.get_cpu_info().await?,
            memory_total: self.get_memory_total().await?,
            memory_available: self.get_memory_available().await?,
            disk_info: self.get_disk_info().await?,
            network_interfaces: self.get_network_interfaces().await?,
        })
    }

    async fn get_hostname(&self) -> Result<String> {
        let output = Command::new("hostname").output().await?;
        if output.status.success() {
            Ok(String::from_utf8_lossy(&output.stdout).trim().to_string())
        } else {
            // 备用方法：读取 /etc/hostname
            match fs::read_to_string("/etc/hostname") {
                Ok(hostname) => Ok(hostname.trim().to_string()),
                Err(_) => Ok("unknown".to_string()),
            }
        }
    }

    async fn get_os_version(&self) -> Result<String> {
        // 尝试读取 /etc/os-release
        if let Ok(content) = fs::read_to_string("/etc/os-release") {
            let mut name = String::new();
            let mut version = String::new();
            
            for line in content.lines() {
                if line.starts_with("NAME=") {
                    name = line.replace("NAME=", "").trim_matches('"').to_string();
                } else if line.starts_with("VERSION=") {
                    version = line.replace("VERSION=", "").trim_matches('"').to_string();
                }
            }
            
            if !name.is_empty() {
                return Ok(format!("{} {}", name, version));
            }
        }

        // 备用方法：使用 lsb_release
        let output = Command::new("lsb_release").args(&["-d", "-s"]).output().await;
        if let Ok(output) = output {
            if output.status.success() {
                return Ok(String::from_utf8_lossy(&output.stdout).trim().to_string());
            }
        }

        Ok("Unknown Linux".to_string())
    }

    async fn get_kernel_version(&self) -> Result<String> {
        let output = Command::new("uname").args(&["-r"]).output().await?;
        if output.status.success() {
            Ok(String::from_utf8_lossy(&output.stdout).trim().to_string())
        } else {
            Ok("unknown".to_string())
        }
    }

    async fn get_architecture(&self) -> Result<String> {
        let output = Command::new("uname").args(&["-m"]).output().await?;
        if output.status.success() {
            Ok(String::from_utf8_lossy(&output.stdout).trim().to_string())
        } else {
            Ok("unknown".to_string())
        }
    }

    async fn get_cpu_info(&self) -> Result<String> {
        if let Ok(content) = fs::read_to_string("/proc/cpuinfo") {
            for line in content.lines() {
                if line.starts_with("model name") {
                    if let Some(cpu_name) = line.split(':').nth(1) {
                        return Ok(cpu_name.trim().to_string());
                    }
                }
            }
        }
        Ok("Unknown CPU".to_string())
    }

    async fn get_memory_total(&self) -> Result<u64> {
        self.parse_meminfo("MemTotal").await
    }

    async fn get_memory_available(&self) -> Result<u64> {
        // 尝试获取 MemAvailable，如果不存在则计算 MemFree + Buffers + Cached
        if let Ok(available) = self.parse_meminfo("MemAvailable").await {
            return Ok(available);
        }

        let free = self.parse_meminfo("MemFree").await.unwrap_or(0);
        let buffers = self.parse_meminfo("Buffers").await.unwrap_or(0);
        let cached = self.parse_meminfo("Cached").await.unwrap_or(0);
        
        Ok(free + buffers + cached)
    }

    async fn parse_meminfo(&self, field: &str) -> Result<u64> {
        let content = fs::read_to_string("/proc/meminfo")?;
        for line in content.lines() {
            if line.starts_with(field) {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    if let Ok(kb) = parts[1].parse::<u64>() {
                        return Ok(kb * 1024); // 转换为字节
                    }
                }
            }
        }
        Err(anyhow!("Field {} not found in /proc/meminfo", field))
    }

    async fn get_disk_info(&self) -> Result<Vec<DiskInfo>> {
        let output = Command::new("df").args(&["-h", "-T"]).output().await?;
        if !output.status.success() {
            return Ok(Vec::new());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let mut disks = Vec::new();

        for line in stdout.lines().skip(1) { // 跳过标题行
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 7 {
                let device = parts[0].to_string();
                let filesystem = parts[1].to_string();
                let mount_point = parts[6].to_string();
                
                // 解析大小（需要处理K, M, G, T后缀）
                let total_space = self.parse_size(parts[2]).unwrap_or(0);
                let used_space = self.parse_size(parts[3]).unwrap_or(0);
                let available_space = total_space.saturating_sub(used_space);

                disks.push(DiskInfo {
                    device,
                    mount_point,
                    total_space,
                    available_space,
                    filesystem,
                });
            }
        }

        Ok(disks)
    }

    fn parse_size(&self, size_str: &str) -> Option<u64> {
        if size_str.is_empty() || size_str == "-" {
            return None;
        }

        let size_str = size_str.to_uppercase();
        let (number_part, suffix) = if size_str.ends_with('K') {
            (&size_str[..size_str.len()-1], 1024)
        } else if size_str.ends_with('M') {
            (&size_str[..size_str.len()-1], 1024 * 1024)
        } else if size_str.ends_with('G') {
            (&size_str[..size_str.len()-1], 1024 * 1024 * 1024)
        } else if size_str.ends_with('T') {
            (&size_str[..size_str.len()-1], 1024_u64.pow(4))
        } else {
            (size_str.as_str(), 1)
        };

        number_part.parse::<f64>().ok().map(|n| (n * suffix as f64) as u64)
    }

    async fn get_network_interfaces(&self) -> Result<Vec<NetworkInterface>> {
        let mut interfaces = Vec::new();

        // 获取接口列表
        let output = Command::new("ip").args(&["link", "show"]).output().await?;
        if !output.status.success() {
            return Ok(interfaces);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let mut current_interface: Option<String> = None;

        for line in stdout.lines() {
            if let Some(interface_name) = self.parse_interface_line(line) {
                if let Some(name) = current_interface.take() {
                    // 处理前一个接口
                    if let Ok(interface) = self.get_interface_details(&name).await {
                        interfaces.push(interface);
                    }
                }
                current_interface = Some(interface_name);
            }
        }

        // 处理最后一个接口
        if let Some(name) = current_interface {
            if let Ok(interface) = self.get_interface_details(&name).await {
                interfaces.push(interface);
            }
        }

        Ok(interfaces)
    }

    fn parse_interface_line(&self, line: &str) -> Option<String> {
        if line.starts_with(char::is_numeric) {
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 2 {
                let name = parts[1].trim_end_matches(':');
                if name != "lo" { // 跳过回环接口
                    return Some(name.to_string());
                }
            }
        }
        None
    }

    async fn get_interface_details(&self, interface_name: &str) -> Result<NetworkInterface> {
        let mut ip_addresses = Vec::new();
        let mut mac_address = String::new();
        let mut is_up = false;

        // 获取IP地址
        let output = Command::new("ip")
            .args(&["addr", "show", interface_name])
            .output()
            .await?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            for line in stdout.lines() {
                if line.contains("inet ") && !line.contains("127.0.0.1") {
                    if let Some(ip) = self.extract_ip_from_line(line) {
                        ip_addresses.push(ip);
                    }
                } else if line.contains("link/ether") {
                    if let Some(mac) = self.extract_mac_from_line(line) {
                        mac_address = mac;
                    }
                } else if line.contains("state UP") {
                    is_up = true;
                }
            }
        }

        Ok(NetworkInterface {
            name: interface_name.to_string(),
            ip_addresses,
            mac_address,
            is_up,
        })
    }

    fn extract_ip_from_line(&self, line: &str) -> Option<String> {
        let parts: Vec<&str> = line.split_whitespace().collect();
        for part in parts {
            if part.contains('/') && part.contains('.') {
                if let Some(ip) = part.split('/').next() {
                    return Some(ip.to_string());
                }
            }
        }
        None
    }

    fn extract_mac_from_line(&self, line: &str) -> Option<String> {
        let parts: Vec<&str> = line.split_whitespace().collect();
        for part in parts {
            if part.matches(':').count() == 5 && part.len() == 17 {
                return Some(part.to_string());
            }
        }
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_system_info_collection() {
        let collector = SystemInfoCollector::new();
        let info = collector.collect().await.unwrap();
        
        assert!(!info.hostname.is_empty());
        assert!(!info.os_version.is_empty());
        assert!(info.memory_total > 0);
    }

    #[test]
    fn test_size_parsing() {
        let collector = SystemInfoCollector::new();
        
        assert_eq!(collector.parse_size("1K"), Some(1024));
        assert_eq!(collector.parse_size("1M"), Some(1024 * 1024));
        assert_eq!(collector.parse_size("1G"), Some(1024 * 1024 * 1024));
        assert_eq!(collector.parse_size("1.5G"), Some((1.5 * 1024.0 * 1024.0 * 1024.0) as u64));
    }
}
