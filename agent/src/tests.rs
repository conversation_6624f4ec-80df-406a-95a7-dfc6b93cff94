#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::time::Duration;

    #[tokio::test]
    async fn test_agent_initialization() {
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("agent_test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        let agent = Agent::new(server_url, &crypto_key, logger, 30).await;
        assert!(agent.is_ok());
        
        let agent = agent.unwrap();
        assert!(!agent.get_id().to_string().is_empty());
    }

    #[tokio::test]
    async fn test_command_executor() {
        use crate::executor::CommandExecutor;
        
        let executor = CommandExecutor::new();
        
        // 测试简单命令
        let result = executor.execute_command("echo", &["test".to_string()], None, None).await.unwrap();
        assert_eq!(result.exit_code, 0);
        assert_eq!(result.stdout.trim(), "test");
        
        // 测试带参数的命令
        let result = executor.execute_command("echo", &["hello".to_string(), "world".to_string()], None, None).await.unwrap();
        assert_eq!(result.exit_code, 0);
        assert_eq!(result.stdout.trim(), "hello world");
        
        // 测试工作目录
        let result = executor.execute_command("pwd", &[], Some("/tmp"), None).await.unwrap();
        assert_eq!(result.exit_code, 0);
        assert!(result.stdout.contains("/tmp"));
        
        // 测试超时
        let result = executor.execute_command("sleep", &["2".to_string()], None, Some(1)).await.unwrap();
        assert!(result.stderr.contains("timed out") || result.exit_code != 0);
    }

    #[tokio::test]
    async fn test_command_security() {
        use crate::executor::CommandExecutor;
        
        let executor = CommandExecutor::new();
        
        // 测试危险命令被阻止
        let dangerous_commands = ["rm", "rmdir", "dd", "mkfs", "shutdown", "reboot"];
        
        for cmd in &dangerous_commands {
            let result = executor.execute_command(cmd, &[], None, None).await.unwrap();
            assert_eq!(result.exit_code, -1);
            assert!(result.stderr.contains("not allowed"));
        }
        
        // 测试允许的命令
        let safe_commands = ["ls", "cat", "grep", "ps", "uname"];
        
        for cmd in &safe_commands {
            let result = executor.execute_command(cmd, &[], None, None).await.unwrap();
            // 这些命令应该被允许执行（即使可能失败）
            assert!(result.exit_code != -1 || !result.stderr.contains("not allowed"));
        }
    }

    #[tokio::test]
    async fn test_process_management() {
        use crate::executor::CommandExecutor;
        use common::ProcessAction;
        
        let executor = CommandExecutor::new();
        
        // 测试进程列表
        let result = executor.manage_process(ProcessAction::List).await.unwrap();
        assert!(!result.processes.is_empty());
        
        // 验证进程信息格式
        for process in &result.processes {
            assert!(process.pid > 0);
            assert!(!process.name.is_empty());
            assert!(process.cpu_usage >= 0.0);
            assert!(process.memory_usage >= 0);
        }
    }

    #[tokio::test]
    async fn test_system_info_collector() {
        use crate::system_info::SystemInfoCollector;
        
        let collector = SystemInfoCollector::new();
        let info = collector.collect().await.unwrap();
        
        // 验证基本系统信息
        assert!(!info.hostname.is_empty());
        assert!(!info.os_version.is_empty());
        assert!(!info.kernel_version.is_empty());
        assert!(!info.architecture.is_empty());
        assert!(!info.cpu_info.is_empty());
        
        // 验证内存信息
        assert!(info.memory_total > 0);
        assert!(info.memory_available <= info.memory_total);
        
        // 验证磁盘信息
        assert!(!info.disk_info.is_empty());
        for disk in &info.disk_info {
            assert!(!disk.device.is_empty());
            assert!(!disk.mount_point.is_empty());
            assert!(disk.total_space >= disk.available_space);
        }
        
        // 验证网络接口信息
        assert!(!info.network_interfaces.is_empty());
        for interface in &info.network_interfaces {
            assert!(!interface.name.is_empty());
            if interface.is_up && !interface.ip_addresses.is_empty() {
                // 验证IP地址格式
                for ip in &interface.ip_addresses {
                    assert!(ip.parse::<std::net::IpAddr>().is_ok());
                }
            }
        }
    }

    #[tokio::test]
    async fn test_file_manager() {
        use crate::file_manager::{FileManager, FileType};
        
        let temp_dir = TempDir::new().unwrap();
        let mut file_manager = FileManager::new();
        
        // 测试文件写入和读取
        let test_file = temp_dir.path().join("test.txt");
        let test_content = b"Hello, World!";
        
        file_manager.write_file_content(
            test_file.to_str().unwrap(),
            test_content
        ).await.unwrap();
        
        let read_content = file_manager.read_file_content(
            test_file.to_str().unwrap(),
            None
        ).await.unwrap();
        
        assert_eq!(test_content, read_content.as_slice());
        
        // 测试文件信息获取
        let file_info = file_manager.get_file_info(
            test_file.to_str().unwrap()
        ).await.unwrap();
        
        assert_eq!(file_info.size, test_content.len() as u64);
        assert!(matches!(file_info.file_type, FileType::File));
        
        // 测试目录操作
        let test_dir = temp_dir.path().join("test_dir");
        file_manager.create_directory(
            test_dir.to_str().unwrap()
        ).await.unwrap();
        
        assert!(test_dir.exists());
        
        // 测试目录列表
        let entries = file_manager.list_directory(
            temp_dir.path().to_str().unwrap()
        ).await.unwrap();
        
        assert!(entries.len() >= 2); // test.txt 和 test_dir
        
        // 测试文件复制
        let copy_file = temp_dir.path().join("test_copy.txt");
        file_manager.copy_file(
            test_file.to_str().unwrap(),
            copy_file.to_str().unwrap()
        ).await.unwrap();
        
        assert!(copy_file.exists());
        
        // 测试文件移动
        let move_file = temp_dir.path().join("test_moved.txt");
        file_manager.move_file(
            copy_file.to_str().unwrap(),
            move_file.to_str().unwrap()
        ).await.unwrap();
        
        assert!(!copy_file.exists());
        assert!(move_file.exists());
        
        // 测试文件删除
        file_manager.delete_file(
            move_file.to_str().unwrap()
        ).await.unwrap();
        
        assert!(!move_file.exists());
    }

    #[tokio::test]
    async fn test_file_chunked_upload() {
        use crate::file_manager::FileManager;
        
        let temp_dir = TempDir::new().unwrap();
        let mut file_manager = FileManager::new();
        
        let test_file_path = temp_dir.path().join("chunked_test.txt");
        let test_content = b"This is a test file for chunked upload testing. It contains multiple chunks of data.";
        
        // 模拟分块上传
        let chunk_size = 20;
        let total_chunks = (test_content.len() + chunk_size - 1) / chunk_size;
        
        for (chunk_index, chunk) in test_content.chunks(chunk_size).enumerate() {
            file_manager.receive_file_chunk(
                test_file_path.to_str().unwrap(),
                chunk_index as u32,
                total_chunks as u32,
                chunk
            ).await.unwrap();
        }
        
        // 验证文件内容
        tokio::time::sleep(Duration::from_millis(100)).await; // 等待文件完成
        
        if test_file_path.exists() {
            let content = tokio::fs::read(&test_file_path).await.unwrap();
            assert_eq!(content, test_content);
        }
        
        // 清理临时文件
        file_manager.cleanup_temp_files().await.unwrap();
    }

    #[tokio::test]
    async fn test_elf_loader() {
        use crate::elf_loader::ElfLoader;
        
        let loader = ElfLoader::new();
        
        // 测试ELF头验证
        let valid_elf_header = [
            0x7f, 0x45, 0x4c, 0x46, // ELF魔数
            0x02, // 64位
            0x01, // 小端
            0x01, // 版本
            0x00, // System V ABI
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 填充
            0x02, 0x00, // 可执行文件
            0x3e, 0x00, // x86-64
        ];
        
        assert!(loader.validate_elf_header(&valid_elf_header).is_ok());
        
        // 测试无效ELF头
        let invalid_header = [0x00, 0x00, 0x00, 0x00];
        assert!(loader.validate_elf_header(&invalid_header).is_err());
        
        // 测试ELF分析
        let mut full_elf = valid_elf_header.to_vec();
        full_elf.resize(64, 0); // 填充到最小ELF头大小
        
        let info = loader.analyze_elf(&full_elf).unwrap();
        assert_eq!(info.class, "64-bit");
        assert_eq!(info.endianness, "Little-endian");
        assert_eq!(info.file_type, "Executable");
        assert_eq!(info.machine, "x86-64");
    }

    #[tokio::test]
    async fn test_agent_message_handling() {
        use common::{Message, MessageType, CommandMessage};
        
        let temp_dir = TempDir::new().unwrap();
        let log_file = temp_dir.path().join("agent_test.log");
        let logger = Logger::new(log_file.to_string_lossy().to_string());
        
        let crypto_key = CryptoManager::generate_key();
        let server_url = "http://localhost:8080".to_string();
        
        let mut agent = Agent::new(server_url, &crypto_key, logger, 30).await.unwrap();
        
        // 创建测试命令消息
        let cmd_msg = CommandMessage {
            command: "echo".to_string(),
            args: vec!["test".to_string()],
            working_dir: None,
            timeout: Some(30),
        };
        
        let message = Message::new(MessageType::Command(cmd_msg));
        
        // 验证消息完整性
        assert!(message.verify_checksum());
        
        // 测试消息处理（这里我们不能直接调用handle_message，因为它是私有的）
        // 在实际实现中，可以考虑将其设为pub(crate)用于测试
    }

    #[tokio::test]
    async fn test_concurrent_operations() {
        use crate::executor::CommandExecutor;
        use std::sync::Arc;
        
        let executor = Arc::new(CommandExecutor::new());
        let mut handles = vec![];
        
        // 并发执行多个命令
        for i in 0..5 {
            let executor_clone = executor.clone();
            let handle = tokio::spawn(async move {
                executor_clone.execute_command(
                    "echo",
                    &[format!("test_{}", i)],
                    None,
                    None
                ).await
            });
            handles.push(handle);
        }
        
        // 等待所有命令完成
        for handle in handles {
            let result = handle.await.unwrap().unwrap();
            assert_eq!(result.exit_code, 0);
            assert!(result.stdout.contains("test_"));
        }
    }

    #[tokio::test]
    async fn test_error_handling() {
        use crate::executor::CommandExecutor;
        
        let executor = CommandExecutor::new();
        
        // 测试不存在的命令
        let result = executor.execute_command("nonexistent_command_12345", &[], None, None).await.unwrap();
        assert_ne!(result.exit_code, 0);
        assert!(!result.stderr.is_empty());
        
        // 测试无效参数
        let result = executor.execute_command("ls", &["--invalid-option-xyz".to_string()], None, None).await.unwrap();
        // ls可能会返回错误码，但不应该崩溃
        assert!(!result.stderr.is_empty() || result.exit_code != 0);
    }

    #[test]
    fn test_size_parsing() {
        use crate::system_info::SystemInfoCollector;
        
        let collector = SystemInfoCollector::new();
        
        assert_eq!(collector.parse_size("1K"), Some(1024));
        assert_eq!(collector.parse_size("1M"), Some(1024 * 1024));
        assert_eq!(collector.parse_size("1G"), Some(1024 * 1024 * 1024));
        assert_eq!(collector.parse_size("1.5G"), Some((1.5 * 1024.0 * 1024.0 * 1024.0) as u64));
        assert_eq!(collector.parse_size(""), None);
        assert_eq!(collector.parse_size("-"), None);
    }

    #[test]
    fn test_ps_line_parsing() {
        use crate::executor::CommandExecutor;
        
        let executor = CommandExecutor::new();
        let test_line = "root      1234  0.1  0.5  12345  6789 ?        S    10:00   0:01 /usr/bin/test arg1 arg2";
        
        let process = executor.parse_ps_line(test_line).unwrap();
        assert_eq!(process.pid, 1234);
        assert_eq!(process.cpu_usage, 0.1);
        assert_eq!(process.memory_usage, 6789 * 1024);
        assert_eq!(process.status, "S");
        assert!(process.name.contains("/usr/bin/test"));
    }

    #[tokio::test]
    async fn test_memory_management() {
        use crate::file_manager::FileManager;
        
        let mut file_manager = FileManager::new();
        
        // 创建大量临时文件来测试内存管理
        for i in 0..100 {
            let _ = file_manager.receive_file_chunk(
                &format!("/tmp/test_{}", i),
                0,
                1,
                &vec![0u8; 1024]
            ).await;
        }
        
        // 清理所有临时文件
        file_manager.cleanup_temp_files().await.unwrap();
    }
}
