use std::time::Duration;
use anyhow::{Result, anyhow};
use tokio::time::{sleep, interval};
use uuid::Uuid;
use common::{
    HttpCommunicator, Message, MessageType, CommandResult, FileData, ProcessInfo,
    SystemInfoResult, Logger, LogLevel, LogCategory, LogEntry
};
use crate::executor::CommandExecutor;
use crate::system_info::SystemInfoCollector;
use crate::file_manager::FileManager;
use crate::elf_loader::ElfLoader;

pub struct Agent {
    id: Uuid,
    communicator: HttpCommunicator,
    logger: Logger,
    heartbeat_interval: Duration,
    executor: CommandExecutor,
    system_info: SystemInfoCollector,
    file_manager: FileManager,
    elf_loader: ElfLoader,
    running: bool,
}

impl Agent {
    pub async fn new(
        server_url: String,
        crypto_key: &[u8],
        logger: Logger,
        heartbeat_interval: u64,
    ) -> Result<Self> {
        let communicator = HttpCommunicator::new(server_url, crypto_key)?;
        let id = Uuid::new_v4();
        
        Ok(Self {
            id,
            communicator,
            logger,
            heartbeat_interval: Duration::from_secs(heartbeat_interval),
            executor: CommandExecutor::new(),
            system_info: SystemInfoCollector::new(),
            file_manager: FileManager::new(),
            elf_loader: ElfLoader::new(),
            running: false,
        })
    }

    pub async fn start(&mut self) -> Result<()> {
        self.running = true;
        self.log_info("Agent starting up").await?;
        
        // 发送初始系统信息
        self.send_initial_info().await?;
        
        // 启动心跳任务
        let communicator_clone = self.communicator.clone();
        let heartbeat_interval = self.heartbeat_interval;
        tokio::spawn(async move {
            let mut interval = interval(heartbeat_interval);
            loop {
                interval.tick().await;
                if let Err(e) = communicator_clone.send_heartbeat().await {
                    log::error!("Failed to send heartbeat: {}", e);
                }
            }
        });

        // 主消息处理循环
        while self.running {
            match self.communicator.receive_message().await {
                Ok(Some(message)) => {
                    if let Err(e) = self.handle_message(message).await {
                        self.log_error(&format!("Error handling message: {}", e)).await?;
                    }
                },
                Ok(None) => {
                    // 没有消息，短暂休眠
                    sleep(Duration::from_millis(100)).await;
                },
                Err(e) => {
                    self.log_error(&format!("Communication error: {}", e)).await?;
                    sleep(Duration::from_secs(5)).await;
                }
            }
        }

        self.log_info("Agent shutting down").await?;
        Ok(())
    }

    pub fn stop(&mut self) {
        self.running = false;
    }

    async fn send_initial_info(&self) -> Result<()> {
        let system_info = self.system_info.collect().await?;
        let message = Message::new(MessageType::SystemInfoResult(system_info));
        self.communicator.send_message(&message).await?;
        Ok(())
    }

    async fn handle_message(&mut self, message: Message) -> Result<()> {
        // 验证消息完整性
        if !message.verify_checksum() {
            return Err(anyhow!("Message checksum verification failed"));
        }

        match message.message_type {
            MessageType::Command(cmd_msg) => {
                self.log_info(&format!("Executing command: {}", cmd_msg.command)).await?;
                
                let result = self.executor.execute_command(
                    &cmd_msg.command,
                    &cmd_msg.args,
                    cmd_msg.working_dir.as_deref(),
                    cmd_msg.timeout,
                ).await?;
                
                let response = Message::new(MessageType::CommandResult(result));
                self.communicator.send_message(&response).await?;
            },
            
            MessageType::FileUpload(upload_msg) => {
                self.log_info(&format!("Receiving file: {}", upload_msg.file_path)).await?;
                
                self.file_manager.receive_file_chunk(
                    &upload_msg.file_path,
                    upload_msg.chunk_index,
                    upload_msg.total_chunks,
                    &upload_msg.data,
                ).await?;
                
                // 发送确认
                let ack = Message::new(MessageType::Ack);
                self.communicator.send_message(&ack).await?;
            },
            
            MessageType::FileDownload(download_msg) => {
                self.log_info(&format!("Sending file: {}", download_msg.file_path)).await?;
                
                let file_data = self.file_manager.send_file(
                    &download_msg.file_path,
                    download_msg.chunk_size.unwrap_or(64 * 1024),
                ).await?;
                
                for chunk in file_data {
                    let response = Message::new(MessageType::FileData(chunk));
                    self.communicator.send_message(&response).await?;
                }
            },
            
            MessageType::ProcessManagement(process_msg) => {
                self.log_info(&format!("Process management: {:?}", process_msg.action)).await?;
                
                let process_info = self.executor.manage_process(process_msg.action).await?;
                let response = Message::new(MessageType::ProcessInfo(process_info));
                self.communicator.send_message(&response).await?;
            },
            
            MessageType::SystemInfo => {
                self.log_info("Collecting system information").await?;
                
                let system_info = self.system_info.collect().await?;
                let response = Message::new(MessageType::SystemInfoResult(system_info));
                self.communicator.send_message(&response).await?;
            },
            
            _ => {
                self.log_info(&format!("Received unsupported message type: {:?}", message.message_type)).await?;
            }
        }

        Ok(())
    }

    async fn log_info(&self, message: &str) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            level: LogLevel::Info,
            category: LogCategory::Communication,
            user_id: Some("agent".to_string()),
            session_id: Some(self.id),
            message: message.to_string(),
            details: None,
        };
        self.logger.log(entry)
    }

    async fn log_error(&self, message: &str) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            level: LogLevel::Error,
            category: LogCategory::Error,
            user_id: Some("agent".to_string()),
            session_id: Some(self.id),
            message: message.to_string(),
            details: None,
        };
        self.logger.log(entry)
    }

    pub fn get_id(&self) -> Uuid {
        self.id
    }

    pub async fn load_and_execute_elf(&mut self, elf_data: &[u8], args: &[String]) -> Result<i32> {
        self.log_info("Loading and executing ELF file").await?;
        self.elf_loader.load_and_execute(elf_data, args).await
    }
}
