use std::collections::HashMap;
use std::path::{Path, PathBuf};
use anyhow::{Result, anyhow};
use tokio::fs::{File, OpenOptions};
use tokio::io::{AsyncReadExt, AsyncWriteExt, AsyncSeekExt, SeekFrom};
use common::FileData;

pub struct FileManager {
    temp_files: HashMap<String, TempFileInfo>,
}

#[derive(Debug)]
struct TempFileInfo {
    path: PathBuf,
    total_chunks: u32,
    received_chunks: Vec<bool>,
    file_size: u64,
}

impl FileManager {
    pub fn new() -> Self {
        Self {
            temp_files: HashMap::new(),
        }
    }

    pub async fn receive_file_chunk(
        &mut self,
        file_path: &str,
        chunk_index: u32,
        total_chunks: u32,
        data: &[u8],
    ) -> Result<()> {
        // 创建或获取临时文件信息
        let temp_info = self.temp_files.entry(file_path.to_string())
            .or_insert_with(|| TempFileInfo {
                path: PathBuf::from(format!("/tmp/upload_{}", uuid::Uuid::new_v4())),
                total_chunks,
                received_chunks: vec![false; total_chunks as usize],
                file_size: 0,
            });

        // 验证chunk索引
        if chunk_index >= total_chunks {
            return Err(anyhow!("Invalid chunk index: {} >= {}", chunk_index, total_chunks));
        }

        // 写入chunk数据
        let mut file = OpenOptions::new()
            .create(true)
            .write(true)
            .open(&temp_info.path)
            .await?;

        // 计算chunk在文件中的位置
        let chunk_size = 64 * 1024; // 64KB
        let offset = chunk_index as u64 * chunk_size;
        
        file.seek(SeekFrom::Start(offset)).await?;
        file.write_all(data).await?;
        file.flush().await?;

        // 标记chunk为已接收
        temp_info.received_chunks[chunk_index as usize] = true;
        temp_info.file_size += data.len() as u64;

        // 检查是否所有chunk都已接收
        if temp_info.received_chunks.iter().all(|&received| received) {
            self.finalize_file_upload(file_path).await?;
        }

        Ok(())
    }

    async fn finalize_file_upload(&mut self, file_path: &str) -> Result<()> {
        if let Some(temp_info) = self.temp_files.remove(file_path) {
            // 确保目标目录存在
            if let Some(parent) = Path::new(file_path).parent() {
                tokio::fs::create_dir_all(parent).await?;
            }

            // 移动临时文件到最终位置
            tokio::fs::rename(&temp_info.path, file_path).await?;
            
            log::info!("File upload completed: {}", file_path);
        }

        Ok(())
    }

    pub async fn send_file(&self, file_path: &str, chunk_size: u32) -> Result<Vec<FileData>> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(anyhow!("File not found: {}", file_path));
        }

        let mut file = File::open(path).await?;
        let file_size = file.metadata().await?.len();
        
        let total_chunks = ((file_size + chunk_size as u64 - 1) / chunk_size as u64) as u32;
        let mut chunks = Vec::new();

        for chunk_index in 0..total_chunks {
            let mut buffer = vec![0u8; chunk_size as usize];
            let bytes_read = file.read(&mut buffer).await?;
            buffer.truncate(bytes_read);

            let is_complete = chunk_index == total_chunks - 1;

            chunks.push(FileData {
                file_path: file_path.to_string(),
                chunk_index,
                total_chunks,
                data: buffer,
                is_complete,
            });
        }

        Ok(chunks)
    }

    pub async fn delete_file(&self, file_path: &str) -> Result<()> {
        let path = Path::new(file_path);
        if path.exists() {
            if path.is_file() {
                tokio::fs::remove_file(path).await?;
            } else if path.is_dir() {
                tokio::fs::remove_dir_all(path).await?;
            }
            log::info!("File deleted: {}", file_path);
        }
        Ok(())
    }

    pub async fn create_directory(&self, dir_path: &str) -> Result<()> {
        tokio::fs::create_dir_all(dir_path).await?;
        log::info!("Directory created: {}", dir_path);
        Ok(())
    }

    pub async fn list_directory(&self, dir_path: &str) -> Result<Vec<FileInfo>> {
        let mut entries = tokio::fs::read_dir(dir_path).await?;
        let mut files = Vec::new();

        while let Some(entry) = entries.next_entry().await? {
            let metadata = entry.metadata().await?;
            let file_type = if metadata.is_file() {
                FileType::File
            } else if metadata.is_dir() {
                FileType::Directory
            } else {
                FileType::Other
            };

            files.push(FileInfo {
                name: entry.file_name().to_string_lossy().to_string(),
                path: entry.path().to_string_lossy().to_string(),
                size: metadata.len(),
                file_type,
                modified: metadata.modified().ok()
                    .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
                    .map(|d| d.as_secs()),
                permissions: self.get_permissions(&metadata),
            });
        }

        Ok(files)
    }

    fn get_permissions(&self, metadata: &std::fs::Metadata) -> String {
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mode = metadata.permissions().mode();
            format!("{:o}", mode & 0o777)
        }
        #[cfg(not(unix))]
        {
            "unknown".to_string()
        }
    }

    pub async fn get_file_info(&self, file_path: &str) -> Result<FileInfo> {
        let path = Path::new(file_path);
        let metadata = tokio::fs::metadata(path).await?;
        
        let file_type = if metadata.is_file() {
            FileType::File
        } else if metadata.is_dir() {
            FileType::Directory
        } else {
            FileType::Other
        };

        Ok(FileInfo {
            name: path.file_name()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
            path: file_path.to_string(),
            size: metadata.len(),
            file_type,
            modified: metadata.modified().ok()
                .and_then(|t| t.duration_since(std::time::UNIX_EPOCH).ok())
                .map(|d| d.as_secs()),
            permissions: self.get_permissions(&metadata),
        })
    }

    pub async fn copy_file(&self, source: &str, destination: &str) -> Result<()> {
        // 确保目标目录存在
        if let Some(parent) = Path::new(destination).parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        tokio::fs::copy(source, destination).await?;
        log::info!("File copied: {} -> {}", source, destination);
        Ok(())
    }

    pub async fn move_file(&self, source: &str, destination: &str) -> Result<()> {
        // 确保目标目录存在
        if let Some(parent) = Path::new(destination).parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        tokio::fs::rename(source, destination).await?;
        log::info!("File moved: {} -> {}", source, destination);
        Ok(())
    }

    pub async fn read_file_content(&self, file_path: &str, max_size: Option<usize>) -> Result<Vec<u8>> {
        let mut file = File::open(file_path).await?;
        let file_size = file.metadata().await?.len() as usize;
        
        let read_size = max_size.unwrap_or(file_size).min(file_size);
        let mut buffer = vec![0u8; read_size];
        
        file.read_exact(&mut buffer).await?;
        Ok(buffer)
    }

    pub async fn write_file_content(&self, file_path: &str, content: &[u8]) -> Result<()> {
        // 确保目标目录存在
        if let Some(parent) = Path::new(file_path).parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        let mut file = File::create(file_path).await?;
        file.write_all(content).await?;
        file.flush().await?;
        
        log::info!("File written: {} ({} bytes)", file_path, content.len());
        Ok(())
    }

    pub async fn cleanup_temp_files(&mut self) -> Result<()> {
        for (_, temp_info) in self.temp_files.drain() {
            if temp_info.path.exists() {
                let _ = tokio::fs::remove_file(&temp_info.path).await;
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct FileInfo {
    pub name: String,
    pub path: String,
    pub size: u64,
    pub file_type: FileType,
    pub modified: Option<u64>,
    pub permissions: String,
}

#[derive(Debug, Clone)]
pub enum FileType {
    File,
    Directory,
    Other,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_file_operations() {
        let temp_dir = TempDir::new().unwrap();
        let file_manager = FileManager::new();
        
        let test_file = temp_dir.path().join("test.txt");
        let content = b"Hello, World!";
        
        // 写入文件
        file_manager.write_file_content(
            test_file.to_str().unwrap(),
            content
        ).await.unwrap();
        
        // 读取文件
        let read_content = file_manager.read_file_content(
            test_file.to_str().unwrap(),
            None
        ).await.unwrap();
        
        assert_eq!(content, read_content.as_slice());
        
        // 获取文件信息
        let file_info = file_manager.get_file_info(
            test_file.to_str().unwrap()
        ).await.unwrap();
        
        assert_eq!(file_info.size, content.len() as u64);
        assert!(matches!(file_info.file_type, FileType::File));
    }

    #[tokio::test]
    async fn test_directory_operations() {
        let temp_dir = TempDir::new().unwrap();
        let file_manager = FileManager::new();
        
        let test_dir = temp_dir.path().join("test_dir");
        
        // 创建目录
        file_manager.create_directory(
            test_dir.to_str().unwrap()
        ).await.unwrap();
        
        assert!(test_dir.exists());
        assert!(test_dir.is_dir());
        
        // 列出目录内容
        let entries = file_manager.list_directory(
            temp_dir.path().to_str().unwrap()
        ).await.unwrap();
        
        assert_eq!(entries.len(), 1);
        assert_eq!(entries[0].name, "test_dir");
    }
}
