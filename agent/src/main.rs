mod agent;
mod executor;
mod system_info;
mod file_manager;
mod elf_loader;

#[cfg(test)]
mod tests;

use anyhow::Result;
use clap::{Parser, Subcommand};
use common::{CryptoManager, Logger};
use agent::Agent;
use std::path::PathBuf;

#[derive(Parser)]
#[command(name = "agent")]
#[command(about = "Ubuntu Remote Control - Agent")]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,
    
    #[arg(short, long, default_value = "agent.log")]
    log_file: PathBuf,
    
    #[arg(short, long, default_value = "http://localhost:8080")]
    server_url: String,
    
    #[arg(short, long)]
    crypto_key: Option<String>,
    
    #[arg(short, long, default_value = "30")]
    heartbeat_interval: u64,
    
    #[arg(short, long)]
    daemon: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// Start agent in foreground
    Start,
    /// Install agent as system service
    Install,
    /// Uninstall agent service
    Uninstall,
    /// Check agent status
    Status,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // 初始化日志
    env_logger::init();
    let logger = Logger::new(cli.log_file.to_string_lossy().to_string());
    logger.ensure_log_directory()?;
    
    // 初始化加密密钥
    let crypto_key = if let Some(key) = cli.crypto_key {
        key.into_bytes()
    } else {
        // 在生产环境中，这应该从安全的地方获取
        CryptoManager::generate_key()
    };
    
    // 如果以daemon模式运行，则fork到后台
    if cli.daemon {
        daemonize()?;
    }
    
    match cli.command.unwrap_or(Commands::Start) {
        Commands::Start => {
            println!("Starting agent...");
            let mut agent = Agent::new(cli.server_url, &crypto_key, logger, cli.heartbeat_interval).await?;
            agent.start().await?;
        },
        Commands::Install => {
            println!("Installing agent as system service...");
            install_service(&cli)?;
        },
        Commands::Uninstall => {
            println!("Uninstalling agent service...");
            uninstall_service()?;
        },
        Commands::Status => {
            println!("Checking agent status...");
            check_status()?;
        },
    }
    
    Ok(())
}

fn daemonize() -> Result<()> {
    use std::process;
    
    // 简单的daemon化实现
    match unsafe { libc::fork() } {
        -1 => return Err(anyhow::anyhow!("Fork failed")),
        0 => {
            // 子进程
            unsafe {
                libc::setsid();
            }
        },
        _ => {
            // 父进程退出
            process::exit(0);
        }
    }
    
    Ok(())
}

fn install_service(cli: &Cli) -> Result<()> {
    let current_exe = std::env::current_exe()?;
    let service_content = format!(r#"[Unit]
Description=Ubuntu Remote Control Agent
After=network.target

[Service]
Type=simple
User=root
ExecStart={} --daemon --server-url {} --log-file {}
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target"#, 
        current_exe.display(),
        cli.server_url,
        cli.log_file.display()
    );
    
    std::fs::write("/etc/systemd/system/remote-agent.service", service_content)?;
    
    // 启用并启动服务
    std::process::Command::new("systemctl")
        .args(&["daemon-reload"])
        .status()?;
    
    std::process::Command::new("systemctl")
        .args(&["enable", "remote-agent"])
        .status()?;
    
    std::process::Command::new("systemctl")
        .args(&["start", "remote-agent"])
        .status()?;
    
    println!("Agent service installed and started successfully");
    Ok(())
}

fn uninstall_service() -> Result<()> {
    // 停止并禁用服务
    std::process::Command::new("systemctl")
        .args(&["stop", "remote-agent"])
        .status()?;
    
    std::process::Command::new("systemctl")
        .args(&["disable", "remote-agent"])
        .status()?;
    
    // 删除服务文件
    if std::path::Path::new("/etc/systemd/system/remote-agent.service").exists() {
        std::fs::remove_file("/etc/systemd/system/remote-agent.service")?;
    }
    
    std::process::Command::new("systemctl")
        .args(&["daemon-reload"])
        .status()?;
    
    println!("Agent service uninstalled successfully");
    Ok(())
}

fn check_status() -> Result<()> {
    let output = std::process::Command::new("systemctl")
        .args(&["status", "remote-agent"])
        .output()?;
    
    println!("{}", String::from_utf8_lossy(&output.stdout));
    
    if !output.stderr.is_empty() {
        eprintln!("{}", String::from_utf8_lossy(&output.stderr));
    }
    
    Ok(())
}
