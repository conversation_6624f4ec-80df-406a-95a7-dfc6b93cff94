use std::process::Stdio;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::process::Command;
use tokio::time::timeout;
use common::{CommandResult, ProcessAction, ProcessInfo, ProcessDetails};

pub struct CommandExecutor {
    // 可以添加配置选项，如允许的命令列表等
}

impl CommandExecutor {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn execute_command(
        &self,
        command: &str,
        args: &[String],
        working_dir: Option<&str>,
        timeout_secs: Option<u64>,
    ) -> Result<CommandResult> {
        let start_time = Instant::now();
        
        // 安全检查：验证命令是否被允许
        if !self.is_command_allowed(command) {
            return Ok(CommandResult {
                exit_code: -1,
                stdout: String::new(),
                stderr: format!("Command not allowed: {}", command),
                execution_time: 0,
            });
        }

        let mut cmd = Command::new(command);
        cmd.args(args)
           .stdout(Stdio::piped())
           .stderr(Stdio::piped());

        if let Some(dir) = working_dir {
            cmd.current_dir(dir);
        }

        let timeout_duration = Duration::from_secs(timeout_secs.unwrap_or(30));
        
        let result = timeout(timeout_duration, cmd.output()).await;
        
        let execution_time = start_time.elapsed().as_millis() as u64;

        match result {
            Ok(Ok(output)) => {
                Ok(CommandResult {
                    exit_code: output.status.code().unwrap_or(-1),
                    stdout: String::from_utf8_lossy(&output.stdout).to_string(),
                    stderr: String::from_utf8_lossy(&output.stderr).to_string(),
                    execution_time,
                })
            },
            Ok(Err(e)) => {
                Ok(CommandResult {
                    exit_code: -1,
                    stdout: String::new(),
                    stderr: format!("Failed to execute command: {}", e),
                    execution_time,
                })
            },
            Err(_) => {
                Ok(CommandResult {
                    exit_code: -1,
                    stdout: String::new(),
                    stderr: "Command execution timed out".to_string(),
                    execution_time,
                })
            }
        }
    }

    pub async fn manage_process(&self, action: ProcessAction) -> Result<ProcessInfo> {
        match action {
            ProcessAction::List => {
                self.list_processes().await
            },
            ProcessAction::Kill => {
                // 实现进程终止逻辑
                Err(anyhow!("Process kill not implemented"))
            },
            ProcessAction::Start(command, args) => {
                // 启动新进程
                self.start_process(&command, &args).await
            }
        }
    }

    async fn list_processes(&self) -> Result<ProcessInfo> {
        let output = Command::new("ps")
            .args(&["aux", "--no-headers"])
            .output()
            .await?;

        if !output.status.success() {
            return Err(anyhow!("Failed to list processes"));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let mut processes = Vec::new();

        for line in stdout.lines() {
            if let Some(process) = self.parse_ps_line(line) {
                processes.push(process);
            }
        }

        Ok(ProcessInfo { processes })
    }

    fn parse_ps_line(&self, line: &str) -> Option<ProcessDetails> {
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() >= 11 {
            // ps aux格式: USER PID %CPU %MEM VSZ RSS TTY STAT START TIME COMMAND
            if let Ok(pid) = parts[1].parse::<u32>() {
                if let Ok(cpu_usage) = parts[2].parse::<f32>() {
                    if let Ok(memory_kb) = parts[5].parse::<u64>() {
                        return Some(ProcessDetails {
                            pid,
                            name: parts[10..].join(" "),
                            cpu_usage,
                            memory_usage: memory_kb * 1024, // 转换为字节
                            status: parts[7].to_string(),
                        });
                    }
                }
            }
        }
        None
    }

    async fn start_process(&self, command: &str, args: &[String]) -> Result<ProcessInfo> {
        if !self.is_command_allowed(command) {
            return Err(anyhow!("Command not allowed: {}", command));
        }

        let mut cmd = Command::new(command);
        cmd.args(args)
           .stdout(Stdio::null())
           .stderr(Stdio::null());

        let child = cmd.spawn()?;
        let pid = child.id().unwrap_or(0);

        // 返回新启动的进程信息
        let process = ProcessDetails {
            pid,
            name: format!("{} {}", command, args.join(" ")),
            cpu_usage: 0.0,
            memory_usage: 0,
            status: "Running".to_string(),
        };

        Ok(ProcessInfo {
            processes: vec![process],
        })
    }

    fn is_command_allowed(&self, command: &str) -> bool {
        // 实现命令白名单或黑名单逻辑
        // 这里是一个简单的实现，实际应用中应该更严格
        let allowed_commands = [
            "ls", "cat", "grep", "find", "ps", "top", "df", "free", "uname",
            "whoami", "id", "pwd", "date", "uptime", "netstat", "ss",
            "systemctl", "service", "journalctl", "dmesg", "lsof",
            "iptables", "ip", "ifconfig", "ping", "curl", "wget",
        ];

        let dangerous_commands = [
            "rm", "rmdir", "dd", "mkfs", "fdisk", "parted",
            "shutdown", "reboot", "halt", "poweroff",
            "passwd", "su", "sudo", "chmod", "chown",
        ];

        // 检查是否在危险命令列表中
        if dangerous_commands.contains(&command) {
            return false;
        }

        // 检查是否在允许命令列表中
        allowed_commands.contains(&command) || 
        command.starts_with("/usr/bin/") ||
        command.starts_with("/bin/")
    }

    pub async fn execute_shell_script(&self, script: &str, timeout_secs: Option<u64>) -> Result<CommandResult> {
        // 将脚本写入临时文件
        let script_path = format!("/tmp/script_{}.sh", uuid::Uuid::new_v4());
        tokio::fs::write(&script_path, script).await?;

        // 设置执行权限
        Command::new("chmod")
            .args(&["+x", &script_path])
            .output()
            .await?;

        // 执行脚本
        let result = self.execute_command("bash", &[script_path.clone()], None, timeout_secs).await;

        // 清理临时文件
        let _ = tokio::fs::remove_file(&script_path).await;

        result
    }

    pub async fn kill_process(&self, pid: u32) -> Result<()> {
        let output = Command::new("kill")
            .args(&["-9", &pid.to_string()])
            .output()
            .await?;

        if !output.status.success() {
            return Err(anyhow!("Failed to kill process {}: {}", 
                pid, String::from_utf8_lossy(&output.stderr)));
        }

        Ok(())
    }

    pub async fn get_process_info(&self, pid: u32) -> Result<Option<ProcessDetails>> {
        let processes = self.list_processes().await?;
        Ok(processes.processes.into_iter().find(|p| p.pid == pid))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_command_execution() {
        let executor = CommandExecutor::new();
        let result = executor.execute_command("echo", &["hello".to_string()], None, None).await.unwrap();
        
        assert_eq!(result.exit_code, 0);
        assert_eq!(result.stdout.trim(), "hello");
    }

    #[tokio::test]
    async fn test_command_security() {
        let executor = CommandExecutor::new();
        let result = executor.execute_command("rm", &["-rf".to_string(), "/".to_string()], None, None).await.unwrap();
        
        assert_eq!(result.exit_code, -1);
        assert!(result.stderr.contains("not allowed"));
    }

    #[test]
    fn test_ps_line_parsing() {
        let executor = CommandExecutor::new();
        let line = "root      1234  0.1  0.5  12345  6789 ?        S    10:00   0:01 /usr/bin/test";
        let process = executor.parse_ps_line(line).unwrap();
        
        assert_eq!(process.pid, 1234);
        assert_eq!(process.cpu_usage, 0.1);
        assert_eq!(process.memory_usage, 6789 * 1024);
    }
}
