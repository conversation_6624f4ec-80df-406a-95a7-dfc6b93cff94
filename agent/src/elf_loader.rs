use anyhow::{Result, anyhow};
use std::fs::File;
use std::io::Write;
use std::os::unix::fs::PermissionsExt;
use std::path::PathBuf;
use std::process::Command;
use tokio::process::Command as AsyncCommand;
use uuid::Uuid;

pub struct ElfLoader {
    temp_dir: PathBuf,
}

impl ElfLoader {
    pub fn new() -> Self {
        Self {
            temp_dir: PathBuf::from("/tmp"),
        }
    }

    pub async fn load_and_execute(&self, elf_data: &[u8], args: &[String]) -> Result<i32> {
        // 验证ELF文件头
        self.validate_elf_header(elf_data)?;
        
        // 创建临时文件
        let temp_file_path = self.create_temp_file(elf_data).await?;
        
        // 设置执行权限
        self.set_executable_permissions(&temp_file_path)?;
        
        // 执行ELF文件
        let exit_code = self.execute_elf(&temp_file_path, args).await?;
        
        // 清理临时文件
        self.cleanup_temp_file(&temp_file_path).await?;
        
        Ok(exit_code)
    }

    fn validate_elf_header(&self, data: &[u8]) -> Result<()> {
        if data.len() < 16 {
            return Err(anyhow!("File too small to be a valid ELF"));
        }

        // 检查ELF魔数
        if &data[0..4] != b"\x7fELF" {
            return Err(anyhow!("Invalid ELF magic number"));
        }

        // 检查架构（64位）
        if data[4] != 2 {
            return Err(anyhow!("Only 64-bit ELF files are supported"));
        }

        // 检查字节序（小端）
        if data[5] != 1 {
            return Err(anyhow!("Only little-endian ELF files are supported"));
        }

        // 检查版本
        if data[6] != 1 {
            return Err(anyhow!("Invalid ELF version"));
        }

        // 检查文件类型（可执行文件）
        let file_type = u16::from_le_bytes([data[16], data[17]]);
        if file_type != 2 && file_type != 3 {
            return Err(anyhow!("ELF file must be executable or shared object"));
        }

        // 检查机器架构（x86-64）
        let machine = u16::from_le_bytes([data[18], data[19]]);
        if machine != 0x3E {
            return Err(anyhow!("Only x86-64 architecture is supported"));
        }

        Ok(())
    }

    async fn create_temp_file(&self, data: &[u8]) -> Result<PathBuf> {
        let file_name = format!("elf_exec_{}", Uuid::new_v4());
        let temp_path = self.temp_dir.join(file_name);
        
        let mut file = File::create(&temp_path)?;
        file.write_all(data)?;
        file.flush()?;
        
        Ok(temp_path)
    }

    fn set_executable_permissions(&self, file_path: &PathBuf) -> Result<()> {
        let mut perms = std::fs::metadata(file_path)?.permissions();
        perms.set_mode(0o755); // rwxr-xr-x
        std::fs::set_permissions(file_path, perms)?;
        Ok(())
    }

    async fn execute_elf(&self, file_path: &PathBuf, args: &[String]) -> Result<i32> {
        // 安全检查：验证文件路径
        if !file_path.starts_with(&self.temp_dir) {
            return Err(anyhow!("Invalid file path"));
        }

        // 执行ELF文件
        let mut cmd = AsyncCommand::new(file_path);
        cmd.args(args);
        
        // 设置安全的环境变量
        cmd.env_clear();
        cmd.env("PATH", "/usr/bin:/bin");
        cmd.env("HOME", "/tmp");
        
        let output = cmd.output().await?;
        
        // 记录执行结果
        log::info!("ELF execution completed with exit code: {:?}", output.status.code());
        
        if !output.stdout.is_empty() {
            log::info!("ELF stdout: {}", String::from_utf8_lossy(&output.stdout));
        }
        
        if !output.stderr.is_empty() {
            log::warn!("ELF stderr: {}", String::from_utf8_lossy(&output.stderr));
        }
        
        Ok(output.status.code().unwrap_or(-1))
    }

    async fn cleanup_temp_file(&self, file_path: &PathBuf) -> Result<()> {
        if file_path.exists() {
            tokio::fs::remove_file(file_path).await?;
        }
        Ok(())
    }

    pub async fn load_shared_library(&self, so_data: &[u8]) -> Result<PathBuf> {
        // 验证共享库文件
        self.validate_shared_library(so_data)?;
        
        // 创建临时共享库文件
        let lib_name = format!("lib_{}.so", Uuid::new_v4());
        let lib_path = self.temp_dir.join(lib_name);
        
        let mut file = File::create(&lib_path)?;
        file.write_all(so_data)?;
        file.flush()?;
        
        // 设置适当的权限
        let mut perms = std::fs::metadata(&lib_path)?.permissions();
        perms.set_mode(0o644); // rw-r--r--
        std::fs::set_permissions(&lib_path, perms)?;
        
        Ok(lib_path)
    }

    fn validate_shared_library(&self, data: &[u8]) -> Result<()> {
        if data.len() < 16 {
            return Err(anyhow!("File too small to be a valid shared library"));
        }

        // 检查ELF魔数
        if &data[0..4] != b"\x7fELF" {
            return Err(anyhow!("Invalid ELF magic number"));
        }

        // 检查文件类型（共享对象）
        let file_type = u16::from_le_bytes([data[16], data[17]]);
        if file_type != 3 {
            return Err(anyhow!("File must be a shared object"));
        }

        Ok(())
    }

    pub async fn execute_with_library(&self, elf_data: &[u8], lib_data: &[u8], args: &[String]) -> Result<i32> {
        // 加载共享库
        let lib_path = self.load_shared_library(lib_data).await?;
        
        // 创建ELF文件
        let elf_path = self.create_temp_file(elf_data).await?;
        self.set_executable_permissions(&elf_path)?;
        
        // 设置LD_LIBRARY_PATH并执行
        let mut cmd = AsyncCommand::new(&elf_path);
        cmd.args(args);
        cmd.env("LD_LIBRARY_PATH", self.temp_dir.to_str().unwrap_or("/tmp"));
        
        let output = cmd.output().await?;
        let exit_code = output.status.code().unwrap_or(-1);
        
        // 清理文件
        self.cleanup_temp_file(&elf_path).await?;
        self.cleanup_temp_file(&lib_path).await?;
        
        Ok(exit_code)
    }

    pub fn analyze_elf(&self, data: &[u8]) -> Result<ElfInfo> {
        if data.len() < 64 {
            return Err(anyhow!("ELF file too small"));
        }

        let mut info = ElfInfo::default();
        
        // 解析ELF头
        info.class = match data[4] {
            1 => "32-bit".to_string(),
            2 => "64-bit".to_string(),
            _ => "Unknown".to_string(),
        };
        
        info.endianness = match data[5] {
            1 => "Little-endian".to_string(),
            2 => "Big-endian".to_string(),
            _ => "Unknown".to_string(),
        };
        
        info.version = data[6];
        
        info.os_abi = match data[7] {
            0 => "System V".to_string(),
            3 => "Linux".to_string(),
            _ => format!("Unknown ({})", data[7]),
        };
        
        let file_type = u16::from_le_bytes([data[16], data[17]]);
        info.file_type = match file_type {
            1 => "Relocatable".to_string(),
            2 => "Executable".to_string(),
            3 => "Shared object".to_string(),
            4 => "Core".to_string(),
            _ => format!("Unknown ({})", file_type),
        };
        
        let machine = u16::from_le_bytes([data[18], data[19]]);
        info.machine = match machine {
            0x3E => "x86-64".to_string(),
            0x28 => "ARM".to_string(),
            0xB7 => "AArch64".to_string(),
            _ => format!("Unknown ({})", machine),
        };
        
        // 入口点地址（64位）
        if data.len() >= 32 {
            let entry_bytes = &data[24..32];
            info.entry_point = u64::from_le_bytes([
                entry_bytes[0], entry_bytes[1], entry_bytes[2], entry_bytes[3],
                entry_bytes[4], entry_bytes[5], entry_bytes[6], entry_bytes[7],
            ]);
        }
        
        Ok(info)
    }

    pub async fn extract_strings(&self, data: &[u8]) -> Result<Vec<String>> {
        // 创建临时文件
        let temp_path = self.create_temp_file(data).await?;
        
        // 使用strings命令提取字符串
        let output = Command::new("strings")
            .arg(&temp_path)
            .output()?;
        
        // 清理临时文件
        self.cleanup_temp_file(&temp_path).await?;
        
        if output.status.success() {
            let strings_output = String::from_utf8_lossy(&output.stdout);
            Ok(strings_output.lines().map(|s| s.to_string()).collect())
        } else {
            Err(anyhow!("Failed to extract strings"))
        }
    }
}

#[derive(Debug, Default)]
pub struct ElfInfo {
    pub class: String,
    pub endianness: String,
    pub version: u8,
    pub os_abi: String,
    pub file_type: String,
    pub machine: String,
    pub entry_point: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_elf_header_validation() {
        let loader = ElfLoader::new();
        
        // 有效的ELF头（简化）
        let valid_elf = [
            0x7f, 0x45, 0x4c, 0x46, // ELF魔数
            0x02, // 64位
            0x01, // 小端
            0x01, // 版本
            0x00, // System V ABI
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 填充
            0x02, 0x00, // 可执行文件
            0x3e, 0x00, // x86-64
        ];
        
        assert!(loader.validate_elf_header(&valid_elf).is_ok());
        
        // 无效的魔数
        let invalid_elf = [0x00, 0x00, 0x00, 0x00];
        assert!(loader.validate_elf_header(&invalid_elf).is_err());
    }

    #[test]
    fn test_elf_analysis() {
        let loader = ElfLoader::new();
        
        let elf_data = vec![
            0x7f, 0x45, 0x4c, 0x46, // ELF魔数
            0x02, // 64位
            0x01, // 小端
            0x01, // 版本
            0x03, // Linux ABI
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 填充
            0x02, 0x00, // 可执行文件
            0x3e, 0x00, // x86-64
            0x01, 0x00, 0x00, 0x00, // 版本
            0x00, 0x10, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, // 入口点
        ];
        
        // 填充到64字节
        let mut full_elf = elf_data;
        full_elf.resize(64, 0);
        
        let info = loader.analyze_elf(&full_elf).unwrap();
        assert_eq!(info.class, "64-bit");
        assert_eq!(info.endianness, "Little-endian");
        assert_eq!(info.os_abi, "Linux");
        assert_eq!(info.file_type, "Executable");
        assert_eq!(info.machine, "x86-64");
    }
}
