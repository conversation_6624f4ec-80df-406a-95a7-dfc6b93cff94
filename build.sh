#!/bin/bash

# Ubuntu服务器远程控制方案构建脚本

set -e  # 遇到错误时退出

echo "=== Ubuntu Remote Control Build Script ==="
echo "开始构建项目..."

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    echo "错误: 未找到Cargo。请安装Rust工具链。"
    echo "安装命令: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

echo "Rust版本信息:"
rustc --version
cargo --version

# 清理之前的构建
echo "清理之前的构建..."
cargo clean

# 检查代码格式
echo "检查代码格式..."
if command -v cargo-fmt &> /dev/null; then
    cargo fmt --check || {
        echo "代码格式检查失败，正在自动格式化..."
        cargo fmt
    }
else
    echo "警告: 未安装rustfmt，跳过格式检查"
fi

# 代码静态分析
echo "运行代码静态分析..."
if command -v cargo-clippy &> /dev/null; then
    cargo clippy --all-targets --all-features -- -D warnings || {
        echo "警告: Clippy检查发现问题，但继续构建..."
    }
else
    echo "警告: 未安装clippy，跳过静态分析"
fi

# 构建项目
echo "构建项目..."
echo "1. 构建common库..."
cargo build --package common

echo "2. 构建controller..."
cargo build --package controller

echo "3. 构建agent..."
cargo build --package agent

echo "4. 构建release版本..."
cargo build --release

# 运行测试
echo "运行测试..."
echo "1. 运行单元测试..."
cargo test --package common --lib

echo "2. 运行controller测试..."
cargo test --package controller --lib

echo "3. 运行agent测试..."
cargo test --package agent --lib

echo "4. 运行集成测试..."
cargo test --test integration_tests

# 检查二进制文件
echo "检查生成的二进制文件..."
if [ -f "target/release/controller" ]; then
    echo "✓ Controller二进制文件已生成: target/release/controller"
    ls -lh target/release/controller
else
    echo "✗ Controller二进制文件未找到"
fi

if [ -f "target/release/agent" ]; then
    echo "✓ Agent二进制文件已生成: target/release/agent"
    ls -lh target/release/agent
else
    echo "✗ Agent二进制文件未找到"
fi

# 生成文档
echo "生成文档..."
cargo doc --no-deps --open || echo "文档生成完成（未自动打开）"

# 安全检查
echo "运行安全检查..."
if command -v cargo-audit &> /dev/null; then
    cargo audit || echo "警告: 安全检查发现问题"
else
    echo "警告: 未安装cargo-audit，跳过安全检查"
    echo "安装命令: cargo install cargo-audit"
fi

# 创建部署包
echo "创建部署包..."
mkdir -p dist
cp target/release/controller dist/ 2>/dev/null || echo "Controller未复制到dist目录"
cp target/release/agent dist/ 2>/dev/null || echo "Agent未复制到dist目录"
cp README.md dist/ 2>/dev/null || echo "README未复制到dist目录"

# 创建安装脚本
cat > dist/install.sh << 'EOF'
#!/bin/bash
# Ubuntu Remote Control 安装脚本

echo "安装Ubuntu Remote Control..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 复制二进制文件
cp controller /usr/local/bin/
cp agent /usr/local/bin/
chmod +x /usr/local/bin/controller
chmod +x /usr/local/bin/agent

# 创建配置目录
mkdir -p /etc/remote-control
mkdir -p /var/log/remote-control

echo "安装完成！"
echo "使用方法:"
echo "  控制端: /usr/local/bin/controller --help"
echo "  受控端: /usr/local/bin/agent --help"
EOF

chmod +x dist/install.sh

echo "=== 构建完成 ==="
echo "生成的文件:"
echo "  - target/release/controller (控制端)"
echo "  - target/release/agent (受控端)"
echo "  - dist/ (部署包)"
echo "  - target/doc/ (文档)"
echo ""
echo "使用方法:"
echo "  1. 运行控制端: ./target/release/controller --help"
echo "  2. 运行受控端: ./target/release/agent --help"
echo "  3. 安装到系统: sudo ./dist/install.sh"
echo ""
echo "注意: 这是一个演示项目，请在授权环境中使用。"
