#!/bin/bash

# Linux AMD64 交叉编译构建脚本
# 在 macOS 上构建适用于 Linux AMD64 的二进制文件

set -e  # 遇到错误时退出

echo "=== Linux AMD64 交叉编译构建脚本 ==="
echo "在 macOS 上为 Linux AMD64 构建项目..."

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    echo "错误: 未找到Cargo。请安装Rust工具链。"
    echo "安装命令: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

echo "Rust版本信息:"
rustc --version
cargo --version

# 检查并安装Linux目标平台
TARGET="x86_64-unknown-linux-gnu"
echo "检查Linux目标平台: $TARGET"

if ! rustup target list --installed | grep -q "$TARGET"; then
    echo "安装Linux目标平台: $TARGET"
    rustup target add $TARGET
else
    echo "✓ Linux目标平台已安装: $TARGET"
fi

# 检查交叉编译工具链
echo "检查交叉编译工具链..."
if ! command -v x86_64-linux-gnu-gcc &> /dev/null; then
    echo "警告: 未找到 x86_64-linux-gnu-gcc"
    echo "建议安装交叉编译工具链:"
    echo "  brew install FiloSottile/musl-cross/musl-cross"
    echo "或者使用 musl 目标 (静态链接):"
    echo "  rustup target add x86_64-unknown-linux-musl"
    echo ""
    echo "继续使用默认配置..."
fi

# 清理之前的构建
echo "清理之前的构建..."
cargo clean

# 设置交叉编译环境变量
export PKG_CONFIG_ALLOW_CROSS=1

# 构建项目 (Linux AMD64)
echo "为 Linux AMD64 构建项目..."
echo "目标平台: $TARGET"

echo "1. 构建common库..."
cargo build --package common --target $TARGET

echo "2. 构建controller..."
cargo build --package controller --target $TARGET

echo "3. 构建agent..."
cargo build --package agent --target $TARGET

echo "4. 构建release版本..."
cargo build --release --target $TARGET

# 检查二进制文件
echo "检查生成的Linux二进制文件..."
LINUX_TARGET_DIR="target/$TARGET/release"

if [ -f "$LINUX_TARGET_DIR/controller" ]; then
    echo "✓ Controller Linux二进制文件已生成: $LINUX_TARGET_DIR/controller"
    ls -lh "$LINUX_TARGET_DIR/controller"
    file "$LINUX_TARGET_DIR/controller"
else
    echo "✗ Controller Linux二进制文件未找到"
fi

if [ -f "$LINUX_TARGET_DIR/agent" ]; then
    echo "✓ Agent Linux二进制文件已生成: $LINUX_TARGET_DIR/agent"
    ls -lh "$LINUX_TARGET_DIR/agent"
    file "$LINUX_TARGET_DIR/agent"
else
    echo "✗ Agent Linux二进制文件未找到"
fi

# 创建Linux部署包
echo "创建Linux部署包..."
DIST_DIR="dist-linux"
mkdir -p $DIST_DIR

if [ -f "$LINUX_TARGET_DIR/controller" ]; then
    cp "$LINUX_TARGET_DIR/controller" $DIST_DIR/
fi

if [ -f "$LINUX_TARGET_DIR/agent" ]; then
    cp "$LINUX_TARGET_DIR/agent" $DIST_DIR/
fi

cp README.md $DIST_DIR/ 2>/dev/null || echo "README未复制到$DIST_DIR目录"

# 创建Linux安装脚本
cat > $DIST_DIR/install.sh << 'EOF'
#!/bin/bash
# Ubuntu Remote Control Linux 安装脚本

echo "安装Ubuntu Remote Control (Linux AMD64)..."

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "x86_64" ]; then
    echo "警告: 当前架构是 $ARCH，此程序为 x86_64 构建"
fi

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 复制二进制文件
if [ -f "controller" ]; then
    cp controller /usr/local/bin/
    chmod +x /usr/local/bin/controller
    echo "✓ Controller已安装到 /usr/local/bin/controller"
fi

if [ -f "agent" ]; then
    cp agent /usr/local/bin/
    chmod +x /usr/local/bin/agent
    echo "✓ Agent已安装到 /usr/local/bin/agent"
fi

# 创建配置目录
mkdir -p /etc/remote-control
mkdir -p /var/log/remote-control

echo "安装完成！"
echo "使用方法:"
echo "  控制端: /usr/local/bin/controller --help"
echo "  受控端: /usr/local/bin/agent --help"
EOF

chmod +x $DIST_DIR/install.sh

# 创建打包脚本
cat > $DIST_DIR/package.sh << 'EOF'
#!/bin/bash
# 创建分发包

echo "创建Linux分发包..."
tar -czf ubuntu-remote-control-linux-amd64.tar.gz controller agent install.sh README.md
echo "✓ 分发包已创建: ubuntu-remote-control-linux-amd64.tar.gz"
EOF

chmod +x $DIST_DIR/package.sh

echo "=== Linux AMD64 构建完成 ==="
echo "生成的文件:"
echo "  - $LINUX_TARGET_DIR/controller (Linux控制端)"
echo "  - $LINUX_TARGET_DIR/agent (Linux受控端)"
echo "  - $DIST_DIR/ (Linux部署包)"
echo ""
echo "部署到Linux服务器:"
echo "  1. 复制 $DIST_DIR 目录到Linux服务器"
echo "  2. 在Linux服务器上运行: sudo ./install.sh"
echo "  3. 或者创建分发包: cd $DIST_DIR && ./package.sh"
echo ""
echo "验证二进制文件:"
echo "  file $LINUX_TARGET_DIR/controller"
echo "  file $LINUX_TARGET_DIR/agent"
echo ""
echo "注意: 这些二进制文件是为Linux AMD64构建的，无法在macOS上运行。"
