[workspace]
members = [
    "controller",
    "agent",
    "common"
]
resolver = "2"

[workspace.dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"
env_logger = "0.10"
anyhow = "1.0"
thiserror = "1.0"
clap = { version = "4.0", features = ["derive"] }
rustls = "0.21"
rustls-pemfile = "1.0"
base64 = "0.21"
sha2 = "0.10"
aes-gcm = "0.10"
rand = "0.8"
webpki-roots = "0.22"
rcgen = "0.11"
x509-parser = "0.15"
tempfile = "3.0"
libc = "0.2"
