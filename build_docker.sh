#!/bin/bash

# Docker 构建脚本 - 在 Linux 容器中构建项目
# 解决 macOS 交叉编译到 Linux 的问题


echo "✓ Docker 可用"

# 创建 Dockerfile
cat > Dockerfile.build << 'EOF'
FROM rust:1.87-slim

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 构建项目
RUN cargo build --release

# 创建输出目录
RUN mkdir -p /output && \
    cp target/release/controller /output/ 2>/dev/null || echo "Controller not built" && \
    cp target/release/agent /output/ 2>/dev/null || echo "Agent not built"

CMD ["ls", "-la", "/output"]
EOF

echo "创建 Docker 构建镜像..."
docker build -f Dockerfile.build -t ubuntu-remote-control-builder .

echo "在 Docker 容器中构建项目..."
docker run --rm -v "$(pwd)/dist-docker:/output" ubuntu-remote-control-builder sh -c "
    echo '开始构建...'
    cargo build --release
    echo '复制二进制文件...'
    mkdir -p /output
    if [ -f target/release/controller ]; then
        cp target/release/controller /output/
        echo '✓ Controller 已构建'
    fi
    if [ -f target/release/agent ]; then
        cp target/release/agent /output/
        echo '✓ Agent 已构建'
    fi
    echo '构建完成！'
    ls -la /output
"

# 清理 Dockerfile
rm -f Dockerfile.build

# 检查构建结果
if [ -d "dist-docker" ]; then
    echo ""
    echo "=== Docker 构建完成 ==="
    echo "生成的文件:"
    ls -la dist-docker/
    
    if [ -f "dist-docker/controller" ]; then
        echo "✓ Controller Linux 二进制文件: dist-docker/controller"
        file dist-docker/controller
    fi
    
    if [ -f "dist-docker/agent" ]; then
        echo "✓ Agent Linux 二进制文件: dist-docker/agent"
        file dist-docker/agent
    fi
    
    # 创建部署包
    echo ""
    echo "创建部署包..."
    mkdir -p dist-docker-deploy
    
    if [ -f "dist-docker/controller" ]; then
        cp dist-docker/controller dist-docker-deploy/
    fi
    
    if [ -f "dist-docker/agent" ]; then
        cp dist-docker/agent dist-docker-deploy/
    fi
    
    cp README.md dist-docker-deploy/ 2>/dev/null || echo "README.md not found"
    
    # 创建安装脚本
    cat > dist-docker-deploy/install.sh << 'INSTALL_EOF'
#!/bin/bash
# Ubuntu Remote Control Docker 构建版本安装脚本

echo "安装 Ubuntu Remote Control (Docker 构建版本)..."

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "x86_64" ]; then
    echo "错误: 当前架构是 $ARCH，此程序仅支持 x86_64"
    exit 1
fi

echo "✓ 架构检查通过: $ARCH"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本，或手动复制文件到目标目录"
    echo "手动安装命令:"
    echo "  cp controller ~/.local/bin/ (或其他PATH目录)"
    echo "  cp agent ~/.local/bin/"
    echo "  chmod +x ~/.local/bin/controller ~/.local/bin/agent"
    exit 1
fi

# 复制二进制文件
if [ -f "controller" ]; then
    cp controller /usr/local/bin/
    chmod +x /usr/local/bin/controller
    echo "✓ Controller已安装到 /usr/local/bin/controller"
fi

if [ -f "agent" ]; then
    cp agent /usr/local/bin/
    chmod +x /usr/local/bin/agent
    echo "✓ Agent已安装到 /usr/local/bin/agent"
fi

# 创建配置目录
mkdir -p /etc/remote-control
mkdir -p /var/log/remote-control

echo "安装完成！"
echo "使用方法:"
echo "  控制端: /usr/local/bin/controller --help"
echo "  受控端: /usr/local/bin/agent --help"
INSTALL_EOF

    chmod +x dist-docker-deploy/install.sh
    
    # 创建用户安装脚本
    cat > dist-docker-deploy/install_user.sh << 'USER_INSTALL_EOF'
#!/bin/bash
# Ubuntu Remote Control 用户安装脚本（无需root权限）

echo "安装 Ubuntu Remote Control 到用户目录..."

# 创建用户bin目录
mkdir -p ~/.local/bin

# 复制二进制文件
if [ -f "controller" ]; then
    cp controller ~/.local/bin/
    chmod +x ~/.local/bin/controller
    echo "✓ Controller已安装到 ~/.local/bin/controller"
fi

if [ -f "agent" ]; then
    cp agent ~/.local/bin/
    chmod +x ~/.local/bin/agent
    echo "✓ Agent已安装到 ~/.local/bin/agent"
fi

# 检查PATH
if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
    echo ""
    echo "注意: ~/.local/bin 不在您的PATH中"
    echo "请将以下行添加到 ~/.bashrc 或 ~/.zshrc:"
    echo "  export PATH=\"\$HOME/.local/bin:\$PATH\""
    echo "然后运行: source ~/.bashrc"
fi

echo ""
echo "安装完成！"
echo "使用方法:"
echo "  控制端: ~/.local/bin/controller --help"
echo "  受控端: ~/.local/bin/agent --help"
USER_INSTALL_EOF

    chmod +x dist-docker-deploy/install_user.sh
    
    echo ""
    echo "部署包已创建: dist-docker-deploy/"
    echo "包含文件:"
    ls -la dist-docker-deploy/
    
else
    echo "错误: 构建失败，未找到输出目录"
    exit 1
fi

echo ""
echo "=== Docker 构建总结 ==="
echo "✓ 使用 Docker 在 Linux 环境中构建成功"
echo "✓ 生成的二进制文件可在 Linux AMD64 系统上运行"
echo "✓ 部署包位于: dist-docker-deploy/"
echo ""
echo "部署到 Linux 服务器:"
echo "  1. 复制 dist-docker-deploy 目录到 Linux 服务器"
echo "  2. 系统安装: sudo ./install.sh"
echo "  3. 用户安装: ./install_user.sh"
