# Ubuntu服务器远程控制方案

基于Ubuntu22.04 LTS服务器的远控工具设计方案，采用控制端-受控端架构。

## 功能特性

- 远程命令执行
- 文件上传下载
- 进程管理
- TLS加密传输
- 身份认证
- 操作日志

## 架构设计

采用控制端-受控端架构，适用于直接连接的场景，简单高效，适合内网环境。

### 控制端模块
- 远程投递插件
- 命令与控制功能
- 内部侦察功能

### 受控端模块
- 命令与控制功能
- 执行功能（ELF文件加载）

## 项目结构

```
ubuntu-remote-control/
├── controller/          # 控制端
│   ├── src/
│   ├── tests/
│   └── Cargo.toml
├── agent/              # 受控端
│   ├── src/
│   ├── tests/
│   └── Cargo.toml
├── common/             # 共享组件
│   ├── src/
│   └── Cargo.toml
├── tests/              # 集成测试
├── docs/               # 文档
├── Cargo.toml          # 工作空间配置
└── README.md
```

## 构建与运行

```bash
# 构建所有组件
cargo build

# 运行测试
cargo test

# 运行控制端
cargo run --bin controller

# 运行受控端
cargo run --bin agent
```

## 安全说明

本项目仅用于合法的系统管理和运维目的，请确保在使用前获得适当的授权。
